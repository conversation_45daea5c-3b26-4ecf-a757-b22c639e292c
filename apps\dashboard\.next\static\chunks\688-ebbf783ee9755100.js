"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[688],{607:(e,t,r)=>{r.d(t,{QP:()=>Y});let n=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||l(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),i=n?o(e.slice(1),n):void 0;if(i)return i;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},i=/^\[(.+)\]$/,l=e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{s(r,n,e,t)}),n},s=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void s(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{s(o,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],i=t.length,l=e=>{let r,l=[],a=0,s=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===a){if(c===o&&(n||e.slice(u,u+i)===t)){l.push(e.slice(s,u)),s=u+i;continue}if("/"===c){r=u;continue}}"["===c?a++:"]"===c&&a--}let u=0===l.length?e:e.substring(s),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:l,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};return r?e=>r({className:e,parseClassName:l}):l},m=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},h=e=>({cache:f(e.cacheSize),parseClassName:p(e),...n(e)}),v=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,i=[],l=e.trim().split(v),a="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{modifiers:s,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,p=n(f?c.substring(0,d):c);if(!p){if(!f||!(p=n(c))){a=t+(a.length>0?" "+a:a);continue}f=!1}let h=m(s).join(":"),v=u?h+"!":h,g=v+p;if(i.includes(g))continue;i.push(g);let b=o(p,f);for(let e=0;e<b.length;++e){let t=b[e];i.push(v+t)}a=t+(a.length>0?" "+a:a)}return a};function b(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=y(e))&&(n&&(n+=" "),n+=t);return n}let y=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=y(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,E=/^\d+\/\d+$/,C=new Set(["px","full","screen"]),R=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,k=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,A=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,T=e=>j(e)||C.has(e)||E.test(e),P=e=>H(e,"length",$),j=e=>!!e&&!Number.isNaN(Number(e)),N=e=>H(e,"number",j),O=e=>!!e&&Number.isInteger(Number(e)),D=e=>e.endsWith("%")&&j(e.slice(0,-1)),L=e=>x.test(e),I=e=>R.test(e),_=new Set(["length","size","percentage"]),F=e=>H(e,_,U),z=e=>H(e,"position",U),W=new Set(["image","url"]),G=e=>H(e,W,X),B=e=>H(e,"",V),K=()=>!0,H=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},$=e=>k.test(e)&&!S.test(e),U=()=>!1,V=e=>M.test(e),X=e=>A.test(e);Symbol.toStringTag;let Y=function(e,...t){let r,n,o,i=function(a){return n=(r=h(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=l,l(a)};function l(e){let t=n(e);if(t)return t;let i=g(e,r);return o(e,i),i}return function(){return i(b.apply(null,arguments))}}(()=>{let e=w("colors"),t=w("spacing"),r=w("blur"),n=w("brightness"),o=w("borderColor"),i=w("borderRadius"),l=w("borderSpacing"),a=w("borderWidth"),s=w("contrast"),u=w("grayscale"),c=w("hueRotate"),d=w("invert"),f=w("gap"),p=w("gradientColorStops"),m=w("gradientColorStopPositions"),h=w("inset"),v=w("margin"),g=w("opacity"),b=w("padding"),y=w("saturate"),x=w("scale"),E=w("sepia"),C=w("skew"),R=w("space"),k=w("translate"),S=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],A=()=>["auto",L,t],_=()=>[L,t],W=()=>["",T,P],H=()=>["auto",j,L],$=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],U=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],X=()=>["start","end","center","between","around","evenly","stretch"],Y=()=>["","0",L],q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[j,L];return{cacheSize:500,separator:":",theme:{colors:[K],spacing:[T,P],blur:["none","",I,L],brightness:Z(),borderColor:[e],borderRadius:["none","","full",I,L],borderSpacing:_(),borderWidth:W(),contrast:Z(),grayscale:Y(),hueRotate:Z(),invert:Y(),gap:_(),gradientColorStops:[e],gradientColorStopPositions:[D,P],inset:A(),margin:A(),opacity:Z(),padding:_(),saturate:Z(),scale:Z(),sepia:Y(),skew:Z(),space:_(),translate:_()},classGroups:{aspect:[{aspect:["auto","square","video",L]}],container:["container"],columns:[{columns:[I]}],"break-after":[{"break-after":q()}],"break-before":[{"break-before":q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...$(),L]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:S()}],"overscroll-x":[{"overscroll-x":S()}],"overscroll-y":[{"overscroll-y":S()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",O,L]}],basis:[{basis:A()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",L]}],grow:[{grow:Y()}],shrink:[{shrink:Y()}],order:[{order:["first","last","none",O,L]}],"grid-cols":[{"grid-cols":[K]}],"col-start-end":[{col:["auto",{span:["full",O,L]},L]}],"col-start":[{"col-start":H()}],"col-end":[{"col-end":H()}],"grid-rows":[{"grid-rows":[K]}],"row-start-end":[{row:["auto",{span:[O,L]},L]}],"row-start":[{"row-start":H()}],"row-end":[{"row-end":H()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",L]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",L]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...X()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...X(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...X(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[R]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[R]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",L,t]}],"min-w":[{"min-w":[L,t,"min","max","fit"]}],"max-w":[{"max-w":[L,t,"none","full","min","max","fit","prose",{screen:[I]},I]}],h:[{h:[L,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[L,t,"auto","min","max","fit"]}],"font-size":[{text:["base",I,P]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",N]}],"font-family":[{font:[K]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",L]}],"line-clamp":[{"line-clamp":["none",j,N]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",T,L]}],"list-image":[{"list-image":["none",L]}],"list-style-type":[{list:["none","disc","decimal",L]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...U(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",T,P]}],"underline-offset":[{"underline-offset":["auto",T,L]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...$(),z]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",F]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},G]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...U(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:U()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...U()]}],"outline-offset":[{"outline-offset":[T,L]}],"outline-w":[{outline:[T,P]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[T,P]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",I,B]}],"shadow-color":[{shadow:[K]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",I,L]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[y]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",L]}],duration:[{duration:Z()}],ease:[{ease:["linear","in","out","in-out",L]}],delay:[{delay:Z()}],animate:[{animate:["none","spin","ping","pulse","bounce",L]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[O,L]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",L]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[T,P,N]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},615:(e,t,r)=>{r.d(t,{F:()=>l});var n=r(2987);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:a}=t,s=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let i=o(t)||o(n);return l[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,s,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...u}[t]):({...a,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},749:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>a});var n=r(7620),o=r(7509),i=r(9649),l=r(4568),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},1261:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8889).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},2987:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}},4762:(e,t,r)=>{r.d(t,{b:()=>a});var n=r(7620),o=r(749),i=r(4568),l=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},4931:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8889).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7167:(e,t,r)=>{r.d(t,{H_:()=>nZ,UC:()=>nV,YJ:()=>nX,q7:()=>nq,VF:()=>n0,JU:()=>nY,ZL:()=>nU,z6:()=>nJ,hN:()=>nQ,bL:()=>nH,wv:()=>n1,Pb:()=>n2,G5:()=>n9,ZP:()=>n6,l9:()=>n$});var n,o,i,l,a=r(7620),s=r.t(a,2);function u(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var c=r(9640),d=r(4568);function f(e,t=[]){let r=[],n=()=>{let t=r.map(e=>a.createContext(e));return function(r){let n=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let o=a.createContext(n),i=r.length;r=[...r,n];let l=t=>{let{scope:r,children:n,...l}=t,s=r?.[e]?.[i]||o,u=a.useMemo(()=>l,Object.values(l));return(0,d.jsx)(s.Provider,{value:u,children:n})};return l.displayName=t+"Provider",[l,function(r,l){let s=l?.[e]?.[i]||o,u=a.useContext(s);if(u)return u;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}var p=globalThis?.document?a.useLayoutEffect:()=>{},m=s[" useInsertionEffect ".trim().toString()]||p;function h({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,i,l]=function({defaultProp:e,onChange:t}){let[r,n]=a.useState(e),o=a.useRef(r),i=a.useRef(t);return m(()=>{i.current=t},[t]),a.useEffect(()=>{o.current!==r&&(i.current?.(r),o.current=r)},[r,o]),[r,n,i]}({defaultProp:t,onChange:r}),s=void 0!==e,u=s?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,n])}return[u,a.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else i(t)},[s,e,i,l])]}Symbol("RADIX:SYNC_STATE");var v=r(749);function g(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function b(e,t){var r=g(e,t,"get");return r.get?r.get.call(e):r.value}function y(e,t,r){var n=g(e,t,"set");if(n.set)n.set.call(e,r);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=r}return r}var w=r(9649);function x(e){let t=e+"CollectionProvider",[r,n]=f(t),[o,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,n=a.useRef(null),i=a.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:n,children:r})};l.displayName=t;let s=e+"CollectionSlot",u=(0,w.TL)(s),p=a.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=i(s,r),l=(0,c.s)(t,o.collectionRef);return(0,d.jsx)(u,{ref:l,children:n})});p.displayName=s;let m=e+"CollectionItemSlot",h="data-radix-collection-item",v=(0,w.TL)(m),g=a.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,l=a.useRef(null),s=(0,c.s)(t,l),u=i(m,r);return a.useEffect(()=>(u.itemMap.set(l,{ref:l,...o}),()=>void u.itemMap.delete(l))),(0,d.jsx)(v,{...{[h]:""},ref:s,children:n})});return g.displayName=m,[{Provider:l,Slot:p,ItemSlot:g},function(t){let r=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var E=new WeakMap;function C(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=R(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function R(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap;var k=a.createContext(void 0);function S(e){let t=a.useContext(k);return e||t||"ltr"}function M(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var A="dismissableLayer.update",T=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),P=a.forwardRef((e,t)=>{var r,n;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:f,onInteractOutside:p,onDismiss:m,...h}=e,g=a.useContext(T),[b,y]=a.useState(null),w=null!=(n=null==b?void 0:b.ownerDocument)?n:null==(r=globalThis)?void 0:r.document,[,x]=a.useState({}),E=(0,c.s)(t,e=>y(e)),C=Array.from(g.layers),[R]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),k=C.indexOf(R),S=b?C.indexOf(b):-1,P=g.layersWithOutsidePointerEventsDisabled.size>0,O=S>=k,D=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=M(e),o=a.useRef(!1),i=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){N("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,r=[...g.branches].some(e=>e.contains(t));O&&!r&&(null==s||s(e),null==p||p(e),e.defaultPrevented||null==m||m())},w),L=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=M(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&N("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...g.branches].some(e=>e.contains(t))&&(null==f||f(e),null==p||p(e),e.defaultPrevented||null==m||m())},w);return!function(e,t=globalThis?.document){let r=M(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{S===g.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))},w),a.useEffect(()=>{if(b)return o&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(i=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(b)),g.layers.add(b),j(),()=>{o&&1===g.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=i)}},[b,w,o,g]),a.useEffect(()=>()=>{b&&(g.layers.delete(b),g.layersWithOutsidePointerEventsDisabled.delete(b),j())},[b,g]),a.useEffect(()=>{let e=()=>x({});return document.addEventListener(A,e),()=>document.removeEventListener(A,e)},[]),(0,d.jsx)(v.sG.div,{...h,ref:E,style:{pointerEvents:P?O?"auto":"none":void 0,...e.style},onFocusCapture:u(e.onFocusCapture,L.onFocusCapture),onBlurCapture:u(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:u(e.onPointerDownCapture,D.onPointerDownCapture)})});function j(){let e=new CustomEvent(A);document.dispatchEvent(e)}function N(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,v.hO)(i,l):i.dispatchEvent(l)}P.displayName="DismissableLayer",a.forwardRef((e,t)=>{let r=a.useContext(T),n=a.useRef(null),o=(0,c.s)(t,n);return a.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,d.jsx)(v.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var O=0;function D(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var L="focusScope.autoFocusOnMount",I="focusScope.autoFocusOnUnmount",_={bubbles:!1,cancelable:!0},F=a.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[s,u]=a.useState(null),f=M(o),p=M(i),m=a.useRef(null),h=(0,c.s)(t,e=>u(e)),g=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(n){let e=function(e){if(g.paused||!s)return;let t=e.target;s.contains(t)?m.current=t:G(m.current,{select:!0})},t=function(e){if(g.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||G(m.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&G(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,g.paused]),a.useEffect(()=>{if(s){B.add(g);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(L,_);s.addEventListener(L,f),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(G(n,{select:t}),document.activeElement!==r)return}(z(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&G(s))}return()=>{s.removeEventListener(L,f),setTimeout(()=>{let t=new CustomEvent(I,_);s.addEventListener(I,p),s.dispatchEvent(t),t.defaultPrevented||G(null!=e?e:document.body,{select:!0}),s.removeEventListener(I,p),B.remove(g)},0)}}},[s,f,p,g]);let b=a.useCallback(e=>{if(!r&&!n||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,i]=function(e){let t=z(e);return[W(t,e),W(t.reverse(),e)]}(t);n&&i?e.shiftKey||o!==i?e.shiftKey&&o===n&&(e.preventDefault(),r&&G(i,{select:!0})):(e.preventDefault(),r&&G(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,g.paused]);return(0,d.jsx)(v.sG.div,{tabIndex:-1,...l,ref:h,onKeyDown:b})});function z(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function W(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function G(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}F.displayName="FocusScope";var B=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=K(e,t)).unshift(t)},remove(t){var r;null==(r=(e=K(e,t))[0])||r.resume()}}}();function K(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var H=s[" useId ".trim().toString()]||(()=>void 0),$=0;function U(e){let[t,r]=a.useState(H());return p(()=>{e||r(e=>e??String($++))},[e]),e||(t?`radix-${t}`:"")}let V=["top","right","bottom","left"],X=Math.min,Y=Math.max,q=Math.round,Z=Math.floor,J=e=>({x:e,y:e}),Q={left:"right",right:"left",bottom:"top",top:"bottom"},ee={start:"end",end:"start"};function et(e,t){return"function"==typeof e?e(t):e}function er(e){return e.split("-")[0]}function en(e){return e.split("-")[1]}function eo(e){return"x"===e?"y":"x"}function ei(e){return"y"===e?"height":"width"}function el(e){return["top","bottom"].includes(er(e))?"y":"x"}function ea(e){return e.replace(/start|end/g,e=>ee[e])}function es(e){return e.replace(/left|right|bottom|top/g,e=>Q[e])}function eu(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ec(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function ed(e,t,r){let n,{reference:o,floating:i}=e,l=el(t),a=eo(el(t)),s=ei(a),u=er(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(u){case"top":n={x:d,y:o.y-i.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-i.width,y:f};break;default:n={x:o.x,y:o.y}}switch(en(t)){case"start":n[a]-=p*(r&&c?-1:1);break;case"end":n[a]+=p*(r&&c?-1:1)}return n}let ef=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:l}=r,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=ed(u,n,s),f=n,p={},m=0;for(let r=0;r<a.length;r++){let{name:i,fn:h}=a[r],{x:v,y:g,data:b,reset:y}=await h({x:c,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...b}},y&&m<=50&&(m++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(u=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:d}=ed(u,f,s)),r=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function ep(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=et(t,e),m=eu(p),h=a[f?"floating"===d?"reference":"floating":d],v=ec(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(h)))||r?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),g="floating"===d?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),y=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},w=ec(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:b,strategy:s}):g);return{top:(v.top-w.top+m.top)/y.y,bottom:(w.bottom-v.bottom+m.bottom)/y.y,left:(v.left-w.left+m.left)/y.x,right:(w.right-v.right+m.right)/y.x}}function em(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function eh(e){return V.some(t=>e[t]>=0)}async function ev(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=er(r),a=en(r),s="y"===el(r),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,d=et(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),s?{x:p*c,y:f*u}:{x:f*u,y:p*c}}function eg(){return"undefined"!=typeof window}function eb(e){return ex(e)?(e.nodeName||"").toLowerCase():"#document"}function ey(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ew(e){var t;return null==(t=(ex(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ex(e){return!!eg()&&(e instanceof Node||e instanceof ey(e).Node)}function eE(e){return!!eg()&&(e instanceof Element||e instanceof ey(e).Element)}function eC(e){return!!eg()&&(e instanceof HTMLElement||e instanceof ey(e).HTMLElement)}function eR(e){return!!eg()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ey(e).ShadowRoot)}function ek(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=eP(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function eS(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eM(e){let t=eA(),r=eE(e)?eP(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function eA(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eT(e){return["html","body","#document"].includes(eb(e))}function eP(e){return ey(e).getComputedStyle(e)}function ej(e){return eE(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eN(e){if("html"===eb(e))return e;let t=e.assignedSlot||e.parentNode||eR(e)&&e.host||ew(e);return eR(t)?t.host:t}function eO(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=eN(t);return eT(r)?t.ownerDocument?t.ownerDocument.body:t.body:eC(r)&&ek(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),l=ey(o);if(i){let e=eD(l);return t.concat(l,l.visualViewport||[],ek(o)?o:[],e&&r?eO(e):[])}return t.concat(o,eO(o,[],r))}function eD(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eL(e){let t=eP(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=eC(e),i=o?e.offsetWidth:r,l=o?e.offsetHeight:n,a=q(r)!==i||q(n)!==l;return a&&(r=i,n=l),{width:r,height:n,$:a}}function eI(e){return eE(e)?e:e.contextElement}function e_(e){let t=eI(e);if(!eC(t))return J(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=eL(t),l=(i?q(r.width):r.width)/n,a=(i?q(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eF=J(0);function ez(e){let t=ey(e);return eA()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eF}function eW(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),l=eI(e),a=J(1);t&&(n?eE(n)&&(a=e_(n)):a=e_(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===ey(l))&&o)?ez(l):J(0),u=(i.left+s.x)/a.x,c=(i.top+s.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=ey(l),t=n&&eE(n)?ey(n):n,r=e,o=eD(r);for(;o&&n&&t!==r;){let e=e_(o),t=o.getBoundingClientRect(),n=eP(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=i,c+=l,o=eD(r=ey(o))}}return ec({width:d,height:f,x:u,y:c})}function eG(e,t){let r=ej(e).scrollLeft;return t?t.left+r:eW(ew(e)).left+r}function eB(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:eG(e,n)),y:n.top+t.scrollTop}}function eK(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=ey(e),n=ew(e),o=r.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=eA();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,r);else if("document"===t)n=function(e){let t=ew(e),r=ej(e),n=e.ownerDocument.body,o=Y(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=Y(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),l=-r.scrollLeft+eG(e),a=-r.scrollTop;return"rtl"===eP(n).direction&&(l+=Y(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:l,y:a}}(ew(e));else if(eE(t))n=function(e,t){let r=eW(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=eC(e)?e_(e):J(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:n*i.y}}(t,r);else{let r=ez(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return ec(n)}function eH(e){return"static"===eP(e).position}function e$(e,t){if(!eC(e)||"fixed"===eP(e).position)return null;if(t)return t(e);let r=e.offsetParent;return ew(e)===r&&(r=r.ownerDocument.body),r}function eU(e,t){let r=ey(e);if(eS(e))return r;if(!eC(e)){let t=eN(e);for(;t&&!eT(t);){if(eE(t)&&!eH(t))return t;t=eN(t)}return r}let n=e$(e,t);for(;n&&["table","td","th"].includes(eb(n))&&eH(n);)n=e$(n,t);return n&&eT(n)&&eH(n)&&!eM(n)?r:n||function(e){let t=eN(e);for(;eC(t)&&!eT(t);){if(eM(t))return t;if(eS(t))break;t=eN(t)}return null}(e)||r}let eV=async function(e){let t=this.getOffsetParent||eU,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=eC(t),o=ew(t),i="fixed"===r,l=eW(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=J(0);if(n||!n&&!i)if(("body"!==eb(t)||ek(o))&&(a=ej(t)),n){let e=eW(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eG(o));i&&!n&&o&&(s.x=eG(o));let u=!o||n||i?J(0):eB(o,a);return{x:l.left+a.scrollLeft-s.x-u.x,y:l.top+a.scrollTop-s.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eX={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,l=ew(n),a=!!t&&eS(t.floating);if(n===l||a&&i)return r;let s={scrollLeft:0,scrollTop:0},u=J(1),c=J(0),d=eC(n);if((d||!d&&!i)&&(("body"!==eb(n)||ek(l))&&(s=ej(n)),eC(n))){let e=eW(n);u=e_(n),c.x=e.x+n.clientLeft,c.y=e.y+n.clientTop}let f=!l||d||i?J(0):eB(l,s,!0);return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-s.scrollLeft*u.x+c.x+f.x,y:r.y*u.y-s.scrollTop*u.y+c.y+f.y}},getDocumentElement:ew,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?eS(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=eO(e,[],!1).filter(e=>eE(e)&&"body"!==eb(e)),o=null,i="fixed"===eP(e).position,l=i?eN(e):e;for(;eE(l)&&!eT(l);){let t=eP(l),r=eM(l);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||ek(l)&&!r&&function e(t,r){let n=eN(t);return!(n===r||!eE(n)||eT(n))&&("fixed"===eP(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):o=t,l=eN(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=i[0],a=i.reduce((e,r)=>{let n=eK(t,r,o);return e.top=Y(n.top,e.top),e.right=X(n.right,e.right),e.bottom=X(n.bottom,e.bottom),e.left=Y(n.left,e.left),e},eK(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eU,getElementRects:eV,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=eL(e);return{width:t,height:r}},getScale:e_,isElement:eE,isRTL:function(e){return"rtl"===eP(e).direction}};function eY(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eq=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=et(e,t)||{};if(null==u)return{};let d=eu(c),f={x:r,y:n},p=eo(el(o)),m=ei(p),h=await l.getDimensions(u),v="y"===p,g=v?"clientHeight":"clientWidth",b=i.reference[m]+i.reference[p]-f[p]-i.floating[m],y=f[p]-i.reference[p],w=await (null==l.getOffsetParent?void 0:l.getOffsetParent(u)),x=w?w[g]:0;x&&await (null==l.isElement?void 0:l.isElement(w))||(x=a.floating[g]||i.floating[m]);let E=x/2-h[m]/2-1,C=X(d[v?"top":"left"],E),R=X(d[v?"bottom":"right"],E),k=x-h[m]-R,S=x/2-h[m]/2+(b/2-y/2),M=Y(C,X(S,k)),A=!s.arrow&&null!=en(o)&&S!==M&&i.reference[m]/2-(S<C?C:R)-h[m]/2<0,T=A?S<C?S-C:S-k:0;return{[p]:f[p]+T,data:{[p]:M,centerOffset:S-M-T,...A&&{alignmentOffset:T}},reset:A}}}),eZ=(e,t,r)=>{let n=new Map,o={platform:eX,...r},i={...o.platform,_c:n};return ef(e,t,{...o,platform:i})};var eJ=r(7509),eQ="undefined"!=typeof document?a.useLayoutEffect:function(){};function e0(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!e0(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!e0(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function e1(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e2(e,t){let r=e1(e);return Math.round(t*r)/r}function e6(e){let t=a.useRef(e);return eQ(()=>{t.current=e}),t}let e9=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?eq({element:r.current,padding:n}).fn(t):{}:r?eq({element:r,padding:n}).fn(t):{}}}),e5=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await ev(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),e8=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=et(e,t),u={x:r,y:n},c=await ep(t,s),d=el(er(o)),f=eo(d),p=u[f],m=u[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+c[e],n=p-c[t];p=Y(r,X(p,n))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=m+c[e],n=m-c[t];m=Y(r,X(m,n))}let h=a.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-r,y:h.y-n,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),e4=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=et(e,t),c={x:r,y:n},d=el(o),f=eo(d),p=c[f],m=c[d],h=et(a,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,r=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>r&&(p=r)}if(u){var g,b;let e="y"===f?"width":"height",t=["top","left"].includes(er(o)),r=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),n=i.reference[d]+i.reference[e]+(t?0:(null==(b=l.offset)?void 0:b[d])||0)-(t?v.crossAxis:0);m<r?m=r:m>n&&(m=n)}return{[f]:p,[d]:m}}}}(e),options:[e,t]}),e7=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:b=!0,...y}=et(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let w=er(a),x=el(c),E=er(c)===c,C=await (null==d.isRTL?void 0:d.isRTL(f.floating)),R=h||(E||!b?[es(c)]:function(e){let t=es(e);return[ea(e),t,ea(t)]}(c)),k="none"!==g;!h&&k&&R.push(...function(e,t,r,n){let o=en(e),i=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(er(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(ea)))),i}(c,b,g,C));let S=[c,...R],M=await ep(t,y),A=[],T=(null==(n=s.flip)?void 0:n.overflows)||[];if(p&&A.push(M[w]),m){let e=function(e,t,r){void 0===r&&(r=!1);let n=en(e),o=eo(el(e)),i=ei(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=es(l)),[l,es(l)]}(a,u,C);A.push(M[e[0]],M[e[1]])}if(T=[...T,{placement:a,overflows:A}],!A.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=S[e];if(t&&("alignment"!==m||x===el(t)||T.every(e=>e.overflows[0]>0&&el(e.placement)===x)))return{data:{index:e,overflows:T},reset:{placement:t}};let r=null==(i=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(v){case"bestFit":{let e=null==(l=T.filter(e=>{if(k){let t=el(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=c}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),e3=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i,{placement:l,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...d}=et(e,t),f=await ep(t,d),p=er(l),m=en(l),h="y"===el(l),{width:v,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=m===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=p,o="end"===m?"top":"bottom");let b=g-f.top-f.bottom,y=v-f.left-f.right,w=X(g-f[o],b),x=X(v-f[i],y),E=!t.middlewareData.shift,C=w,R=x;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(R=y),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(C=b),E&&!m){let e=Y(f.left,0),t=Y(f.right,0),r=Y(f.top,0),n=Y(f.bottom,0);h?R=v-2*(0!==e||0!==t?e+t:Y(f.left,f.right)):C=g-2*(0!==r||0!==n?r+n:Y(f.top,f.bottom))}await c({...t,availableWidth:R,availableHeight:C});let k=await s.getDimensions(u.floating);return v!==k.width||g!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),te=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=et(e,t);switch(n){case"referenceHidden":{let e=em(await ep(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:eh(e)}}}case"escaped":{let e=em(await ep(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:eh(e)}}}default:return{}}}}}(e),options:[e,t]}),tt=(e,t)=>({...e9(e),options:[e,t]});var tr=a.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,d.jsx)(v.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tr.displayName="Arrow";var tn="Popper",[to,ti]=f(tn),[tl,ta]=to(tn),ts=e=>{let{__scopePopper:t,children:r}=e,[n,o]=a.useState(null);return(0,d.jsx)(tl,{scope:t,anchor:n,onAnchorChange:o,children:r})};ts.displayName=tn;var tu="PopperAnchor",tc=a.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,i=ta(tu,r),l=a.useRef(null),s=(0,c.s)(t,l);return a.useEffect(()=>{i.onAnchorChange((null==n?void 0:n.current)||l.current)}),n?null:(0,d.jsx)(v.sG.div,{...o,ref:s})});tc.displayName=tu;var td="PopperContent",[tf,tp]=to(td),tm=a.forwardRef((e,t)=>{var r,n,o,i,l,s,u,f;let{__scopePopper:m,side:h="bottom",sideOffset:g=0,align:b="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:E=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:k=!1,updatePositionStrategy:S="optimized",onPlaced:A,...T}=e,P=ta(td,m),[j,N]=a.useState(null),O=(0,c.s)(t,e=>N(e)),[D,L]=a.useState(null),I=function(e){let[t,r]=a.useState(void 0);return p(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(D),_=null!=(u=null==I?void 0:I.width)?u:0,F=null!=(f=null==I?void 0:I.height)?f:0,z="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},W=Array.isArray(E)?E:[E],G=W.length>0,B={padding:z,boundary:W.filter(tb),altBoundary:G},{refs:K,floatingStyles:H,placement:$,isPositioned:U,middlewareData:V}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:o,elements:{reference:i,floating:l}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=a.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=a.useState(n);e0(p,n)||m(n);let[h,v]=a.useState(null),[g,b]=a.useState(null),y=a.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),w=a.useCallback(e=>{e!==R.current&&(R.current=e,b(e))},[]),x=i||h,E=l||g,C=a.useRef(null),R=a.useRef(null),k=a.useRef(d),S=null!=u,M=e6(u),A=e6(o),T=e6(c),P=a.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:r,middleware:p};A.current&&(e.platform=A.current),eZ(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};j.current&&!e0(k.current,t)&&(k.current=t,eJ.flushSync(()=>{f(t)}))})},[p,t,r,A,T]);eQ(()=>{!1===c&&k.current.isPositioned&&(k.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let j=a.useRef(!1);eQ(()=>(j.current=!0,()=>{j.current=!1}),[]),eQ(()=>{if(x&&(C.current=x),E&&(R.current=E),x&&E){if(M.current)return M.current(x,E,P);P()}},[x,E,P,M,S]);let N=a.useMemo(()=>({reference:C,floating:R,setReference:y,setFloating:w}),[y,w]),O=a.useMemo(()=>({reference:x,floating:E}),[x,E]),D=a.useMemo(()=>{let e={position:r,left:0,top:0};if(!O.floating)return e;let t=e2(O.floating,d.x),n=e2(O.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...e1(O.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,O.floating,d.x,d.y]);return a.useMemo(()=>({...d,update:P,refs:N,elements:O,floatingStyles:D}),[d,P,N,O,D])}({strategy:"fixed",placement:h+("center"!==b?"-"+b:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=n,c=eI(e),d=i||l?[...c?eO(c):[],...eO(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),l&&e.addEventListener("resize",r)});let f=c&&s?function(e,t){let r,n=null,o=ew(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=u;if(a||t(),!f||!p)return;let m=Z(d),h=Z(o.clientWidth-(c+f)),v={rootMargin:-m+"px "+-h+"px "+-Z(o.clientHeight-(d+p))+"px "+-Z(c)+"px",threshold:Y(0,X(1,s))||1},g=!0;function b(t){let n=t[0].intersectionRatio;if(n!==s){if(!g)return l();n?l(!1,n):r=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==n||eY(u,e.getBoundingClientRect())||l(),g=!1}try{n=new IntersectionObserver(b,{...v,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(b,v)}n.observe(e)}(!0),i}(c,r):null,p=-1,m=null;a&&(m=new ResizeObserver(e=>{let[n]=e;n&&n.target===c&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),r()}),c&&!u&&m.observe(c),m.observe(t));let h=u?eW(e):null;return u&&function t(){let n=eW(e);h&&!eY(h,n)&&r(),h=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",r),l&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=m)||e.disconnect(),m=null,u&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===S})},elements:{reference:P.anchor},middleware:[e5({mainAxis:g+F,alignmentAxis:y}),x&&e8({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?e4():void 0,...B}),x&&e7({...B}),e3({...B,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:l}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&tt({element:D,padding:w}),ty({arrowWidth:_,arrowHeight:F}),k&&te({strategy:"referenceHidden",...B})]}),[q,J]=tw($),Q=M(A);p(()=>{U&&(null==Q||Q())},[U,Q]);let ee=null==(r=V.arrow)?void 0:r.x,et=null==(n=V.arrow)?void 0:n.y,er=(null==(o=V.arrow)?void 0:o.centerOffset)!==0,[en,eo]=a.useState();return p(()=>{j&&eo(window.getComputedStyle(j).zIndex)},[j]),(0,d.jsx)("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:U?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(i=V.transformOrigin)?void 0:i.x,null==(l=V.transformOrigin)?void 0:l.y].join(" "),...(null==(s=V.hide)?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(tf,{scope:m,placedSide:q,onArrowChange:L,arrowX:ee,arrowY:et,shouldHideArrow:er,children:(0,d.jsx)(v.sG.div,{"data-side":q,"data-align":J,...T,ref:O,style:{...T.style,animation:U?void 0:"none"}})})})});tm.displayName=td;var th="PopperArrow",tv={top:"bottom",right:"left",bottom:"top",left:"right"},tg=a.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=tp(th,r),i=tv[o.placedSide];return(0,d.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(tr,{...n,ref:t,style:{...n.style,display:"block"}})})});function tb(e){return null!==e}tg.displayName=th;var ty=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(r=u.arrow)?void 0:r.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,m]=tw(a),h={start:"0%",center:"50%",end:"100%"}[m],v=(null!=(i=null==(n=u.arrow)?void 0:n.x)?i:0)+d/2,g=(null!=(l=null==(o=u.arrow)?void 0:o.y)?l:0)+f/2,b="",y="";return"bottom"===p?(b=c?h:"".concat(v,"px"),y="".concat(-f,"px")):"top"===p?(b=c?h:"".concat(v,"px"),y="".concat(s.floating.height+f,"px")):"right"===p?(b="".concat(-f,"px"),y=c?h:"".concat(g,"px")):"left"===p&&(b="".concat(s.floating.width+f,"px"),y=c?h:"".concat(g,"px")),{data:{x:b,y}}}});function tw(e){let[t,r="center"]=e.split("-");return[t,r]}var tx=a.forwardRef((e,t)=>{var r,n;let{container:o,...i}=e,[l,s]=a.useState(!1);p(()=>s(!0),[]);let u=o||l&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return u?eJ.createPortal((0,d.jsx)(v.sG.div,{...i,ref:t}),u):null});tx.displayName="Portal";var tE=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,o]=a.useState(),i=a.useRef(null),l=a.useRef(e),s=a.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return a.useEffect(()=>{let e=tC(i.current);s.current="mounted"===u?e:"none"},[u]),p(()=>{let t=i.current,r=l.current;if(r!==e){let n=s.current,o=tC(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),l.current=e}},[e,c]),p(()=>{if(n){var e;let t,r=null!=(e=n.ownerDocument.defaultView)?e:window,o=e=>{let o=tC(i.current).includes(e.animationName);if(e.target===n&&o&&(c("ANIMATION_END"),!l.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},a=e=>{e.target===n&&(s.current=tC(i.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",o),n.addEventListener("animationend",o),()=>{r.clearTimeout(t),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",o),n.removeEventListener("animationend",o)}}c("ANIMATION_END")},[n,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof r?r({present:n.isPresent}):a.Children.only(r),i=(0,c.s)(n.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof r||n.isPresent?a.cloneElement(o,{ref:i}):null};function tC(e){return(null==e?void 0:e.animationName)||"none"}tE.displayName="Presence";var tR="rovingFocusGroup.onEntryFocus",tk={bubbles:!1,cancelable:!0},tS="RovingFocusGroup",[tM,tA,tT]=x(tS),[tP,tj]=f(tS,[tT]),[tN,tO]=tP(tS),tD=a.forwardRef((e,t)=>(0,d.jsx)(tM.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(tM.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(tL,{...e,ref:t})})}));tD.displayName=tS;var tL=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:o=!1,dir:i,currentTabStopId:l,defaultCurrentTabStopId:s,onCurrentTabStopIdChange:f,onEntryFocus:p,preventScrollOnEntryFocus:m=!1,...g}=e,b=a.useRef(null),y=(0,c.s)(t,b),w=S(i),[x,E]=h({prop:l,defaultProp:null!=s?s:null,onChange:f,caller:tS}),[C,R]=a.useState(!1),k=M(p),A=tA(r),T=a.useRef(!1),[P,j]=a.useState(0);return a.useEffect(()=>{let e=b.current;if(e)return e.addEventListener(tR,k),()=>e.removeEventListener(tR,k)},[k]),(0,d.jsx)(tN,{scope:r,orientation:n,dir:w,loop:o,currentTabStopId:x,onItemFocus:a.useCallback(e=>E(e),[E]),onItemShiftTab:a.useCallback(()=>R(!0),[]),onFocusableItemAdd:a.useCallback(()=>j(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>j(e=>e-1),[]),children:(0,d.jsx)(v.sG.div,{tabIndex:C||0===P?-1:0,"data-orientation":n,...g,ref:y,style:{outline:"none",...e.style},onMouseDown:u(e.onMouseDown,()=>{T.current=!0}),onFocus:u(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!C){let t=new CustomEvent(tR,tk);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=A().filter(e=>e.focusable);tz([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),m)}}T.current=!1}),onBlur:u(e.onBlur,()=>R(!1))})})}),tI="RovingFocusGroupItem",t_=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:o=!1,tabStopId:i,children:l,...s}=e,c=U(),f=i||c,p=tO(tI,r),m=p.currentTabStopId===f,h=tA(r),{onFocusableItemAdd:g,onFocusableItemRemove:b,currentTabStopId:y}=p;return a.useEffect(()=>{if(n)return g(),()=>b()},[n,g,b]),(0,d.jsx)(tM.ItemSlot,{scope:r,id:f,focusable:n,active:o,children:(0,d.jsx)(v.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...s,ref:t,onMouseDown:u(e.onMouseDown,e=>{n?p.onItemFocus(f):e.preventDefault()}),onFocus:u(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:u(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tF[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>tz(r))}}),children:"function"==typeof l?l({isCurrentTabStop:m,hasTabStop:null!=y}):l})})});t_.displayName=tI;var tF={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tz(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var tW=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tG=new WeakMap,tB=new WeakMap,tK={},tH=0,t$=function(e){return e&&(e.host||t$(e.parentNode))},tU=function(e,t,r,n){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=t$(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tK[r]||(tK[r]=new WeakMap);var i=tK[r],l=[],a=new Set,s=new Set(o),u=function(e){!e||a.has(e)||(a.add(e),u(e.parentNode))};o.forEach(u);var c=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,s=(tG.get(e)||0)+1,u=(i.get(e)||0)+1;tG.set(e,s),i.set(e,u),l.push(e),1===s&&o&&tB.set(e,!0),1===u&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),tH++,function(){l.forEach(function(e){var t=tG.get(e)-1,o=i.get(e)-1;tG.set(e,t),i.set(e,o),t||(tB.has(e)||e.removeAttribute(n),tB.delete(e)),o||e.removeAttribute(r)}),--tH||(tG=new WeakMap,tG=new WeakMap,tB=new WeakMap,tK={})}},tV=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||tW(e);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),tU(n,o,r,"aria-hidden")):function(){return null}},tX=function(){return(tX=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tY(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var tq=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tZ="width-before-scroll-bar";function tJ(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tQ="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,t0=new WeakMap;function t1(e){return e}var t2=function(e){void 0===e&&(e={});var t,r,n,o,i=(t=null,void 0===r&&(r=t1),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var i=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return i.options=tX({async:!0,ssr:!1},e),i}(),t6=function(){},t9=a.forwardRef(function(e,t){var r,n,o,i,l=a.useRef(null),s=a.useState({onScrollCapture:t6,onWheelCapture:t6,onTouchMoveCapture:t6}),u=s[0],c=s[1],d=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,h=e.enabled,v=e.shards,g=e.sideCar,b=e.noRelative,y=e.noIsolation,w=e.inert,x=e.allowPinchZoom,E=e.as,C=e.gapMode,R=tY(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(r=[l,t],n=function(e){return r.forEach(function(t){return tJ(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,i=o.facade,tQ(function(){var e=t0.get(i);if(e){var t=new Set(e),n=new Set(r),o=i.current;t.forEach(function(e){n.has(e)||tJ(e,null)}),n.forEach(function(e){t.has(e)||tJ(e,o)})}t0.set(i,r)},[r]),i),S=tX(tX({},R),u);return a.createElement(a.Fragment,null,h&&a.createElement(g,{sideCar:t2,removeScrollBar:m,shards:v,noRelative:b,noIsolation:y,inert:w,setCallbacks:c,allowPinchZoom:!!x,lockRef:l,gapMode:C}),d?a.cloneElement(a.Children.only(f),tX(tX({},S),{ref:k})):a.createElement(void 0===E?"div":E,tX({},S,{className:p,ref:k}),f))});t9.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t9.classNames={fullWidth:tZ,zeroRight:tq};var t5=function(e){var t=e.sideCar,r=tY(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return a.createElement(n,tX({},r))};t5.isSideCarExport=!0;var t8=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=l||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t4=function(){var e=t8();return function(t,r){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},t7=function(){var e=t4();return function(t){return e(t.styles,t.dynamic),null}},t3={left:0,top:0,right:0,gap:0},re=function(e){return parseInt(e||"",10)||0},rt=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[re(r),re(n),re(o)]},rr=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t3;var t=rt(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},rn=t7(),ro="data-scroll-locked",ri=function(e,t,r,n){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(a,"px ").concat(n,";\n  }\n  body[").concat(ro,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(a,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tq," {\n    right: ").concat(a,"px ").concat(n,";\n  }\n  \n  .").concat(tZ," {\n    margin-right: ").concat(a,"px ").concat(n,";\n  }\n  \n  .").concat(tq," .").concat(tq," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(tZ," .").concat(tZ," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(ro,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},rl=function(){var e=parseInt(document.body.getAttribute(ro)||"0",10);return isFinite(e)?e:0},ra=function(){a.useEffect(function(){return document.body.setAttribute(ro,(rl()+1).toString()),function(){var e=rl()-1;e<=0?document.body.removeAttribute(ro):document.body.setAttribute(ro,e.toString())}},[])},rs=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;ra();var i=a.useMemo(function(){return rr(o)},[o]);return a.createElement(rn,{styles:ri(i,!t,o,r?"":"!important")})},ru=!1;if("undefined"!=typeof window)try{var rc=Object.defineProperty({},"passive",{get:function(){return ru=!0,!0}});window.addEventListener("test",rc,rc),window.removeEventListener("test",rc,rc)}catch(e){ru=!1}var rd=!!ru&&{passive:!1},rf=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},rp=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),rm(e,n)){var o=rh(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},rm=function(e,t){return"v"===e?rf(t,"overflowY"):rf(t,"overflowX")},rh=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},rv=function(e,t,r,n,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*n,s=r.target,u=t.contains(s),c=!1,d=a>0,f=0,p=0;do{if(!s)break;var m=rh(e,s),h=m[0],v=m[1]-m[2]-l*h;(h||v)&&rm(e,s)&&(f+=v,p+=h);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},rg=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},rb=function(e){return[e.deltaX,e.deltaY]},ry=function(e){return e&&"current"in e?e.current:e},rw=0,rx=[];let rE=(n=function(e){var t=a.useRef([]),r=a.useRef([0,0]),n=a.useRef(),o=a.useState(rw++)[0],i=a.useState(t7)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ry),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=rg(e),a=r.current,s="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=rp(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=rp(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||u)&&(n.current=o),!o)return!0;var p=n.current||o;return rv(p,t,e,"h"===p?s:u,!0)},[]),u=a.useCallback(function(e){if(rx.length&&rx[rx.length-1]===i){var r="deltaY"in e?rb(e):rg(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(l.current.shards||[]).map(ry).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){r.current=rg(e),n.current=void 0},[]),f=a.useCallback(function(t){c(t.type,rb(t),t.target,s(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,rg(t),t.target,s(t,e.lockRef.current))},[]);a.useEffect(function(){return rx.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,rd),document.addEventListener("touchmove",u,rd),document.addEventListener("touchstart",d,rd),function(){rx=rx.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,rd),document.removeEventListener("touchmove",u,rd),document.removeEventListener("touchstart",d,rd)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(rs,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},t2.useMedium(n),t5);var rC=a.forwardRef(function(e,t){return a.createElement(t9,tX({},e,{ref:t,sideCar:rE}))});rC.classNames=t9.classNames;var rR=["Enter"," "],rk=["ArrowUp","PageDown","End"],rS=["ArrowDown","PageUp","Home",...rk],rM={ltr:[...rR,"ArrowRight"],rtl:[...rR,"ArrowLeft"]},rA={ltr:["ArrowLeft"],rtl:["ArrowRight"]},rT="Menu",[rP,rj,rN]=x(rT),[rO,rD]=f(rT,[rN,ti,tj]),rL=ti(),rI=tj(),[r_,rF]=rO(rT),[rz,rW]=rO(rT),rG=e=>{let{__scopeMenu:t,open:r=!1,children:n,dir:o,onOpenChange:i,modal:l=!0}=e,s=rL(t),[u,c]=a.useState(null),f=a.useRef(!1),p=M(i),m=S(o);return a.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,d.jsx)(ts,{...s,children:(0,d.jsx)(r_,{scope:t,open:r,onOpenChange:p,content:u,onContentChange:c,children:(0,d.jsx)(rz,{scope:t,onClose:a.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:n})})})};rG.displayName=rT;var rB=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=rL(r);return(0,d.jsx)(tc,{...o,...n,ref:t})});rB.displayName="MenuAnchor";var rK="MenuPortal",[rH,r$]=rO(rK,{forceMount:void 0}),rU=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,i=rF(rK,t);return(0,d.jsx)(rH,{scope:t,forceMount:r,children:(0,d.jsx)(tE,{present:r||i.open,children:(0,d.jsx)(tx,{asChild:!0,container:o,children:n})})})};rU.displayName=rK;var rV="MenuContent",[rX,rY]=rO(rV),rq=a.forwardRef((e,t)=>{let r=r$(rV,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=rF(rV,e.__scopeMenu),l=rW(rV,e.__scopeMenu);return(0,d.jsx)(rP.Provider,{scope:e.__scopeMenu,children:(0,d.jsx)(tE,{present:n||i.open,children:(0,d.jsx)(rP.Slot,{scope:e.__scopeMenu,children:l.modal?(0,d.jsx)(rZ,{...o,ref:t}):(0,d.jsx)(rJ,{...o,ref:t})})})})}),rZ=a.forwardRef((e,t)=>{let r=rF(rV,e.__scopeMenu),n=a.useRef(null),o=(0,c.s)(t,n);return a.useEffect(()=>{let e=n.current;if(e)return tV(e)},[]),(0,d.jsx)(r0,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:u(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),rJ=a.forwardRef((e,t)=>{let r=rF(rV,e.__scopeMenu);return(0,d.jsx)(r0,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),rQ=(0,w.TL)("MenuContent.ScrollLock"),r0=a.forwardRef((e,t)=>{let{__scopeMenu:r,loop:n=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,disableOutsidePointerEvents:s,onEntryFocus:f,onEscapeKeyDown:p,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:v,onDismiss:g,disableOutsideScroll:b,...y}=e,w=rF(rV,r),x=rW(rV,r),E=rL(r),C=rI(r),R=rj(r),[k,S]=a.useState(null),M=a.useRef(null),A=(0,c.s)(t,M,w.onContentChange),T=a.useRef(0),j=a.useRef(""),N=a.useRef(0),L=a.useRef(null),I=a.useRef("right"),_=a.useRef(0),z=b?rC:a.Fragment,W=e=>{var t,r;let n=j.current+e,o=R().filter(e=>!e.disabled),i=document.activeElement,l=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,l=(n=Math.max(i,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(l=l.filter(e=>e!==r));let a=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==r?a:void 0}(o.map(e=>e.textValue),n,l),s=null==(r=o.find(e=>e.textValue===a))?void 0:r.ref.current;!function e(t){j.current=t,window.clearTimeout(T.current),""!==t&&(T.current=window.setTimeout(()=>e(""),1e3))}(n),s&&setTimeout(()=>s.focus())};a.useEffect(()=>()=>window.clearTimeout(T.current),[]),a.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:D()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:D()),O++,()=>{1===O&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),O--}},[]);let G=a.useCallback(e=>{var t,r;return I.current===(null==(t=L.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(r=L.current)?void 0:r.area)},[]);return(0,d.jsx)(rX,{scope:r,searchRef:j,onItemEnter:a.useCallback(e=>{G(e)&&e.preventDefault()},[G]),onItemLeave:a.useCallback(e=>{var t;G(e)||(null==(t=M.current)||t.focus(),S(null))},[G]),onTriggerLeave:a.useCallback(e=>{G(e)&&e.preventDefault()},[G]),pointerGraceTimerRef:N,onPointerGraceIntentChange:a.useCallback(e=>{L.current=e},[]),children:(0,d.jsx)(z,{...b?{as:rQ,allowPinchZoom:!0}:void 0,children:(0,d.jsx)(F,{asChild:!0,trapped:o,onMountAutoFocus:u(i,e=>{var t;e.preventDefault(),null==(t=M.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:(0,d.jsx)(P,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:p,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:v,onDismiss:g,children:(0,d.jsx)(tD,{asChild:!0,...C,dir:x.dir,orientation:"vertical",loop:n,currentTabStopId:k,onCurrentTabStopIdChange:S,onEntryFocus:u(f,e=>{x.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,d.jsx)(tm,{role:"menu","aria-orientation":"vertical","data-state":nb(w.open),"data-radix-menu-content":"",dir:x.dir,...E,...y,ref:A,style:{outline:"none",...y.style},onKeyDown:u(y.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&W(e.key));let o=M.current;if(e.target!==o||!rS.includes(e.key))return;e.preventDefault();let i=R().filter(e=>!e.disabled).map(e=>e.ref.current);rk.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(i)}),onBlur:u(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(T.current),j.current="")}),onPointerMove:u(e.onPointerMove,nx(e=>{let t=e.target,r=_.current!==e.clientX;e.currentTarget.contains(t)&&r&&(I.current=e.clientX>_.current?"right":"left",_.current=e.clientX)}))})})})})})})});rq.displayName=rV;var r1=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,d.jsx)(v.sG.div,{role:"group",...n,ref:t})});r1.displayName="MenuGroup";var r2=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,d.jsx)(v.sG.div,{...n,ref:t})});r2.displayName="MenuLabel";var r6="MenuItem",r9="menu.itemSelect",r5=a.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:n,...o}=e,i=a.useRef(null),l=rW(r6,e.__scopeMenu),s=rY(r6,e.__scopeMenu),f=(0,c.s)(t,i),p=a.useRef(!1);return(0,d.jsx)(r8,{...o,ref:f,disabled:r,onClick:u(e.onClick,()=>{let e=i.current;if(!r&&e){let t=new CustomEvent(r9,{bubbles:!0,cancelable:!0});e.addEventListener(r9,e=>null==n?void 0:n(e),{once:!0}),(0,v.hO)(e,t),t.defaultPrevented?p.current=!1:l.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),p.current=!0},onPointerUp:u(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:u(e.onKeyDown,e=>{let t=""!==s.searchRef.current;r||t&&" "===e.key||rR.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});r5.displayName=r6;var r8=a.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:n=!1,textValue:o,...i}=e,l=rY(r6,r),s=rI(r),f=a.useRef(null),p=(0,c.s)(t,f),[m,h]=a.useState(!1),[g,b]=a.useState("");return a.useEffect(()=>{let e=f.current;if(e){var t;b((null!=(t=e.textContent)?t:"").trim())}},[i.children]),(0,d.jsx)(rP.ItemSlot,{scope:r,disabled:n,textValue:null!=o?o:g,children:(0,d.jsx)(t_,{asChild:!0,...s,focusable:!n,children:(0,d.jsx)(v.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":n||void 0,"data-disabled":n?"":void 0,...i,ref:p,onPointerMove:u(e.onPointerMove,nx(e=>{n?l.onItemLeave(e):(l.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:u(e.onPointerLeave,nx(e=>l.onItemLeave(e))),onFocus:u(e.onFocus,()=>h(!0)),onBlur:u(e.onBlur,()=>h(!1))})})})}),r4=a.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...o}=e;return(0,d.jsx)(ni,{scope:e.__scopeMenu,checked:r,children:(0,d.jsx)(r5,{role:"menuitemcheckbox","aria-checked":ny(r)?"mixed":r,...o,ref:t,"data-state":nw(r),onSelect:u(o.onSelect,()=>null==n?void 0:n(!!ny(r)||!r),{checkForDefaultPrevented:!1})})})});r4.displayName="MenuCheckboxItem";var r7="MenuRadioGroup",[r3,ne]=rO(r7,{value:void 0,onValueChange:()=>{}}),nt=a.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,i=M(n);return(0,d.jsx)(r3,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,d.jsx)(r1,{...o,ref:t})})});nt.displayName=r7;var nr="MenuRadioItem",nn=a.forwardRef((e,t)=>{let{value:r,...n}=e,o=ne(nr,e.__scopeMenu),i=r===o.value;return(0,d.jsx)(ni,{scope:e.__scopeMenu,checked:i,children:(0,d.jsx)(r5,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":nw(i),onSelect:u(n.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,r)},{checkForDefaultPrevented:!1})})})});nn.displayName=nr;var no="MenuItemIndicator",[ni,nl]=rO(no,{checked:!1}),na=a.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,i=nl(no,r);return(0,d.jsx)(tE,{present:n||ny(i.checked)||!0===i.checked,children:(0,d.jsx)(v.sG.span,{...o,ref:t,"data-state":nw(i.checked)})})});na.displayName=no;var ns=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,d.jsx)(v.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ns.displayName="MenuSeparator";var nu=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=rL(r);return(0,d.jsx)(tg,{...o,...n,ref:t})});nu.displayName="MenuArrow";var nc="MenuSub",[nd,nf]=rO(nc),np=e=>{let{__scopeMenu:t,children:r,open:n=!1,onOpenChange:o}=e,i=rF(nc,t),l=rL(t),[s,u]=a.useState(null),[c,f]=a.useState(null),p=M(o);return a.useEffect(()=>(!1===i.open&&p(!1),()=>p(!1)),[i.open,p]),(0,d.jsx)(ts,{...l,children:(0,d.jsx)(r_,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:f,children:(0,d.jsx)(nd,{scope:t,contentId:U(),triggerId:U(),trigger:s,onTriggerChange:u,children:r})})})};np.displayName=nc;var nm="MenuSubTrigger",nh=a.forwardRef((e,t)=>{let r=rF(nm,e.__scopeMenu),n=rW(nm,e.__scopeMenu),o=nf(nm,e.__scopeMenu),i=rY(nm,e.__scopeMenu),l=a.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:f}=i,p={__scopeMenu:e.__scopeMenu},m=a.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return a.useEffect(()=>m,[m]),a.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),f(null)}},[s,f]),(0,d.jsx)(rB,{asChild:!0,...p,children:(0,d.jsx)(r8,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":o.contentId,"data-state":nb(r.open),...e,ref:(0,c.t)(t,o.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:u(e.onPointerMove,nx(t=>{i.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||l.current||(i.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{r.onOpenChange(!0),m()},100)))})),onPointerLeave:u(e.onPointerLeave,nx(e=>{var t,n;m();let o=null==(t=r.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(n=r.content)?void 0:n.dataset.side,l="right"===t,a=o[l?"left":"right"],u=o[l?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(l?-5:5),y:e.clientY},{x:a,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:u(e.onKeyDown,t=>{let o=""!==i.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&rM[n.dir].includes(t.key)){var l;r.onOpenChange(!0),null==(l=r.content)||l.focus(),t.preventDefault()}})})})});nh.displayName=nm;var nv="MenuSubContent",ng=a.forwardRef((e,t)=>{let r=r$(rV,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=rF(rV,e.__scopeMenu),l=rW(rV,e.__scopeMenu),s=nf(nv,e.__scopeMenu),f=a.useRef(null),p=(0,c.s)(t,f);return(0,d.jsx)(rP.Provider,{scope:e.__scopeMenu,children:(0,d.jsx)(tE,{present:n||i.open,children:(0,d.jsx)(rP.Slot,{scope:e.__scopeMenu,children:(0,d.jsx)(r0,{id:s.contentId,"aria-labelledby":s.triggerId,...o,ref:p,align:"start",side:"rtl"===l.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;l.isUsingKeyboardRef.current&&(null==(t=f.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:u(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:u(e.onEscapeKeyDown,e=>{l.onClose(),e.preventDefault()}),onKeyDown:u(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=rA[l.dir].includes(e.key);if(t&&r){var n;i.onOpenChange(!1),null==(n=s.trigger)||n.focus(),e.preventDefault()}})})})})})});function nb(e){return e?"open":"closed"}function ny(e){return"indeterminate"===e}function nw(e){return ny(e)?"indeterminate":e?"checked":"unchecked"}function nx(e){return t=>"mouse"===t.pointerType?e(t):void 0}ng.displayName=nv;var nE="DropdownMenu",[nC,nR]=f(nE,[rD]),nk=rD(),[nS,nM]=nC(nE),nA=e=>{let{__scopeDropdownMenu:t,children:r,dir:n,open:o,defaultOpen:i,onOpenChange:l,modal:s=!0}=e,u=nk(t),c=a.useRef(null),[f,p]=h({prop:o,defaultProp:null!=i&&i,onChange:l,caller:nE});return(0,d.jsx)(nS,{scope:t,triggerId:U(),triggerRef:c,contentId:U(),open:f,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,d.jsx)(rG,{...u,open:f,onOpenChange:p,dir:n,modal:s,children:r})})};nA.displayName=nE;var nT="DropdownMenuTrigger",nP=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...o}=e,i=nM(nT,r),l=nk(r);return(0,d.jsx)(rB,{asChild:!0,...l,children:(0,d.jsx)(v.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...o,ref:(0,c.t)(t,i.triggerRef),onPointerDown:u(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:u(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});nP.displayName=nT;var nj=e=>{let{__scopeDropdownMenu:t,...r}=e,n=nk(t);return(0,d.jsx)(rU,{...n,...r})};nj.displayName="DropdownMenuPortal";var nN="DropdownMenuContent",nO=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nM(nN,r),i=nk(r),l=a.useRef(!1);return(0,d.jsx)(rq,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...n,ref:t,onCloseAutoFocus:u(e.onCloseAutoFocus,e=>{var t;l.current||null==(t=o.triggerRef.current)||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:u(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!o.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nO.displayName=nN;var nD=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(r1,{...o,...n,ref:t})});nD.displayName="DropdownMenuGroup";var nL=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(r2,{...o,...n,ref:t})});nL.displayName="DropdownMenuLabel";var nI=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(r5,{...o,...n,ref:t})});nI.displayName="DropdownMenuItem";var n_=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(r4,{...o,...n,ref:t})});n_.displayName="DropdownMenuCheckboxItem";var nF=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(nt,{...o,...n,ref:t})});nF.displayName="DropdownMenuRadioGroup";var nz=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(nn,{...o,...n,ref:t})});nz.displayName="DropdownMenuRadioItem";var nW=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(na,{...o,...n,ref:t})});nW.displayName="DropdownMenuItemIndicator";var nG=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(ns,{...o,...n,ref:t})});nG.displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(nu,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var nB=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(nh,{...o,...n,ref:t})});nB.displayName="DropdownMenuSubTrigger";var nK=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nk(r);return(0,d.jsx)(ng,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nK.displayName="DropdownMenuSubContent";var nH=nA,n$=nP,nU=nj,nV=nO,nX=nD,nY=nL,nq=nI,nZ=n_,nJ=nF,nQ=nz,n0=nW,n1=nG,n2=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:i}=e,l=nk(t),[a,s]=h({prop:n,defaultProp:null!=i&&i,onChange:o,caller:"DropdownMenuSub"});return(0,d.jsx)(np,{...l,open:a,onOpenChange:s,children:r})},n6=nB,n9=nK},7911:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8889).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8889:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(7620);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:s,className:u="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:r,strokeWidth:s?24*Number(a)/Number(o):a,className:i("lucide",u),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),s=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:s,...u}=r;return(0,n.createElement)(a,{ref:l,iconNode:t,className:i("lucide-".concat(o(e)),s),...u})});return r.displayName="".concat(e),r}},9640:(e,t,r)=>{r.d(t,{s:()=>l,t:()=>i});var n=r(7620);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},9649:(e,t,r)=>{r.d(t,{DX:()=>a,TL:()=>l});var n=r(7620),o=r(9640),i=r(4568);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var l;let e,a,s=(l=r,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,o.t)(t,s):s),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,a=n.Children.toArray(o),s=a.find(u);if(s){let e=s.props.children,o=a.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var a=l("Slot"),s=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}}}]);
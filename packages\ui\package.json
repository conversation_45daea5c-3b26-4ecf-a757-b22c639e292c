{"name": "@monetizr/ui", "version": "0.0.0", "private": true, "main": "./index.tsx", "types": "./index.tsx", "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.395.0", "tailwind-merge": "^2.3.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-dropdown-menu": "^2.0.6"}, "devDependencies": {"@monetizr/config": "0.1.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "eslint": "^8.57.0", "react": "^19.0.0", "tailwindcss": "^4.0.0-alpha.13", "typescript": "^5.4.5"}}
{"version": 4, "routes": {"/favicon.ico": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "image/x-icon", "x-next-cache-tags": "_N_T_/layout,_N_T_/favicon.ico/layout,_N_T_/favicon.ico/route,_N_T_/favicon.ico"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favicon.ico", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/dashboard/bank-account": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard/bank-account", "dataRoute": "/dashboard/bank-account.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/dashboard/profile": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard/profile", "dataRoute": "/dashboard/profile.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/dashboard/campaigns": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard/campaigns", "dataRoute": "/dashboard/campaigns.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "8c2744a8e5fd435a5a12c91138530a2f", "previewModeSigningKey": "060d78bb91657c8f28a3ffd3c91c933fdeab078b446ed59666b7c0ca9976df71", "previewModeEncryptionKey": "6db7d57e1f0b6c914d922dbb5f9cba17840e1d7b0f8e0a80637111091297f1bb"}}
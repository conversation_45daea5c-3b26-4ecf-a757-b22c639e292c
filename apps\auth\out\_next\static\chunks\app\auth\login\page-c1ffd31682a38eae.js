(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{2534:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>f});var r=t(4568),s=t(7261),d=t.n(s),o=t(2418),l=t(1938),n=t(60),i=t(5006),m=t(2935);let c=i.Ik({email:i.Yj().email({message:"Alamat email tidak valid."}),password:i.Yj().min(1,{message:"Password tidak boleh kosong."})});function f(){let e=(0,o.useRouter)(),{register:a,handleSubmit:t,formState:{errors:s,isSubmitting:i}}=(0,l.mN)({resolver:(0,n.u)(c)}),f=async a=>{try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a.email,password:a.password})});if(!t.ok){let e=await t.json();throw Error(e.error||"Login gagal. Periksa kembali email dan password Anda.")}e.push("https://dashboard.monetizr.com")}catch(e){console.error(e)}};return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900 p-4",children:(0,r.jsx)(m.Zp,{className:"w-full max-w-md",children:(0,r.jsxs)("form",{onSubmit:t(f),children:[(0,r.jsxs)(m.aR,{className:"space-y-1 text-center",children:[(0,r.jsx)(m.ZB,{className:"text-2xl font-bold",children:"Login ke PromotePro"}),(0,r.jsx)(m.BT,{children:"Masukkan email dan password Anda untuk mengakses dashboard."})]}),(0,r.jsxs)(m.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.JU,{htmlFor:"email",children:"Email"}),(0,r.jsx)(m.pd,{id:"email",type:"email",placeholder:"<EMAIL>",...a("email")}),s.email&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:s.email.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.JU,{htmlFor:"password",children:"Password"}),(0,r.jsx)(m.pd,{id:"password",type:"password",...a("password")}),s.password&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:s.password.message})]}),(0,r.jsx)(m.$n,{type:"submit",className:"w-full",disabled:i,children:i?"Memproses...":"Login"})]}),(0,r.jsxs)(m.wL,{className:"flex flex-col gap-2 text-sm text-center",children:[(0,r.jsx)(d(),{href:"/auth/forgot-password",children:"Lupa Password?"}),(0,r.jsxs)("p",{children:["Belum punya akun?"," ",(0,r.jsx)(d(),{href:"/auth/register",children:"Daftar Sekarang"})]})]})]})})})}},2935:(e,a,t)=>{"use strict";t.d(a,{$n:()=>c,Zp:()=>b,Wu:()=>y,BT:()=>N,wL:()=>w,aR:()=>h,ZB:()=>g,pd:()=>f,JU:()=>x});var r=t(4568),s=t(7620),d=t(9649),o=t(615),l=t(2987),n=t(607);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,n.QP)((0,l.$)(a))}let m=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,a)=>{let{className:t,variant:s,size:o,asChild:l=!1,...n}=e,c=l?d.DX:"button";return(0,r.jsx)(c,{className:i(m({variant:s,size:o,className:t})),ref:a,...n})});c.displayName="Button";let f=s.forwardRef((e,a)=>{let{className:t,type:s,...d}=e;return(0,r.jsx)("input",{type:s,className:i("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...d})});f.displayName="Input";var u=t(4762);let p=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),x=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(u.b,{ref:a,className:i(p(),t),...s})});x.displayName=u.b.displayName;let b=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});b.displayName="Card";let h=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:i("flex flex-col space-y-1.5 p-6",t),...s})});h.displayName="CardHeader";let g=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("h3",{ref:a,className:i("text-2xl font-semibold leading-none tracking-tight",t),...s})});g.displayName="CardTitle";let N=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("p",{ref:a,className:i("text-sm text-muted-foreground",t),...s})});N.displayName="CardDescription";let y=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:i("p-6 pt-0",t),...s})});y.displayName="CardContent";let w=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:i("flex items-center p-6 pt-0",t),...s})});w.displayName="CardFooter";var j=t(7167),v=t(7911),R=t(4931),k=t(1261);j.bL,j.l9,j.YJ,j.ZL,j.Pb,j.z6,s.forwardRef((e,a)=>{let{className:t,inset:s,children:d,...o}=e;return(0,r.jsxs)(j.ZP,{ref:a,className:i("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",t),...o,children:[d,(0,r.jsx)(v.A,{className:"ml-auto h-4 w-4"})]})}).displayName=j.ZP.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(j.G5,{ref:a,className:i("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...s})}).displayName=j.G5.displayName,s.forwardRef((e,a)=>{let{className:t,sideOffset:s=4,...d}=e;return(0,r.jsx)(j.ZL,{children:(0,r.jsx)(j.UC,{ref:a,sideOffset:s,className:i("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...d})})}).displayName=j.UC.displayName,s.forwardRef((e,a)=>{let{className:t,inset:s,...d}=e;return(0,r.jsx)(j.q7,{ref:a,className:i("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",t),...d})}).displayName=j.q7.displayName,s.forwardRef((e,a)=>{let{className:t,children:s,checked:d,...o}=e;return(0,r.jsxs)(j.H_,{ref:a,className:i("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:d,...o,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(j.VF,{children:(0,r.jsx)(R.A,{className:"h-4 w-4"})})}),s]})}).displayName=j.H_.displayName,s.forwardRef((e,a)=>{let{className:t,children:s,...d}=e;return(0,r.jsxs)(j.hN,{ref:a,className:i("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...d,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(j.VF,{children:(0,r.jsx)(k.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=j.hN.displayName,s.forwardRef((e,a)=>{let{className:t,inset:s,...d}=e;return(0,r.jsx)(j.JU,{ref:a,className:i("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",t),...d})}).displayName=j.JU.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(j.wv,{ref:a,className:i("-mx-1 my-1 h-px bg-muted",t),...s})}).displayName=j.wv.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:i("w-full caption-bottom text-sm",t),...s})})}).displayName="Table",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("thead",{ref:a,className:i("[&_tr]:border-b",t),...s})}).displayName="TableHeader",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tbody",{ref:a,className:i("[&_tr:last-child]:border-0",t),...s})}).displayName="TableBody",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tfoot",{ref:a,className:i("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...s})}).displayName="TableFooter",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tr",{ref:a,className:i("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...s})}).displayName="TableRow",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("th",{ref:a,className:i("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...s})}).displayName="TableHead",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("td",{ref:a,className:i("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...s})}).displayName="TableCell",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("caption",{ref:a,className:i("mt-4 text-sm text-muted-foreground",t),...s})}).displayName="TableCaption"},4331:(e,a,t)=>{Promise.resolve().then(t.bind(t,2534))}},e=>{var a=a=>e(e.s=a);e.O(0,[0,587,315,358],()=>a(4331)),_N_E=e.O()}]);
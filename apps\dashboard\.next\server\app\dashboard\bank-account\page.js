(()=>{var e={};e.id=506,e.ids=[506],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2436:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>n});let n=(0,r(3952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\apps\\\\dashboard\\\\src\\\\app\\\\dashboard\\\\bank-account\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\bank-account\\page.tsx","default")},2999:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>l,routeModule:()=>p,tree:()=>c});var n=r(4332),t=r(8819),s=r(7851),o=r.n(s),i=r(7540),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(a,d);let c={children:["",{children:["dashboard",{children:["bank-account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2436)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\bank-account\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,2341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\bank-account\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/dashboard/bank-account/page",pathname:"/dashboard/bank-account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},7902:(e,a,r)=>{Promise.resolve().then(r.bind(r,2436))},9062:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>c});var n=r(3486),t=r(5626),s=r(5519),o=r(1507),i=r(4665);let d=o.Ik({bankName:o.Yj().min(1,{message:"Nama bank tidak boleh kosong."}),accountHolderName:o.Yj().min(1,{message:"Nama pemilik rekening tidak boleh kosong."}),accountNumber:o.Yj().min(1,{message:"Nomor rekening tidak boleh kosong."})});function c(){let{register:e,handleSubmit:a,formState:{errors:r,isSubmitting:o}}=(0,t.mN)({resolver:(0,s.u)(d)}),c=async e=>{try{if(!(await fetch("/api/user/bank-account",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Gagal menyimpan rekening bank");alert("Rekening bank berhasil disimpan!")}catch(e){console.error(e),alert("Terjadi kesalahan saat menyimpan rekening bank")}};return(0,n.jsx)("div",{className:"p-4",children:(0,n.jsx)(i.Zp,{className:"w-full max-w-md",children:(0,n.jsxs)("form",{onSubmit:a(c),children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsx)(i.ZB,{children:"Informasi Rekening Bank"}),(0,n.jsx)(i.BT,{children:"Masukkan detail rekening bank Anda untuk menerima pembayaran."})]}),(0,n.jsxs)(i.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(i.JU,{htmlFor:"bankName",children:"Nama Bank"}),(0,n.jsx)(i.pd,{id:"bankName",placeholder:"Contoh: Bank Central Asia",...e("bankName")}),r.bankName&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:r.bankName.message})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(i.JU,{htmlFor:"accountHolderName",children:"Nama Pemilik Rekening"}),(0,n.jsx)(i.pd,{id:"accountHolderName",placeholder:"Nama sesuai buku tabungan",...e("accountHolderName")}),r.accountHolderName&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:r.accountHolderName.message})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(i.JU,{htmlFor:"accountNumber",children:"Nomor Rekening"}),(0,n.jsx)(i.pd,{id:"accountNumber",placeholder:"xxxxxxxxxx",...e("accountNumber")}),r.accountNumber&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:r.accountNumber.message})]})]}),(0,n.jsx)(i.wL,{children:(0,n.jsx)(i.$n,{type:"submit",disabled:o,children:o?"Menyimpan...":"Simpan Rekening"})})]})})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9654:(e,a,r)=>{Promise.resolve().then(r.bind(r,9062))},9699:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>t});var n=r(1253);let t=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var a=require("../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),n=a.X(0,[191,77,253,330,405],()=>r(2999));module.exports=n})();
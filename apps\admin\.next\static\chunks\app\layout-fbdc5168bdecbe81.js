(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{8735:(e,n,s)=>{Promise.resolve().then(s.t.bind(s,8744,23))},8744:()=>{throw Error("Module build failed (from ../../node_modules/next/dist/compiled/mini-css-extract-plugin/loader.js):\nHookWebpackError: Module build failed (from ../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js):\nNonErrorEmittedError: (Emitted value instead of an instance of Error) ResolveMessage: Cannot find module '../lightningcss.win32-x64-msvc.node' from 'C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\lightningcss\\node\\index.js'\n    at new WebpackError (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:503751)\n    at new NonErrorEmittedError (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:400197)\n    at processResult (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:407042)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:408906)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\loader-runner\\LoaderRunner.js:1:8645)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\loader-runner\\LoaderRunner.js:1:5828)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\loader-runner\\LoaderRunner.js:1:4039)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js:122:37)\n    at processTicksAndRejections (native)\n    at new WebpackError (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:503751)\n    at new HookWebpackError (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:315505)\n    at tryRunOrWebpackError (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:316142)\n    at __webpack_require_module__ (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:131548)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:131840)\n    at symbolIterator (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\neo-async\\async.js:1:14444)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\neo-async\\async.js:1:14824)\n    at anonymous (file:///C:/Users/<USER>/Documents/augment-projects/monetizr/node_modules/next/dist/compiled/webpack/bundle5.js:15:10)\n    at CALL_ASYNC_DELEGATE (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:14:6378)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:130703)\n-- inner error --\nError: Module build failed (from ../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js):\nNonErrorEmittedError: (Emitted value instead of an instance of Error) ResolveMessage: Cannot find module '../lightningcss.win32-x64-msvc.node' from 'C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\lightningcss\\node\\index.js'\n    at new WebpackError (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:503751)\n    at new NonErrorEmittedError (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:400197)\n    at processResult (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:407042)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:408906)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\loader-runner\\LoaderRunner.js:1:8645)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\loader-runner\\LoaderRunner.js:1:5828)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\loader-runner\\LoaderRunner.js:1:4039)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js:122:37)\n    at processTicksAndRejections (native)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\globals.css:2:7)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:962741)\n    at anonymous (file:///C:/Users/<USER>/Documents/augment-projects/monetizr/node_modules/next/dist/compiled/webpack/bundle5.js:7:5)\n    at CALL_DELEGATE (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:14:6272)\n    at tryRunOrWebpackError (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:316096)\n    at __webpack_require_module__ (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:131548)\n    at <anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:29:131840)\n    at symbolIterator (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\neo-async\\async.js:1:14444)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\compiled\\neo-async\\async.js:1:14824)\n    at anonymous (file:///C:/Users/<USER>/Documents/augment-projects/monetizr/node_modules/next/dist/compiled/webpack/bundle5.js:15:10)\n\nGenerated code for C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\globals.css\n1 | throw new Error(\"Module build failed (from ../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js):\\nNonErrorEmittedError: (Emitted value instead of an instance of Error) ResolveMessage: Cannot find module '../lightningcss.win32-x64-msvc.node' from 'C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\node_modules\\\\lightningcss\\\\node\\\\index.js'\\n    at new WebpackError (C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\webpack\\\\bundle5.js:29:503751)\\n    at new NonErrorEmittedError (C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\webpack\\\\bundle5.js:29:400197)\\n    at processResult (C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\webpack\\\\bundle5.js:29:407042)\\n    at <anonymous> (C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\webpack\\\\bundle5.js:29:408906)\\n    at <anonymous> (C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\loader-runner\\\\LoaderRunner.js:1:8645)\\n    at <anonymous> (C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\loader-runner\\\\LoaderRunner.js:1:5828)\\n    at <anonymous> (C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\loader-runner\\\\LoaderRunner.js:1:4039)\\n    at <anonymous> (C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\node_modules\\\\next\\\\dist\\\\build\\\\webpack\\\\loaders\\\\postcss-loader\\\\src\\\\index.js:122:37)\\n    at processTicksAndRejections (native)\");")}},e=>{var n=n=>e(e.s=n);e.O(0,[587,315,358],()=>n(8735)),_N_E=e.O()}]);
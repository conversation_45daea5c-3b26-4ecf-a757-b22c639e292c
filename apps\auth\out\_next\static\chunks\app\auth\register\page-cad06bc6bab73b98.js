(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{2935:(e,a,r)=>{"use strict";r.d(a,{$n:()=>c,Zp:()=>h,Wu:()=>w,BT:()=>N,wL:()=>y,aR:()=>b,ZB:()=>g,pd:()=>f,JU:()=>x});var t=r(4568),s=r(7620),d=r(9649),o=r(615),l=r(2987),n=r(607);function i(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,n.QP)((0,l.$)(a))}let m=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,a)=>{let{className:r,variant:s,size:o,asChild:l=!1,...n}=e,c=l?d.DX:"button";return(0,t.jsx)(c,{className:i(m({variant:s,size:o,className:r})),ref:a,...n})});c.displayName="Button";let f=s.forwardRef((e,a)=>{let{className:r,type:s,...d}=e;return(0,t.jsx)("input",{type:s,className:i("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:a,...d})});f.displayName="Input";var p=r(4762);let u=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),x=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)(p.b,{ref:a,className:i(u(),r),...s})});x.displayName=p.b.displayName;let h=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});h.displayName="Card";let b=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:i("flex flex-col space-y-1.5 p-6",r),...s})});b.displayName="CardHeader";let g=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("h3",{ref:a,className:i("text-2xl font-semibold leading-none tracking-tight",r),...s})});g.displayName="CardTitle";let N=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("p",{ref:a,className:i("text-sm text-muted-foreground",r),...s})});N.displayName="CardDescription";let w=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:i("p-6 pt-0",r),...s})});w.displayName="CardContent";let y=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:i("flex items-center p-6 pt-0",r),...s})});y.displayName="CardFooter";var j=r(7167),v=r(7911),k=r(4931),R=r(1261);j.bL,j.l9,j.YJ,j.ZL,j.Pb,j.z6,s.forwardRef((e,a)=>{let{className:r,inset:s,children:d,...o}=e;return(0,t.jsxs)(j.ZP,{ref:a,className:i("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",r),...o,children:[d,(0,t.jsx)(v.A,{className:"ml-auto h-4 w-4"})]})}).displayName=j.ZP.displayName,s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)(j.G5,{ref:a,className:i("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...s})}).displayName=j.G5.displayName,s.forwardRef((e,a)=>{let{className:r,sideOffset:s=4,...d}=e;return(0,t.jsx)(j.ZL,{children:(0,t.jsx)(j.UC,{ref:a,sideOffset:s,className:i("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...d})})}).displayName=j.UC.displayName,s.forwardRef((e,a)=>{let{className:r,inset:s,...d}=e;return(0,t.jsx)(j.q7,{ref:a,className:i("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",r),...d})}).displayName=j.q7.displayName,s.forwardRef((e,a)=>{let{className:r,children:s,checked:d,...o}=e;return(0,t.jsxs)(j.H_,{ref:a,className:i("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:d,...o,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(j.VF,{children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})}),s]})}).displayName=j.H_.displayName,s.forwardRef((e,a)=>{let{className:r,children:s,...d}=e;return(0,t.jsxs)(j.hN,{ref:a,className:i("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...d,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(j.VF,{children:(0,t.jsx)(R.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=j.hN.displayName,s.forwardRef((e,a)=>{let{className:r,inset:s,...d}=e;return(0,t.jsx)(j.JU,{ref:a,className:i("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",r),...d})}).displayName=j.JU.displayName,s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)(j.wv,{ref:a,className:i("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=j.wv.displayName,s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:a,className:i("w-full caption-bottom text-sm",r),...s})})}).displayName="Table",s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("thead",{ref:a,className:i("[&_tr]:border-b",r),...s})}).displayName="TableHeader",s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("tbody",{ref:a,className:i("[&_tr:last-child]:border-0",r),...s})}).displayName="TableBody",s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("tfoot",{ref:a,className:i("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...s})}).displayName="TableFooter",s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("tr",{ref:a,className:i("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...s})}).displayName="TableRow",s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("th",{ref:a,className:i("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...s})}).displayName="TableHead",s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("td",{ref:a,className:i("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...s})}).displayName="TableCell",s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("caption",{ref:a,className:i("mt-4 text-sm text-muted-foreground",r),...s})}).displayName="TableCaption"},3015:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>c});var t=r(4568),s=r(7261),d=r.n(s),o=r(1938),l=r(60),n=r(5006),i=r(2935);let m=n.Ik({name:n.Yj().min(1,{message:"Nama lengkap tidak boleh kosong."}),email:n.Yj().email({message:"Alamat email tidak valid."}),password:n.Yj().min(8,{message:"Password harus minimal 8 karakter."}),confirmPassword:n.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Password tidak cocok",path:["confirmPassword"]});function c(){let{register:e,handleSubmit:a,formState:{errors:r,isSubmitting:s}}=(0,o.mN)({resolver:(0,l.u)(m)}),n=async e=>{try{let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,email:e.email,password:e.password,role:"promoter"})}),r=await a.json();if(!a.ok)throw Error(r.error||"Pendaftaran gagal. Coba lagi nanti.");alert("Pendaftaran berhasil! Silakan login dengan akun Anda."),window.location.href="/auth/login"}catch(e){console.error(e),alert(e.message||"Terjadi kesalahan saat mendaftar")}};return(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900 p-4",children:(0,t.jsx)(i.Zp,{className:"w-full max-w-md",children:(0,t.jsxs)("form",{onSubmit:a(n),children:[(0,t.jsxs)(i.aR,{className:"space-y-1 text-center",children:[(0,t.jsx)(i.ZB,{className:"text-2xl font-bold",children:"Daftar Akun PromotePro"}),(0,t.jsx)(i.BT,{children:"Buat akun baru untuk memulai perjalanan Anda."})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.JU,{htmlFor:"name",children:"Nama Lengkap"}),(0,t.jsx)(i.pd,{id:"name",type:"text",placeholder:"John Doe",...e("name")}),r.name&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:r.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.JU,{htmlFor:"email",children:"Email"}),(0,t.jsx)(i.pd,{id:"email",type:"email",placeholder:"<EMAIL>",...e("email")}),r.email&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:r.email.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.JU,{htmlFor:"password",children:"Password"}),(0,t.jsx)(i.pd,{id:"password",type:"password",...e("password")}),r.password&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:r.password.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.JU,{htmlFor:"confirm-password",children:"Konfirmasi Password"}),(0,t.jsx)(i.pd,{id:"confirm-password",type:"password",...e("confirmPassword")}),r.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:r.confirmPassword.message})]}),(0,t.jsx)(i.$n,{type:"submit",className:"w-full",disabled:s,children:s?"Memproses...":"Daftar"})]}),(0,t.jsx)(i.wL,{className:"flex flex-col gap-2 text-sm text-center",children:(0,t.jsxs)("p",{children:["Sudah punya akun?"," ",(0,t.jsx)(d(),{href:"/auth/login",className:"underline",children:"Login"})]})})]})})})}},5385:(e,a,r)=>{Promise.resolve().then(r.bind(r,3015))}},e=>{var a=a=>e(e.s=a);e.O(0,[0,587,315,358],()=>a(5385)),_N_E=e.O()}]);
{"clientTcpRtt": 14, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES128-GCM-SHA256", "continent": "AS", "asn": 8075, "clientAcceptEncoding": "gzip, deflate, br, zstd", "verifiedBotCategory": "", "country": "ID", "isEUCountry": false, "region": "Jakarta", "tlsClientCiphersSha1": "z756bn+x21PiebYy319nCdFpEYo=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "LY2Qz85peG5MD+K/mBLpzrCCog2kyVyXOZ4W3OhXHjo=", "tlsExportedAuthenticator": {"clientFinished": "df2d2ecd655ad43fed878aeb18d17c8652d54617e699f6f2c913fb8afe8312f3", "clientHandshake": "f5cb9f805abe39ebd02031992e5ae1f08d60fcf601d4326bcb183e1a12252a82", "serverHandshake": "389fcbe3022058cccfc76b15f3a14a210308909a8ff34721fdcbe32e1081bac6", "serverFinished": "793463d6d601d564edc0e601fdddafb18479eccaceef15d00da481ec8a436fa0"}, "tlsClientHelloLength": "564", "colo": "SIN", "timezone": "Asia/Jakarta", "longitude": "106.84513", "latitude": "-6.21462", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "12850", "city": "Jakarta", "tlsVersion": "TLSv1.3", "regionCode": "JK", "asOrganization": "Microsoft Corporation", "tlsClientExtensionsSha1Le": "29pLmat8Ljr3wiUemHNTUe0plFA=", "tlsClientExtensionsSha1": "g7CUyX4IGfVWT4opL1PEzZ2U1EQ=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}
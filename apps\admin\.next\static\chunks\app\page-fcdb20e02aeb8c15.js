(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1870:(e,t,a)=>{"use strict";a.d(t,{UsersDataTable:()=>D});var r=a(4568),s=a(7620),l=a(7657),o=a(8175),d=a(9649),n=a(615),i=a(2987),c=a(607);function m(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,c.QP)((0,i.$)(t))}let f=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef((e,t)=>{let{className:a,variant:s,size:l,asChild:o=!1,...n}=e,i=o?d.DX:"button";return(0,r.jsx)(i,{className:m(f({variant:s,size:l,className:a})),ref:t,...n})});u.displayName="Button";let p=s.forwardRef((e,t)=>{let{className:a,type:s,...l}=e;return(0,r.jsx)("input",{type:s,className:m("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...l})});p.displayName="Input";var x=a(4762);let g=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70");s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(x.b,{ref:t,className:m(g(),a),...s})}).displayName=x.b.displayName;let b=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:m("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});b.displayName="Card",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:m("flex flex-col space-y-1.5 p-6",a),...s})}).displayName="CardHeader",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h3",{ref:t,className:m("text-2xl font-semibold leading-none tracking-tight",a),...s})}).displayName="CardTitle",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("p",{ref:t,className:m("text-sm text-muted-foreground",a),...s})}).displayName="CardDescription",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:m("p-6 pt-0",a),...s})}).displayName="CardContent",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:m("flex items-center p-6 pt-0",a),...s})}).displayName="CardFooter";var h=a(7167),N=a(7911),v=a(4931),y=a(1261);h.bL,h.l9,h.YJ,h.ZL,h.Pb,h.z6,s.forwardRef((e,t)=>{let{className:a,inset:s,children:l,...o}=e;return(0,r.jsxs)(h.ZP,{ref:t,className:m("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",a),...o,children:[l,(0,r.jsx)(N.A,{className:"ml-auto h-4 w-4"})]})}).displayName=h.ZP.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(h.G5,{ref:t,className:m("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...s})}).displayName=h.G5.displayName,s.forwardRef((e,t)=>{let{className:a,sideOffset:s=4,...l}=e;return(0,r.jsx)(h.ZL,{children:(0,r.jsx)(h.UC,{ref:t,sideOffset:s,className:m("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})})}).displayName=h.UC.displayName,s.forwardRef((e,t)=>{let{className:a,inset:s,...l}=e;return(0,r.jsx)(h.q7,{ref:t,className:m("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",a),...l})}).displayName=h.q7.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,checked:l,...o}=e;return(0,r.jsxs)(h.H_,{ref:t,className:m("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:l,...o,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(h.VF,{children:(0,r.jsx)(v.A,{className:"h-4 w-4"})})}),s]})}).displayName=h.H_.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,...l}=e;return(0,r.jsxs)(h.hN,{ref:t,className:m("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(h.VF,{children:(0,r.jsx)(y.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=h.hN.displayName,s.forwardRef((e,t)=>{let{className:a,inset:s,...l}=e;return(0,r.jsx)(h.JU,{ref:t,className:m("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",a),...l})}).displayName=h.JU.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(h.wv,{ref:t,className:m("-mx-1 my-1 h-px bg-muted",a),...s})}).displayName=h.wv.displayName;let j=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:m("w-full caption-bottom text-sm",a),...s})})});j.displayName="Table";let w=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("thead",{ref:t,className:m("[&_tr]:border-b",a),...s})});w.displayName="TableHeader";let R=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("tbody",{ref:t,className:m("[&_tr:last-child]:border-0",a),...s})});R.displayName="TableBody",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("tfoot",{ref:t,className:m("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...s})}).displayName="TableFooter";let C=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("tr",{ref:t,className:m("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...s})});C.displayName="TableRow";let k=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("th",{ref:t,className:m("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...s})});k.displayName="TableHead";let P=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("td",{ref:t,className:m("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...s})});P.displayName="TableCell",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("caption",{ref:t,className:m("mt-4 text-sm text-muted-foreground",a),...s})}).displayName="TableCaption";let z=[{id:"1",name:"John Doe",email:"<EMAIL>",joinDate:"2023-01-15",status:"Aktif"},{id:"2",name:"Jane Smith",email:"<EMAIL>",joinDate:"2023-02-20",status:"Aktif"},{id:"3",name:"Sam Wilson",email:"<EMAIL>",joinDate:"2023-03-10",status:"Nonaktif"}],T=[{accessorKey:"name",header:"Nama"},{accessorKey:"email",header:"Email"},{accessorKey:"joinDate",header:"Tanggal Bergabung"},{accessorKey:"status",header:"Status"},{id:"actions",cell:e=>{let{row:t}=e;return t.original,(0,r.jsx)(u,{variant:"ghost",size:"sm",children:"Aksi"})}}];function D(){var e,t,a;let[d]=(0,s.useState)(()=>[...z]),[n,i]=(0,s.useState)([]),[c,m]=(0,s.useState)([]),f=(0,l.N4)({data:d,columns:T,getCoreRowModel:(0,o.HT)(),getPaginationRowModel:(0,o.kW)(),onSortingChange:i,getSortedRowModel:(0,o.h5)(),onColumnFiltersChange:m,getFilteredRowModel:(0,o.hM)(),state:{sorting:n,columnFilters:c}});return(0,r.jsxs)(b,{children:[(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)(p,{placeholder:"Cari pengguna...",value:null!=(a=null==(e=f.getColumn("name"))?void 0:e.getFilterValue())?a:"",onChange:e=>{var t;return null==(t=f.getColumn("name"))?void 0:t.setFilterValue(e.target.value)},className:"max-w-sm"})}),(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(j,{children:[(0,r.jsx)(w,{children:f.getHeaderGroups().map(e=>(0,r.jsx)(C,{children:e.headers.map(e=>(0,r.jsx)(k,{children:e.isPlaceholder?null:(0,l.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,r.jsx)(R,{children:(null==(t=f.getRowModel().rows)?void 0:t.length)?f.getRowModel().rows.map(e=>(0,r.jsx)(C,{"data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,r.jsx)(P,{children:(0,l.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,r.jsx)(C,{children:(0,r.jsx)(P,{colSpan:T.length,className:"h-24 text-center",children:"No results."})})})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 p-4",children:[(0,r.jsx)(u,{variant:"outline",size:"sm",onClick:()=>f.previousPage(),disabled:!f.getCanPreviousPage(),children:"Previous"}),(0,r.jsx)(u,{variant:"outline",size:"sm",onClick:()=>f.nextPage(),disabled:!f.getCanNextPage(),children:"Next"})]})]})}},3189:(e,t,a)=>{Promise.resolve().then(a.bind(a,1870))}},e=>{var t=t=>e(e.s=t);e.O(0,[112,587,315,358],()=>t(3189)),_N_E=e.O()}]);
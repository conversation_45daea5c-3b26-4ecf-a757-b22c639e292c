"use strict";

module.exports = (arr_, pred) => {

    const arr   = arr_ || [],
          spans = []
    
    let span = { label: undefined,
                 items: [arr.first] }
                 
    arr.forEach (x => {

        const label = pred (x)

        if ((span.label !== label) && span.items.length) {
            spans.push (span = { label: label, items: [x] }) }

        else {
            span.items.push (x) } })

    return spans
}
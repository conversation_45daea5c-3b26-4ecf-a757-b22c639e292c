(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[399],{2985:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var r=a(2987),o=a(607);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,o.QP)((0,r.$)(t))}},3482:(e,t,a)=>{"use strict";a.r(t),a.d(t,{DropdownMenu:()=>c,DropdownMenuCheckboxItem:()=>N,DropdownMenuContent:()=>g,DropdownMenuGroup:()=>m,DropdownMenuItem:()=>w,DropdownMenuLabel:()=>v,DropdownMenuPortal:()=>u,DropdownMenuRadioGroup:()=>b,DropdownMenuRadioItem:()=>y,DropdownMenuSeparator:()=>j,DropdownMenuShortcut:()=>R,DropdownMenuSub:()=>p,DropdownMenuSubContent:()=>h,DropdownMenuSubTrigger:()=>x,DropdownMenuTrigger:()=>f});var r=a(4568),o=a(7620),s=a(7167),n=a(7911),d=a(4931),l=a(1261),i=a(2985);let c=s.bL,f=s.l9,m=s.YJ,u=s.ZL,p=s.Pb,b=s.z6,x=o.forwardRef((e,t)=>{let{className:a,inset:o,children:d,...l}=e;return(0,r.jsxs)(s.ZP,{ref:t,className:(0,i.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",o&&"pl-8",a),...l,children:[d,(0,r.jsx)(n.A,{className:"ml-auto h-4 w-4"})]})});x.displayName=s.ZP.displayName;let h=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)(s.G5,{ref:t,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...o})});h.displayName=s.G5.displayName;let g=o.forwardRef((e,t)=>{let{className:a,sideOffset:o=4,...n}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{ref:t,sideOffset:o,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})})});g.displayName=s.UC.displayName;let w=o.forwardRef((e,t)=>{let{className:a,inset:o,...n}=e;return(0,r.jsx)(s.q7,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",o&&"pl-8",a),...n})});w.displayName=s.q7.displayName;let N=o.forwardRef((e,t)=>{let{className:a,children:o,checked:n,...l}=e;return(0,r.jsxs)(s.H_,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:n,...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),o]})});N.displayName=s.H_.displayName;let y=o.forwardRef((e,t)=>{let{className:a,children:o,...n}=e;return(0,r.jsxs)(s.hN,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),o]})});y.displayName=s.hN.displayName;let v=o.forwardRef((e,t)=>{let{className:a,inset:o,...n}=e;return(0,r.jsx)(s.JU,{ref:t,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",o&&"pl-8",a),...n})});v.displayName=s.JU.displayName;let j=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)(s.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...o})});j.displayName=s.wv.displayName;let R=e=>{let{className:t,...a}=e;return(0,r.jsx)("span",{className:(0,i.cn)("ml-auto text-xs tracking-widest opacity-60",t),...a})};R.displayName="DropdownMenuShortcut"},3735:(e,t,a)=>{"use strict";a.d(t,{$n:()=>i,Zp:()=>m,Wu:()=>x,BT:()=>b,wL:()=>h,aR:()=>u,ZB:()=>p,rI:()=>g.DropdownMenu,SQ:()=>g.DropdownMenuContent,_2:()=>g.DropdownMenuItem,lp:()=>g.DropdownMenuLabel,mB:()=>g.DropdownMenuSeparator,ty:()=>g.DropdownMenuTrigger,pd:()=>c,JU:()=>f.Label});var r=a(4568),o=a(7620),s=a(9649),n=a(615),d=a(2985);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=o.forwardRef((e,t)=>{let{className:a,variant:o,size:n,asChild:i=!1,...c}=e,f=i?s.DX:"button";return(0,r.jsx)(f,{className:(0,d.cn)(l({variant:o,size:n,className:a})),ref:t,...c})});i.displayName="Button";let c=o.forwardRef((e,t)=>{let{className:a,type:o,...s}=e;return(0,r.jsx)("input",{type:o,className:(0,d.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...s})});c.displayName="Input";var f=a(9237);let m=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("div",{ref:t,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...o})});m.displayName="Card";let u=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("div",{ref:t,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...o})});u.displayName="CardHeader";let p=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("h3",{ref:t,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...o})});p.displayName="CardTitle";let b=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("p",{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",a),...o})});b.displayName="CardDescription";let x=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("div",{ref:t,className:(0,d.cn)("p-6 pt-0",a),...o})});x.displayName="CardContent";let h=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("div",{ref:t,className:(0,d.cn)("flex items-center p-6 pt-0",a),...o})});h.displayName="CardFooter";var g=a(3482);o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,d.cn)("w-full caption-bottom text-sm",a),...o})})}).displayName="Table",o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("thead",{ref:t,className:(0,d.cn)("[&_tr]:border-b",a),...o})}).displayName="TableHeader",o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,d.cn)("[&_tr:last-child]:border-0",a),...o})}).displayName="TableBody",o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,d.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...o})}).displayName="TableFooter",o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("tr",{ref:t,className:(0,d.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...o})}).displayName="TableRow",o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("th",{ref:t,className:(0,d.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...o})}).displayName="TableHead",o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("td",{ref:t,className:(0,d.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...o})}).displayName="TableCell",o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)("caption",{ref:t,className:(0,d.cn)("mt-4 text-sm text-muted-foreground",a),...o})}).displayName="TableCaption"},6665:(e,t,a)=>{Promise.resolve().then(a.bind(a,8691))},8691:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(4568),o=a(7620),s=a(1938),n=a(60),d=a(5006),l=a(3735);let i=d.Ik({name:d.Yj().min(1,{message:"Nama tidak boleh kosong."}),bio:d.Yj().optional()});function c(){let[e,t]=(0,o.useState)(null),{register:a,handleSubmit:d,formState:{errors:c,isSubmitting:f},reset:m}=(0,s.mN)({resolver:(0,n.u)(i)});(0,o.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/user/profile");if(!e.ok)throw Error("Gagal mengambil data profil");let a=await e.json();t(a),m(a)}catch(e){console.error(e)}})()},[m]);let u=async e=>{try{if(!(await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Gagal memperbarui profil");t(e),alert("Profil berhasil diperbarui!")}catch(e){console.error(e),alert("Terjadi kesalahan saat memperbarui profil")}};return e?(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)(l.Zp,{className:"w-full max-w-md",children:(0,r.jsxs)("form",{onSubmit:d(u),children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Profil Pengguna"})}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.JU,{htmlFor:"name",children:"Nama"}),(0,r.jsx)(l.pd,{id:"name",placeholder:"Nama Anda",...a("name")}),c.name&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:c.name.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.JU,{htmlFor:"bio",children:"Bio"}),(0,r.jsx)(l.pd,{id:"bio",placeholder:"Tentang Anda",...a("bio")})]})]}),(0,r.jsx)(l.wL,{children:(0,r.jsx)(l.$n,{type:"submit",disabled:f,children:f?"Menyimpan...":"Simpan Perubahan"})})]})})}):(0,r.jsx)("div",{children:"Memuat..."})}},9237:(e,t,a)=>{"use strict";a.r(t),a.d(t,{Label:()=>i});var r=a(4568),o=a(7620),s=a(4762),n=a(615),d=a(2985);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,r.jsx)(s.b,{ref:t,className:(0,d.cn)(l(),a),...o})});i.displayName=s.b.displayName}},e=>{var t=t=>e(e.s=t);e.O(0,[688,415,587,315,358],()=>t(6665)),_N_E=e.O()}]);
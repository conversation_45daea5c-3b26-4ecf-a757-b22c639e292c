{"name": "dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "zustand": "^4.5.2", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@monetizr/ui": "*", "@monetizr/db": "*", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"eslint-config-custom": "*", "tsconfig": "*", "typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/jsonwebtoken": "^9.0.6", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}
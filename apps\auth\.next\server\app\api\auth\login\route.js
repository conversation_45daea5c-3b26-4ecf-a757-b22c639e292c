(()=>{var e={};e.id=758,e.ids=[758],e.modules={408:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3252:(e,t,a)=>{"use strict";a.d(t,{Er:()=>T,wU:()=>d,QQ:()=>o,BE:()=>n}),a(424),a(5208);let s=`
-- Users table
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  password_hash TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('creator', 'promoter', 'admin')),
  bio TEXT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Bank accounts table
CREATE TABLE IF NOT EXISTS bank_accounts (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  bank_name TEXT NOT NULL,
  account_holder_name TEXT NOT NULL,
  account_number TEXT NOT NULL,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Campaigns table
CREATE TABLE IF NOT EXISTS campaigns (
  id TEXT PRIMARY KEY,
  creator_id TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  budget REAL NOT NULL,
  price_per_view REAL NOT NULL,
  requirements TEXT,
  material_url TEXT,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed')),
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  expires_at TEXT,
  FOREIGN KEY (creator_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Promotions table
CREATE TABLE IF NOT EXISTS promotions (
  id TEXT PRIMARY KEY,
  campaign_id TEXT NOT NULL,
  promoter_id TEXT NOT NULL,
  tracking_link TEXT UNIQUE NOT NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'rejected')),
  views_count INTEGER NOT NULL DEFAULT 0,
  earnings REAL NOT NULL DEFAULT 0,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (campaign_id) REFERENCES campaigns (id) ON DELETE CASCADE,
  FOREIGN KEY (promoter_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'earning', 'payment')),
  amount REAL NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
  description TEXT NOT NULL,
  reference_id TEXT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Wallets table
CREATE TABLE IF NOT EXISTS wallets (
  id TEXT PRIMARY KEY,
  user_id TEXT UNIQUE NOT NULL,
  balance REAL NOT NULL DEFAULT 0,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users (email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users (role);
CREATE INDEX IF NOT EXISTS idx_campaigns_creator ON campaigns (creator_id);
CREATE INDEX IF NOT EXISTS idx_campaigns_status ON campaigns (status);
CREATE INDEX IF NOT EXISTS idx_promotions_campaign ON promotions (campaign_id);
CREATE INDEX IF NOT EXISTS idx_promotions_promoter ON promotions (promoter_id);
CREATE INDEX IF NOT EXISTS idx_transactions_user ON transactions (user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions (type);
`;function r(){return new Date().toISOString()}class E{constructor(){this.data=new Map,this.data.set("users",[]),this.data.set("bank_accounts",[]),this.data.set("campaigns",[]),this.data.set("promotions",[]),this.data.set("transactions",[]),this.data.set("wallets",[])}prepare(e){return{bind:(...t)=>({first:async()=>e.includes("SELECT")&&e.includes("users")&&e.includes("email")&&(this.data.get("users")||[]).find(e=>e.email===t[0])||null,all:async()=>e.includes("SELECT")&&e.includes("users")?{results:this.data.get("users")||[]}:{results:[]},run:async()=>{if(e.includes("INSERT INTO users")){let e=this.data.get("users")||[],a={id:t[0],email:t[1],name:t[2],password_hash:t[3],role:t[4],bio:t[5]||null,created_at:r(),updated_at:r(),is_active:!0};return e.push(a),this.data.set("users",e),{success:!0,meta:{changes:1}}}return{success:!0,meta:{changes:0}}}})}}async exec(e){return{success:!0}}}let i=new E;async function T(e){return btoa(e)}async function n(e,t){return btoa(e)===t}let o={async create(e){let t=crypto.randomUUID(),a=r(),s={...e,id:t,created_at:a,updated_at:a};return await i.prepare(`
      INSERT INTO users (id, email, name, password_hash, role, bio, created_at, updated_at, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(s.id,s.email,s.name,s.password_hash,s.role,s.bio,s.created_at,s.updated_at,s.is_active).run(),s},findByEmail:async e=>await i.prepare(`
      SELECT * FROM users WHERE email = ? AND is_active = TRUE
    `).bind(e).first(),findById:async e=>await i.prepare(`
      SELECT * FROM users WHERE id = ? AND is_active = TRUE
    `).bind(e).first(),async update(e,t){let a=Object.keys(t).map(e=>`${e} = ?`).join(", "),s=Object.values(t);return s.push(r()),s.push(e),(await i.prepare(`
      UPDATE users SET ${a}, updated_at = ? WHERE id = ?
    `).bind(...s).run()).success}};async function d(){try{await i.exec(s),console.log("Database schema initialized successfully")}catch(e){throw console.error("Failed to initialize database schema:",e),e}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4135:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>_,routeModule:()=>p,serverHooks:()=>R,workAsyncStorage:()=>L,workUnitAsyncStorage:()=>l});var s={};a.r(s),a.d(s,{POST:()=>c});var r=a(8106),E=a(8819),i=a(2050),T=a(4235),n=a(5208),o=a(3252),d=a(424),u=a.n(d);let N=!1;async function c(e){try{N||(await (0,o.wU)(),N=!0);let{email:t,password:a}=await e.json();if(!t||!a)return T.NextResponse.json({error:"Email dan password harus diisi"},{status:400});let s=await o.QQ.findByEmail(t);if(!s||!await (0,o.BE)(a,s.password_hash))return T.NextResponse.json({error:"Email atau password tidak valid"},{status:401});let r=u().sign({userId:s.id,email:s.email,role:s.role},process.env.JWT_SECRET||"development-secret-key",{expiresIn:"7d"});return(await (0,n.UL)()).set("auth_token",r,{httpOnly:!0,secure:!0,maxAge:604800,path:"/",domain:".monetizr.com"}),T.NextResponse.json({success:!0,user:{id:s.id,email:s.email,name:s.name,role:s.role}})}catch(e){return console.error("Login error:",e),T.NextResponse.json({error:e.message||"Terjadi kesalahan saat login"},{status:500})}}let p=new r.AppRouteRouteModule({definition:{kind:E.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"export",userland:s}),{workAsyncStorage:L,workUnitAsyncStorage:l,serverHooks:R}=p;function _(){return(0,i.patchFetch)({workAsyncStorage:L,workUnitAsyncStorage:l})}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},7032:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[191,744,692],()=>a(4135));module.exports=s})();
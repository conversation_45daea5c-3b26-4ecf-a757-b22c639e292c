"use strict";(()=>{var e={};e.id=492,e.ids=[492],e.modules={491:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>x,tree:()=>p});var n=t(4332),o=t(8819),s=t(7851),a=t.n(s),d=t(7540),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(r,i);let p={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,9033,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,2341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],l={require:t,loadChunk:()=>Promise.resolve()},x=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[191,77,405],()=>t(491));module.exports=n})();
[{"name": "generate-buildid", "duration": 335, "timestamp": 810563, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750321248097, "traceId": "5f717e85b4d44510"}, {"name": "load-custom-routes", "duration": 640, "timestamp": 811004, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750321248097, "traceId": "5f717e85b4d44510"}, {"name": "create-dist-dir", "duration": 1769, "timestamp": 892788, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750321248179, "traceId": "5f717e85b4d44510"}, {"name": "create-pages-mapping", "duration": 420, "timestamp": 898372, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750321248185, "traceId": "5f717e85b4d44510"}, {"name": "collect-app-paths", "duration": 1575, "timestamp": 898817, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750321248185, "traceId": "5f717e85b4d44510"}, {"name": "create-app-mapping", "duration": 10894, "timestamp": 900412, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750321248187, "traceId": "5f717e85b4d44510"}, {"name": "public-dir-conflict-check", "duration": 698, "timestamp": 911728, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750321248198, "traceId": "5f717e85b4d44510"}, {"name": "generate-routes-manifest", "duration": 2654, "timestamp": 912640, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750321248199, "traceId": "5f717e85b4d44510"}, {"name": "next-build", "duration": 6650428, "timestamp": 591543, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.3", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1750321247878, "traceId": "5f717e85b4d44510"}]
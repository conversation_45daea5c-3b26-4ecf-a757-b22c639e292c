(()=>{var e={};e.id=381,e.ids=[381],e.modules={663:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var t=a(3486),r=a(159),i=a(5626),n=a(5519),l=a(1507),d=a(4665);let c=l.Ik({title:l.Yj().min(1,"Judul kampanye tidak boleh kosong"),description:l.Yj().min(10,"Deskripsi harus minimal 10 karakter"),budget:l.ai().min(1e4,"Budget minimal Rp 10.000"),pricePerView:l.ai().min(100,"Harga per tampilan minimal Rp 100"),requirements:l.Yj().optional(),materialUrl:l.Yj().url("URL materi tidak valid").optional()});function o({onSuccess:e}){let[s,a]=(0,r.useState)(!1),{register:l,handleSubmit:o,formState:{errors:p},reset:m}=(0,i.mN)({resolver:(0,n.u)(c)}),u=async s=>{a(!0);try{let a=await fetch("/api/campaigns",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok){let e=await a.json();throw Error(e.message||"Gagal membuat kampanye")}alert("Kampanye berhasil dibuat!"),m(),e?.()}catch(e){alert(e.message||"Terjadi kesalahan")}finally{a(!1)}};return(0,t.jsxs)(d.Zp,{className:"w-full max-w-2xl",children:[(0,t.jsx)(d.aR,{children:(0,t.jsx)(d.ZB,{children:"Buat Kampanye Baru"})}),(0,t.jsx)(d.Wu,{children:(0,t.jsxs)("form",{onSubmit:o(u),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.JU,{htmlFor:"title",children:"Judul Kampanye"}),(0,t.jsx)(d.pd,{id:"title",...l("title"),placeholder:"Masukkan judul kampanye"}),p.title&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:p.title.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.JU,{htmlFor:"description",children:"Deskripsi"}),(0,t.jsx)("textarea",{id:"description",...l("description"),placeholder:"Jelaskan kampanye Anda",className:"w-full min-h-[100px] px-3 py-2 border border-input rounded-md"}),p.description&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:p.description.message})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.JU,{htmlFor:"budget",children:"Budget (Rp)"}),(0,t.jsx)(d.pd,{id:"budget",type:"number",...l("budget",{valueAsNumber:!0}),placeholder:"100000"}),p.budget&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:p.budget.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.JU,{htmlFor:"pricePerView",children:"Harga per Tampilan (Rp)"}),(0,t.jsx)(d.pd,{id:"pricePerView",type:"number",...l("pricePerView",{valueAsNumber:!0}),placeholder:"150"}),p.pricePerView&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:p.pricePerView.message})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.JU,{htmlFor:"requirements",children:"Persyaratan (Opsional)"}),(0,t.jsx)("textarea",{id:"requirements",...l("requirements"),placeholder:"Persyaratan khusus untuk promotor",className:"w-full min-h-[80px] px-3 py-2 border border-input rounded-md"}),p.requirements&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:p.requirements.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.JU,{htmlFor:"materialUrl",children:"URL Materi (Opsional)"}),(0,t.jsx)(d.pd,{id:"materialUrl",...l("materialUrl"),placeholder:"https://drive.google.com/..."}),p.materialUrl&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:p.materialUrl.message})]}),(0,t.jsx)(d.$n,{type:"submit",className:"w-full",disabled:s,children:s?"Membuat...":"Buat Kampanye"})]})})]})}function p(){let[e,s]=(0,r.useState)([]),[a,i]=(0,r.useState)(!1),[n,l]=(0,r.useState)(!0),c=async()=>{try{let e=await fetch("/api/campaigns");if(e.ok){let a=await e.json();s(a.campaigns)}}catch(e){console.error("Failed to fetch campaigns:",e)}finally{l(!1)}},p=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),m=e=>new Date(e).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"});return n?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex justify-between items-center",children:(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Kampanye"})}),(0,t.jsx)("div",{className:"text-center py-8",children:"Loading..."})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Kampanye"}),(0,t.jsx)(d.$n,{onClick:()=>i(!a),children:a?"Tutup Form":"Buat Kampanye Baru"})]}),a&&(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(o,{onSuccess:()=>{i(!1),c()}})}),(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:0===e.length?(0,t.jsx)("div",{className:"col-span-full text-center py-8 text-muted-foreground",children:"Belum ada kampanye. Buat kampanye pertama Anda!"}):e.map(e=>(0,t.jsxs)(d.Zp,{children:[(0,t.jsx)(d.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsx)(d.ZB,{className:"text-lg",children:e.title}),(0,t.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"active"===e.status?"bg-green-100 text-green-800":"draft"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:"active"===e.status?"Aktif":"draft"===e.status?"Draft":e.status})]})}),(0,t.jsxs)(d.Wu,{className:"space-y-3",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:e.description}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Budget:"}),(0,t.jsx)("span",{className:"font-medium",children:p(e.budget)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Per Tampilan:"}),(0,t.jsx)("span",{className:"font-medium",children:p(e.pricePerView)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Dibuat:"}),(0,t.jsx)("span",{children:m(e.created_at)})]})]}),(0,t.jsx)("div",{className:"pt-2",children:(0,t.jsx)(d.$n,{variant:"outline",size:"sm",className:"w-full",children:"Lihat Detail"})})]})]},e.id))})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1745:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(3952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\apps\\\\dashboard\\\\src\\\\app\\\\dashboard\\\\campaigns\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\campaigns\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5389:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=a(4332),r=a(8819),i=a(7851),n=a.n(i),l=a(7540),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(s,d);let c={children:["",{children:["dashboard",{children:["campaigns",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1745)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\campaigns\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,2341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\campaigns\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/campaigns/page",pathname:"/dashboard/campaigns",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6401:(e,s,a)=>{Promise.resolve().then(a.bind(a,1745))},6673:(e,s,a)=>{Promise.resolve().then(a.bind(a,663))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9699:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});var t=a(1253);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[191,77,253,330,405],()=>a(5389));module.exports=t})();
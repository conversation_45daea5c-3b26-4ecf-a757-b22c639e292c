{"version": 3, "sources": ["../../../node_modules/unenv/dist/runtime/_internal/utils.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/perf_hooks/performance.mjs", "../../../node_modules/unenv/dist/runtime/node/perf_hooks.mjs", "../../../node_modules/@cloudflare/unenv-preset/dist/runtime/polyfill/performance.mjs", "../../../node_modules/unenv/dist/runtime/mock/noop.mjs", "../../../node_modules/unenv/dist/runtime/node/console.mjs", "../../../node_modules/@cloudflare/unenv-preset/dist/runtime/node/console.mjs", "../../../node_modules/wrangler/_virtual_unenv_global_polyfill-@cloudflare-unenv-preset-node-console", "../../../node_modules/unenv/dist/runtime/node/internal/process/hrtime.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/tty/write-stream.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/tty/read-stream.mjs", "../../../node_modules/unenv/dist/runtime/node/tty.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/process/node-version.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/process/process.mjs", "../../../node_modules/@cloudflare/unenv-preset/dist/runtime/node/process.mjs", "../../../node_modules/wrangler/_virtual_unenv_global_polyfill-@cloudflare-unenv-preset-node-process", "wrangler-modules-watch:wrangler:modules-watch", "../../../node_modules/wrangler/templates/modules-watch-stub.js", "../../../node_modules/unenv/dist/runtime/node/internal/crypto/web.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/crypto/node.mjs", "../../../node_modules/unenv/dist/runtime/node/crypto.mjs", "../../../node_modules/@cloudflare/unenv-preset/dist/runtime/node/crypto.mjs", "node-built-in-modules:crypto", "../../../node_modules/bcryptjs/dist/bcrypt.js", "../bundle-fNTuON/middleware-loader.entry.ts", "../bundle-fNTuON/middleware-insertion-facade.js", "../../../src/index.ts", "../../../node_modules/hono/dist/index.js", "../../../node_modules/hono/dist/hono.js", "../../../node_modules/hono/dist/hono-base.js", "../../../node_modules/hono/dist/compose.js", "../../../node_modules/hono/dist/context.js", "../../../node_modules/hono/dist/request.js", "../../../node_modules/hono/dist/request/constants.js", "../../../node_modules/hono/dist/utils/body.js", "../../../node_modules/hono/dist/utils/url.js", "../../../node_modules/hono/dist/utils/html.js", "../../../node_modules/hono/dist/router.js", "../../../node_modules/hono/dist/utils/constants.js", "../../../node_modules/hono/dist/router/reg-exp-router/index.js", "../../../node_modules/hono/dist/router/reg-exp-router/router.js", "../../../node_modules/hono/dist/router/reg-exp-router/node.js", "../../../node_modules/hono/dist/router/reg-exp-router/trie.js", "../../../node_modules/hono/dist/router/smart-router/index.js", "../../../node_modules/hono/dist/router/smart-router/router.js", "../../../node_modules/hono/dist/router/trie-router/index.js", "../../../node_modules/hono/dist/router/trie-router/router.js", "../../../node_modules/hono/dist/router/trie-router/node.js", "../../../node_modules/hono/dist/middleware/cors/index.js", "../../../src/routes/auth.ts", "../../../node_modules/hono/dist/middleware/jwt/index.js", "../../../node_modules/hono/dist/middleware/jwt/jwt.js", "../../../node_modules/hono/dist/helper/cookie/index.js", "../../../node_modules/hono/dist/utils/cookie.js", "../../../node_modules/hono/dist/http-exception.js", "../../../node_modules/hono/dist/utils/jwt/index.js", "../../../node_modules/hono/dist/utils/jwt/jwt.js", "../../../node_modules/hono/dist/utils/encode.js", "../../../node_modules/hono/dist/utils/jwt/jwa.js", "../../../node_modules/hono/dist/utils/jwt/jws.js", "../../../node_modules/hono/dist/helper/adapter/index.js", "../../../node_modules/hono/dist/utils/jwt/types.js", "../../../node_modules/hono/dist/utils/jwt/utf8.js", "../../../src/routes/user.ts", "../../../src/routes/campaigns.ts", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../../../node_modules/wrangler/templates/middleware/common.ts"], "sourceRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\workers\\api\\.wrangler\\tmp\\dev-v494U8", "sourcesContent": ["/* @__NO_SIDE_EFFECTS__ */\nexport function rawHeaders(headers) {\n\tconst rawHeaders = [];\n\tfor (const key in headers) {\n\t\tif (Array.isArray(headers[key])) {\n\t\t\tfor (const h of headers[key]) {\n\t\t\t\trawHeaders.push(key, h);\n\t\t\t}\n\t\t} else {\n\t\t\trawHeaders.push(key, headers[key]);\n\t\t}\n\t}\n\treturn rawHeaders;\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function mergeFns(...functions) {\n\treturn function(...args) {\n\t\tfor (const fn of functions) {\n\t\t\tfn(...args);\n\t\t}\n\t};\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function createNotImplementedError(name) {\n\treturn new Error(`[unenv] ${name} is not implemented yet!`);\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplemented(name) {\n\tconst fn = () => {\n\t\tthrow createNotImplementedError(name);\n\t};\n\treturn Object.assign(fn, { __unenv__: true });\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplementedAsync(name) {\n\tconst fn = notImplemented(name);\n\tfn.__promisify__ = () => notImplemented(name + \".__promisify__\");\n\tfn.native = fn;\n\treturn fn;\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplementedClass(name) {\n\treturn class {\n\t\t__unenv__ = true;\n\t\tconstructor() {\n\t\t\tthrow new Error(`[unenv] ${name} is not implemented yet!`);\n\t\t}\n\t};\n}\n", "import { createNotImplementedError } from \"../../../_internal/utils.mjs\";\nconst _timeOrigin = globalThis.performance?.timeOrigin ?? Date.now();\nconst _performanceNow = globalThis.performance?.now ? globalThis.performance.now.bind(globalThis.performance) : () => Date.now() - _timeOrigin;\nconst nodeTiming = {\n\tname: \"node\",\n\tentryType: \"node\",\n\tstartTime: 0,\n\tduration: 0,\n\tnodeStart: 0,\n\tv8Start: 0,\n\tbootstrapComplete: 0,\n\tenvironment: 0,\n\tloopStart: 0,\n\tloopExit: 0,\n\tidleTime: 0,\n\tuvMetricsInfo: {\n\t\tloopCount: 0,\n\t\tevents: 0,\n\t\teventsWaiting: 0\n\t},\n\tdetail: undefined,\n\ttoJSON() {\n\t\treturn this;\n\t}\n};\nexport class PerformanceEntry {\n\t__unenv__ = true;\n\tdetail;\n\tentryType = \"event\";\n\tname;\n\tstartTime;\n\tconstructor(name, options) {\n\t\tthis.name = name;\n\t\tthis.startTime = options?.startTime || _performanceNow();\n\t\tthis.detail = options?.detail;\n\t}\n\tget duration() {\n\t\treturn _performanceNow() - this.startTime;\n\t}\n\ttoJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tentryType: this.entryType,\n\t\t\tstartTime: this.startTime,\n\t\t\tduration: this.duration,\n\t\t\tdetail: this.detail\n\t\t};\n\t}\n}\nexport const PerformanceMark = class PerformanceMark extends PerformanceEntry {\n\tentryType = \"mark\";\n\tconstructor() {\n\t\tsuper(...arguments);\n\t}\n\tget duration() {\n\t\treturn 0;\n\t}\n};\nexport class PerformanceMeasure extends PerformanceEntry {\n\tentryType = \"measure\";\n}\nexport class PerformanceResourceTiming extends PerformanceEntry {\n\tentryType = \"resource\";\n\tserverTiming = [];\n\tconnectEnd = 0;\n\tconnectStart = 0;\n\tdecodedBodySize = 0;\n\tdomainLookupEnd = 0;\n\tdomainLookupStart = 0;\n\tencodedBodySize = 0;\n\tfetchStart = 0;\n\tinitiatorType = \"\";\n\tname = \"\";\n\tnextHopProtocol = \"\";\n\tredirectEnd = 0;\n\tredirectStart = 0;\n\trequestStart = 0;\n\tresponseEnd = 0;\n\tresponseStart = 0;\n\tsecureConnectionStart = 0;\n\tstartTime = 0;\n\ttransferSize = 0;\n\tworkerStart = 0;\n\tresponseStatus = 0;\n}\nexport class PerformanceObserverEntryList {\n\t__unenv__ = true;\n\tgetEntries() {\n\t\treturn [];\n\t}\n\tgetEntriesByName(_name, _type) {\n\t\treturn [];\n\t}\n\tgetEntriesByType(type) {\n\t\treturn [];\n\t}\n}\nexport class Performance {\n\t__unenv__ = true;\n\ttimeOrigin = _timeOrigin;\n\teventCounts = new Map();\n\t_entries = [];\n\t_resourceTimingBufferSize = 0;\n\tnavigation = undefined;\n\ttiming = undefined;\n\ttimerify(_fn, _options) {\n\t\tthrow createNotImplementedError(\"Performance.timerify\");\n\t}\n\tget nodeTiming() {\n\t\treturn nodeTiming;\n\t}\n\teventLoopUtilization() {\n\t\treturn {};\n\t}\n\tmarkResourceTiming() {\n\t\treturn new PerformanceResourceTiming(\"\");\n\t}\n\tonresourcetimingbufferfull = null;\n\tnow() {\n\t\tif (this.timeOrigin === _timeOrigin) {\n\t\t\treturn _performanceNow();\n\t\t}\n\t\treturn Date.now() - this.timeOrigin;\n\t}\n\tclearMarks(markName) {\n\t\tthis._entries = markName ? this._entries.filter((e) => e.name !== markName) : this._entries.filter((e) => e.entryType !== \"mark\");\n\t}\n\tclearMeasures(measureName) {\n\t\tthis._entries = measureName ? this._entries.filter((e) => e.name !== measureName) : this._entries.filter((e) => e.entryType !== \"measure\");\n\t}\n\tclearResourceTimings() {\n\t\tthis._entries = this._entries.filter((e) => e.entryType !== \"resource\" || e.entryType !== \"navigation\");\n\t}\n\tgetEntries() {\n\t\treturn this._entries;\n\t}\n\tgetEntriesByName(name, type) {\n\t\treturn this._entries.filter((e) => e.name === name && (!type || e.entryType === type));\n\t}\n\tgetEntriesByType(type) {\n\t\treturn this._entries.filter((e) => e.entryType === type);\n\t}\n\tmark(name, options) {\n\t\tconst entry = new PerformanceMark(name, options);\n\t\tthis._entries.push(entry);\n\t\treturn entry;\n\t}\n\tmeasure(measureName, startOrMeasureOptions, endMark) {\n\t\tlet start;\n\t\tlet end;\n\t\tif (typeof startOrMeasureOptions === \"string\") {\n\t\t\tstart = this.getEntriesByName(startOrMeasureOptions, \"mark\")[0]?.startTime;\n\t\t\tend = this.getEntriesByName(endMark, \"mark\")[0]?.startTime;\n\t\t} else {\n\t\t\tstart = Number.parseFloat(startOrMeasureOptions?.start) || this.now();\n\t\t\tend = Number.parseFloat(startOrMeasureOptions?.end) || this.now();\n\t\t}\n\t\tconst entry = new PerformanceMeasure(measureName, {\n\t\t\tstartTime: start,\n\t\t\tdetail: {\n\t\t\t\tstart,\n\t\t\t\tend\n\t\t\t}\n\t\t});\n\t\tthis._entries.push(entry);\n\t\treturn entry;\n\t}\n\tsetResourceTimingBufferSize(maxSize) {\n\t\tthis._resourceTimingBufferSize = maxSize;\n\t}\n\taddEventListener(type, listener, options) {\n\t\tthrow createNotImplementedError(\"Performance.addEventListener\");\n\t}\n\tremoveEventListener(type, listener, options) {\n\t\tthrow createNotImplementedError(\"Performance.removeEventListener\");\n\t}\n\tdispatchEvent(event) {\n\t\tthrow createNotImplementedError(\"Performance.dispatchEvent\");\n\t}\n\ttoJSON() {\n\t\treturn this;\n\t}\n}\nexport class PerformanceObserver {\n\t__unenv__ = true;\n\tstatic supportedEntryTypes = [];\n\t_callback = null;\n\tconstructor(callback) {\n\t\tthis._callback = callback;\n\t}\n\ttakeRecords() {\n\t\treturn [];\n\t}\n\tdisconnect() {\n\t\tthrow createNotImplementedError(\"PerformanceObserver.disconnect\");\n\t}\n\tobserve(options) {\n\t\tthrow createNotImplementedError(\"PerformanceObserver.observe\");\n\t}\n\tbind(fn) {\n\t\treturn fn;\n\t}\n\trunInAsyncScope(fn, thisArg, ...args) {\n\t\treturn fn.call(thisArg, ...args);\n\t}\n\tasyncId() {\n\t\treturn 0;\n\t}\n\ttriggerAsyncId() {\n\t\treturn 0;\n\t}\n\temitDestroy() {\n\t\treturn this;\n\t}\n}\nexport const performance = globalThis.performance && \"addEventListener\" in globalThis.performance ? globalThis.performance : new Performance();\n", "import { IntervalHistogram, RecordableHistogram } from \"./internal/perf_hooks/histogram.mjs\";\nimport { performance, Performance, PerformanceEntry, PerformanceMark, PerformanceMeasure, PerformanceObserverEntryList, PerformanceObserver, PerformanceResourceTiming } from \"./internal/perf_hooks/performance.mjs\";\nexport * from \"./internal/perf_hooks/performance.mjs\";\nimport { NODE_PERFORMANCE_GC_MAJOR, NODE_PERFORMANCE_GC_MINOR, NODE_PERFORMANCE_GC_INCREMENTAL, NODE_PERFORMANCE_GC_WEAKCB, NODE_PERFORMANCE_GC_FLAGS_NO, NODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED, NODE_PERFORMANCE_GC_FLAGS_FORCED, NODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING, NODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE, NODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY, NODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE, NODE_PERFORMANCE_ENTRY_TYPE_GC, NODE_PERFORMANCE_ENTRY_TYPE_HTTP, NODE_PERFORMANCE_ENTRY_TYPE_HTTP2, NODE_PERFORMANCE_ENTRY_TYPE_NET, NODE_PERFORMANCE_ENTRY_TYPE_DNS, NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP, NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN, NODE_PERFORMANCE_MILESTONE_ENVIRONMENT, NODE_PERFORMANCE_MILESTONE_NODE_START, NODE_PERFORMANCE_MILESTONE_V8_START, NODE_PERFORMANCE_MILESTONE_LOOP_START, NODE_PERFORMANCE_MILESTONE_LOOP_EXIT, NODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE } from \"./internal/perf_hooks/constants.mjs\";\nexport const constants = {\n\tNODE_PERFORMANCE_GC_MAJOR,\n\tNODE_PERFORMANCE_GC_MINOR,\n\tNODE_PERFORMANCE_GC_INCREMENTAL,\n\tNODE_PERFORMANCE_GC_WEAKCB,\n\tNODE_PERFORMANCE_GC_FLAGS_NO,\n\tNODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED,\n\tNODE_PERFORMANCE_GC_FLAGS_FORCED,\n\tNODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING,\n\tNODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE,\n\tNODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY,\n\tNODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE,\n\tNODE_PERFORMANCE_ENTRY_TYPE_GC,\n\tNODE_PERFORMANCE_ENTRY_TYPE_HTTP,\n\tNODE_PERFORMANCE_ENTRY_TYPE_HTTP2,\n\tNODE_PERFORMANCE_ENTRY_TYPE_NET,\n\tNODE_PERFORMANCE_ENTRY_TYPE_DNS,\n\tNODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP,\n\tNODE_PERFORMANCE_MILESTONE_TIME_ORIGIN,\n\tNODE_PERFORMANCE_MILESTONE_ENVIRONMENT,\n\tNODE_PERFORMANCE_MILESTONE_NODE_START,\n\tNODE_PERFORMANCE_MILESTONE_V8_START,\n\tNODE_PERFORMANCE_MILESTONE_LOOP_START,\n\tNODE_PERFORMANCE_MILESTONE_LOOP_EXIT,\n\tNODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE\n};\nexport const monitorEventLoopDelay = function(_options) {\n\treturn new IntervalHistogram();\n};\nexport const createHistogram = function(_options) {\n\treturn new RecordableHistogram();\n};\nexport default {\n\tPerformance,\n\tPerformanceMark,\n\tPerformanceEntry,\n\tPerformanceMeasure,\n\tPerformanceObserverEntryList,\n\tPerformanceObserver,\n\tPerformanceResourceTiming,\n\tperformance,\n\tconstants,\n\tcreateHistogram,\n\tmonitorEventLoopDelay\n};\n", "import {\n  performance,\n  Performance,\n  PerformanceEntry,\n  PerformanceMark,\n  PerformanceMeasure,\n  PerformanceObserver,\n  PerformanceObserverEntryList,\n  PerformanceResourceTiming\n} from \"node:perf_hooks\";\nglobalThis.performance = performance;\nglobalThis.Performance = Performance;\nglobalThis.PerformanceEntry = PerformanceEntry;\nglobalThis.PerformanceMark = PerformanceMark;\nglobalThis.PerformanceMeasure = PerformanceMeasure;\nglobalThis.PerformanceObserver = PerformanceObserver;\nglobalThis.PerformanceObserverEntryList = PerformanceObserverEntryList;\nglobalThis.PerformanceResourceTiming = PerformanceResourceTiming;\n", "export default Object.assign(() => {}, { __unenv__: true });\n", "import { Writable } from \"node:stream\";\nimport noop from \"../mock/noop.mjs\";\nimport { notImplemented, notImplementedClass } from \"../_internal/utils.mjs\";\nconst _console = globalThis.console;\nexport const _ignoreErrors = true;\nexport const _stderr = new Writable();\nexport const _stdout = new Writable();\nexport const log = _console?.log ?? noop;\nexport const info = _console?.info ?? log;\nexport const trace = _console?.trace ?? info;\nexport const debug = _console?.debug ?? log;\nexport const table = _console?.table ?? log;\nexport const error = _console?.error ?? log;\nexport const warn = _console?.warn ?? error;\nexport const createTask = _console?.createTask ?? /* @__PURE__ */ notImplemented(\"console.createTask\");\nexport const assert = /* @__PURE__ */ notImplemented(\"console.assert\");\nexport const clear = _console?.clear ?? noop;\nexport const count = _console?.count ?? noop;\nexport const countReset = _console?.countReset ?? noop;\nexport const dir = _console?.dir ?? noop;\nexport const dirxml = _console?.dirxml ?? noop;\nexport const group = _console?.group ?? noop;\nexport const groupEnd = _console?.groupEnd ?? noop;\nexport const groupCollapsed = _console?.groupCollapsed ?? noop;\nexport const profile = _console?.profile ?? noop;\nexport const profileEnd = _console?.profileEnd ?? noop;\nexport const time = _console?.time ?? noop;\nexport const timeEnd = _console?.timeEnd ?? noop;\nexport const timeLog = _console?.timeLog ?? noop;\nexport const timeStamp = _console?.timeStamp ?? noop;\nexport const Console = _console?.Console ?? /* @__PURE__ */ notImplementedClass(\"console.Console\");\nexport const _times = /* @__PURE__ */ new Map();\nexport function context() {\n\treturn _console;\n}\nexport const _stdoutErrorHandler = noop;\nexport const _stderrErrorHandler = noop;\nexport default {\n\t_times,\n\t_ignoreErrors,\n\t_stdoutErrorHandler,\n\t_stderrErrorHandler,\n\t_stdout,\n\t_stderr,\n\tassert,\n\tclear,\n\tConsole,\n\tcount,\n\tcountReset,\n\tdebug,\n\tdir,\n\tdirxml,\n\terror,\n\tcontext,\n\tcreateTask,\n\tgroup,\n\tgroupEnd,\n\tgroupCollapsed,\n\tinfo,\n\tlog,\n\tprofile,\n\tprofileEnd,\n\ttable,\n\ttime,\n\ttimeEnd,\n\ttimeLog,\n\ttimeStamp,\n\ttrace,\n\twarn\n};\n", "import {\n  _ignoreErrors,\n  _stderr,\n  _stderr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  _stdout,\n  _stdoutError<PERSON>and<PERSON>,\n  _times,\n  Console\n} from \"unenv/node/console\";\nexport {\n  Console,\n  _ignoreErrors,\n  _stderr,\n  _stderrError<PERSON><PERSON><PERSON>,\n  _stdout,\n  _stdoutErrorHandler,\n  _times\n} from \"unenv/node/console\";\nconst workerdConsole = globalThis[\"console\"];\nexport const {\n  assert,\n  clear,\n  // @ts-expect-error undocumented public API\n  context,\n  count,\n  countReset,\n  // @ts-expect-error undocumented public API\n  createTask,\n  debug,\n  dir,\n  dirxml,\n  error,\n  group,\n  groupCollapsed,\n  groupEnd,\n  info,\n  log,\n  profile,\n  profileEnd,\n  table,\n  time,\n  timeEnd,\n  timeLog,\n  timeStamp,\n  trace,\n  warn\n} = workerdConsole;\nObject.assign(workerdConsole, {\n  Console,\n  _ignoreErrors,\n  _stderr,\n  _stderrError<PERSON>and<PERSON>,\n  _stdout,\n  _stdoutErrorHandler,\n  _times\n});\nexport default workerdConsole;\n", "import { default as defaultExport } from \"@cloudflare/unenv-preset/node/console\";\nglobalThis.console = defaultExport;", "export const hrtime = /* @__PURE__ */ Object.assign(function hrtime(startTime) {\n\tconst now = Date.now();\n\tconst seconds = Math.trunc(now / 1e3);\n\tconst nanos = now % 1e3 * 1e6;\n\tif (startTime) {\n\t\tlet diffSeconds = seconds - startTime[0];\n\t\tlet diffNanos = nanos - startTime[0];\n\t\tif (diffNanos < 0) {\n\t\t\tdiffSeconds = diffSeconds - 1;\n\t\t\tdiffNanos = 1e9 + diffNanos;\n\t\t}\n\t\treturn [diffSeconds, diffNanos];\n\t}\n\treturn [seconds, nanos];\n}, { bigint: function bigint() {\n\treturn BigInt(Date.now() * 1e6);\n} });\n", "export class WriteStream {\n\tfd;\n\tcolumns = 80;\n\trows = 24;\n\tisTTY = false;\n\tconstructor(fd) {\n\t\tthis.fd = fd;\n\t}\n\tclearLine(dir, callback) {\n\t\tcallback && callback();\n\t\treturn false;\n\t}\n\tclearScreenDown(callback) {\n\t\tcallback && callback();\n\t\treturn false;\n\t}\n\tcursorTo(x, y, callback) {\n\t\tcallback && typeof callback === \"function\" && callback();\n\t\treturn false;\n\t}\n\tmoveCursor(dx, dy, callback) {\n\t\tcallback && callback();\n\t\treturn false;\n\t}\n\tgetColorDepth(env) {\n\t\treturn 1;\n\t}\n\thasColors(count, env) {\n\t\treturn false;\n\t}\n\tgetWindowSize() {\n\t\treturn [this.columns, this.rows];\n\t}\n\twrite(str, encoding, cb) {\n\t\tif (str instanceof Uint8Array) {\n\t\t\tstr = new TextDecoder().decode(str);\n\t\t}\n\t\ttry {\n\t\t\tconsole.log(str);\n\t\t} catch {}\n\t\tcb && typeof cb === \"function\" && cb();\n\t\treturn false;\n\t}\n}\n", "export class ReadStream {\n\tfd;\n\tisRaw = false;\n\tisTTY = false;\n\tconstructor(fd) {\n\t\tthis.fd = fd;\n\t}\n\tsetRawMode(mode) {\n\t\tthis.isRaw = mode;\n\t\treturn this;\n\t}\n}\n", "import { WriteStream } from \"./internal/tty/write-stream.mjs\";\nexport { ReadStream } from \"./internal/tty/read-stream.mjs\";\nexport { WriteStream } from \"./internal/tty/write-stream.mjs\";\nexport const isatty = function() {\n\treturn false;\n};\nexport default {\n\tReadStream: WriteStream,\n\tWriteStream,\n\tisatty\n};\n", "export const NODE_VERSION = \"22.14.0\";\n", "import { EventEmitter } from \"node:events\";\nimport { ReadStream, WriteStream } from \"node:tty\";\nimport { notImplemented, createNotImplementedError } from \"../../../_internal/utils.mjs\";\nimport { NODE_VERSION } from \"./node-version.mjs\";\nexport class Process extends EventEmitter {\n\tenv;\n\thrtime;\n\tnextTick;\n\tconstructor(impl) {\n\t\tsuper();\n\t\tthis.env = impl.env;\n\t\tthis.hrtime = impl.hrtime;\n\t\tthis.nextTick = impl.nextTick;\n\t\tfor (const prop of [...Object.getOwnPropertyNames(Process.prototype), ...Object.getOwnPropertyNames(EventEmitter.prototype)]) {\n\t\t\tconst value = this[prop];\n\t\t\tif (typeof value === \"function\") {\n\t\t\t\tthis[prop] = value.bind(this);\n\t\t\t}\n\t\t}\n\t}\n\temitWarning(warning, type, code) {\n\t\tconsole.warn(`${code ? `[${code}] ` : \"\"}${type ? `${type}: ` : \"\"}${warning}`);\n\t}\n\temit(...args) {\n\t\treturn super.emit(...args);\n\t}\n\tlisteners(eventName) {\n\t\treturn super.listeners(eventName);\n\t}\n\t#stdin;\n\t#stdout;\n\t#stderr;\n\tget stdin() {\n\t\treturn this.#stdin ??= new ReadStream(0);\n\t}\n\tget stdout() {\n\t\treturn this.#stdout ??= new WriteStream(1);\n\t}\n\tget stderr() {\n\t\treturn this.#stderr ??= new WriteStream(2);\n\t}\n\t#cwd = \"/\";\n\tchdir(cwd) {\n\t\tthis.#cwd = cwd;\n\t}\n\tcwd() {\n\t\treturn this.#cwd;\n\t}\n\tarch = \"\";\n\tplatform = \"\";\n\targv = [];\n\targv0 = \"\";\n\texecArgv = [];\n\texecPath = \"\";\n\ttitle = \"\";\n\tpid = 200;\n\tppid = 100;\n\tget version() {\n\t\treturn `v${NODE_VERSION}`;\n\t}\n\tget versions() {\n\t\treturn { node: NODE_VERSION };\n\t}\n\tget allowedNodeEnvironmentFlags() {\n\t\treturn new Set();\n\t}\n\tget sourceMapsEnabled() {\n\t\treturn false;\n\t}\n\tget debugPort() {\n\t\treturn 0;\n\t}\n\tget throwDeprecation() {\n\t\treturn false;\n\t}\n\tget traceDeprecation() {\n\t\treturn false;\n\t}\n\tget features() {\n\t\treturn {};\n\t}\n\tget release() {\n\t\treturn {};\n\t}\n\tget connected() {\n\t\treturn false;\n\t}\n\tget config() {\n\t\treturn {};\n\t}\n\tget moduleLoadList() {\n\t\treturn [];\n\t}\n\tconstrainedMemory() {\n\t\treturn 0;\n\t}\n\tavailableMemory() {\n\t\treturn 0;\n\t}\n\tuptime() {\n\t\treturn 0;\n\t}\n\tresourceUsage() {\n\t\treturn {};\n\t}\n\tref() {}\n\tunref() {}\n\tumask() {\n\t\tthrow createNotImplementedError(\"process.umask\");\n\t}\n\tgetBuiltinModule() {\n\t\treturn undefined;\n\t}\n\tgetActiveResourcesInfo() {\n\t\tthrow createNotImplementedError(\"process.getActiveResourcesInfo\");\n\t}\n\texit() {\n\t\tthrow createNotImplementedError(\"process.exit\");\n\t}\n\treallyExit() {\n\t\tthrow createNotImplementedError(\"process.reallyExit\");\n\t}\n\tkill() {\n\t\tthrow createNotImplementedError(\"process.kill\");\n\t}\n\tabort() {\n\t\tthrow createNotImplementedError(\"process.abort\");\n\t}\n\tdlopen() {\n\t\tthrow createNotImplementedError(\"process.dlopen\");\n\t}\n\tsetSourceMapsEnabled() {\n\t\tthrow createNotImplementedError(\"process.setSourceMapsEnabled\");\n\t}\n\tloadEnvFile() {\n\t\tthrow createNotImplementedError(\"process.loadEnvFile\");\n\t}\n\tdisconnect() {\n\t\tthrow createNotImplementedError(\"process.disconnect\");\n\t}\n\tcpuUsage() {\n\t\tthrow createNotImplementedError(\"process.cpuUsage\");\n\t}\n\tsetUncaughtExceptionCaptureCallback() {\n\t\tthrow createNotImplementedError(\"process.setUncaughtExceptionCaptureCallback\");\n\t}\n\thasUncaughtExceptionCaptureCallback() {\n\t\tthrow createNotImplementedError(\"process.hasUncaughtExceptionCaptureCallback\");\n\t}\n\tinitgroups() {\n\t\tthrow createNotImplementedError(\"process.initgroups\");\n\t}\n\topenStdin() {\n\t\tthrow createNotImplementedError(\"process.openStdin\");\n\t}\n\tassert() {\n\t\tthrow createNotImplementedError(\"process.assert\");\n\t}\n\tbinding() {\n\t\tthrow createNotImplementedError(\"process.binding\");\n\t}\n\tpermission = { has: /* @__PURE__ */ notImplemented(\"process.permission.has\") };\n\treport = {\n\t\tdirectory: \"\",\n\t\tfilename: \"\",\n\t\tsignal: \"SIGUSR2\",\n\t\tcompact: false,\n\t\treportOnFatalError: false,\n\t\treportOnSignal: false,\n\t\treportOnUncaughtException: false,\n\t\tgetReport: /* @__PURE__ */ notImplemented(\"process.report.getReport\"),\n\t\twriteReport: /* @__PURE__ */ notImplemented(\"process.report.writeReport\")\n\t};\n\tfinalization = {\n\t\tregister: /* @__PURE__ */ notImplemented(\"process.finalization.register\"),\n\t\tunregister: /* @__PURE__ */ notImplemented(\"process.finalization.unregister\"),\n\t\tregisterBeforeExit: /* @__PURE__ */ notImplemented(\"process.finalization.registerBeforeExit\")\n\t};\n\tmemoryUsage = Object.assign(() => ({\n\t\tarrayBuffers: 0,\n\t\trss: 0,\n\t\texternal: 0,\n\t\theapTotal: 0,\n\t\theapUsed: 0\n\t}), { rss: () => 0 });\n\tmainModule = undefined;\n\tdomain = undefined;\n\tsend = undefined;\n\texitCode = undefined;\n\tchannel = undefined;\n\tgetegid = undefined;\n\tgeteuid = undefined;\n\tgetgid = undefined;\n\tgetgroups = undefined;\n\tgetuid = undefined;\n\tsetegid = undefined;\n\tseteuid = undefined;\n\tsetgid = undefined;\n\tsetgroups = undefined;\n\tsetuid = undefined;\n\t_events = undefined;\n\t_eventsCount = undefined;\n\t_exiting = undefined;\n\t_maxListeners = undefined;\n\t_debugEnd = undefined;\n\t_debugProcess = undefined;\n\t_fatalException = undefined;\n\t_getActiveHandles = undefined;\n\t_getActiveRequests = undefined;\n\t_kill = undefined;\n\t_preload_modules = undefined;\n\t_rawDebug = undefined;\n\t_startProfilerIdleNotifier = undefined;\n\t_stopProfilerIdleNotifier = undefined;\n\t_tickCallback = undefined;\n\t_disconnect = undefined;\n\t_handleQueue = undefined;\n\t_pendingMessage = undefined;\n\t_channel = undefined;\n\t_send = undefined;\n\t_linkedBinding = undefined;\n}\n", "import { hrtime as UnenvHrTime } from \"unenv/node/internal/process/hrtime\";\nimport { Process as UnenvProcess } from \"unenv/node/internal/process/process\";\nconst globalProcess = globalThis[\"process\"];\nexport const getBuiltinModule = globalProcess.getBuiltinModule;\nexport const { exit, platform, nextTick } = getBuiltinModule(\n  \"node:process\"\n);\nconst unenvProcess = new UnenvProcess({\n  env: globalProcess.env,\n  hrtime: UnenvHrTime,\n  nextTick\n});\nexport const {\n  abort,\n  addListener,\n  allowedNodeEnvironmentFlags,\n  hasUncaughtExceptionCaptureCallback,\n  setUncaughtExceptionCaptureCallback,\n  loadEnvFile,\n  sourceMapsEnabled,\n  arch,\n  argv,\n  argv0,\n  chdir,\n  config,\n  connected,\n  constrainedMemory,\n  availableMemory,\n  cpuUsage,\n  cwd,\n  debugPort,\n  dlopen,\n  disconnect,\n  emit,\n  emitWarning,\n  env,\n  eventNames,\n  execArgv,\n  execPath,\n  finalization,\n  features,\n  getActiveResourcesInfo,\n  getMaxListeners,\n  hrtime,\n  kill,\n  listeners,\n  listenerCount,\n  memoryUsage,\n  on,\n  off,\n  once,\n  pid,\n  ppid,\n  prependListener,\n  prependOnceListener,\n  rawListeners,\n  release,\n  removeAllListeners,\n  removeListener,\n  report,\n  resourceUsage,\n  setMaxListeners,\n  setSourceMapsEnabled,\n  stderr,\n  stdin,\n  stdout,\n  title,\n  throwDeprecation,\n  traceDeprecation,\n  umask,\n  uptime,\n  version,\n  versions,\n  domain,\n  initgroups,\n  moduleLoadList,\n  reallyExit,\n  openStdin,\n  assert,\n  binding,\n  send,\n  exitCode,\n  channel,\n  getegid,\n  geteuid,\n  getgid,\n  getgroups,\n  getuid,\n  setegid,\n  seteuid,\n  setgid,\n  setgroups,\n  setuid,\n  permission,\n  mainModule,\n  _events,\n  _eventsCount,\n  _exiting,\n  _maxListeners,\n  _debugEnd,\n  _debugProcess,\n  _fatalException,\n  _getActiveHandles,\n  _getActiveRequests,\n  _kill,\n  _preload_modules,\n  _rawDebug,\n  _startProfilerIdleNotifier,\n  _stopProfilerIdleNotifier,\n  _tickCallback,\n  _disconnect,\n  _handleQueue,\n  _pendingMessage,\n  _channel,\n  _send,\n  _linkedBinding\n} = unenvProcess;\nconst _process = {\n  abort,\n  addListener,\n  allowedNodeEnvironmentFlags,\n  hasUncaughtExceptionCaptureCallback,\n  setUncaughtExceptionCaptureCallback,\n  loadEnvFile,\n  sourceMapsEnabled,\n  arch,\n  argv,\n  argv0,\n  chdir,\n  config,\n  connected,\n  constrainedMemory,\n  availableMemory,\n  cpuUsage,\n  cwd,\n  debugPort,\n  dlopen,\n  disconnect,\n  emit,\n  emitWarning,\n  env,\n  eventNames,\n  execArgv,\n  execPath,\n  exit,\n  finalization,\n  features,\n  getBuiltinModule,\n  getActiveResourcesInfo,\n  getMaxListeners,\n  hrtime,\n  kill,\n  listeners,\n  listenerCount,\n  memoryUsage,\n  nextTick,\n  on,\n  off,\n  once,\n  pid,\n  platform,\n  ppid,\n  prependListener,\n  prependOnceListener,\n  rawListeners,\n  release,\n  removeAllListeners,\n  removeListener,\n  report,\n  resourceUsage,\n  setMaxListeners,\n  setSourceMapsEnabled,\n  stderr,\n  stdin,\n  stdout,\n  title,\n  throwDeprecation,\n  traceDeprecation,\n  umask,\n  uptime,\n  version,\n  versions,\n  // @ts-expect-error old API\n  domain,\n  initgroups,\n  moduleLoadList,\n  reallyExit,\n  openStdin,\n  assert,\n  binding,\n  send,\n  exitCode,\n  channel,\n  getegid,\n  geteuid,\n  getgid,\n  getgroups,\n  getuid,\n  setegid,\n  seteuid,\n  setgid,\n  setgroups,\n  setuid,\n  permission,\n  mainModule,\n  _events,\n  _eventsCount,\n  _exiting,\n  _maxListeners,\n  _debugEnd,\n  _debugProcess,\n  _fatalException,\n  _getActiveHandles,\n  _getActiveRequests,\n  _kill,\n  _preload_modules,\n  _rawDebug,\n  _startProfilerIdleNotifier,\n  _stopProfilerIdleNotifier,\n  _tickCallback,\n  _disconnect,\n  _handleQueue,\n  _pendingMessage,\n  _channel,\n  _send,\n  _linkedBinding\n};\nexport default _process;\n", "import { default as defaultExport } from \"@cloudflare/unenv-preset/node/process\";\nglobalThis.process = defaultExport;", "", "// `esbuild` doesn't support returning `watch*` options from `onStart()`\n// plugin callbacks. Instead, we define an empty virtual module that is\n// imported by this injected file. Importing the module registers watchers.\nimport \"wrangler:modules-watch\";\n", "export const subtle = globalThis.crypto?.subtle;\nexport const randomUUID = () => {\n\treturn globalThis.crypto?.randomUUID();\n};\nexport const getRandomValues = (array) => {\n\treturn globalThis.crypto?.getRandomValues(array);\n};\n", "import { notImplemented, notImplementedClass } from \"../../../_internal/utils.mjs\";\nimport { getRandomValues } from \"./web.mjs\";\nconst MAX_RANDOM_VALUE_BYTES = 65536;\nexport const webcrypto = new Proxy(globalThis.crypto, { get(_, key) {\n\tif (key === \"CryptoKey\") {\n\t\treturn globalThis.CryptoKey;\n\t}\n\tif (typeof globalThis.crypto[key] === \"function\") {\n\t\treturn globalThis.crypto[key].bind(globalThis.crypto);\n\t}\n\treturn globalThis.crypto[key];\n} });\nexport const randomBytes = (size, cb) => {\n\tconst bytes = Buffer.alloc(size, 0, undefined);\n\tfor (let generated = 0; generated < size; generated += MAX_RANDOM_VALUE_BYTES) {\n\t\tgetRandomValues(\n\t\t\t// Use subarray to get a view of the buffer\n\t\t\tUint8Array.prototype.subarray.call(bytes, generated, generated + MAX_RANDOM_VALUE_BYTES)\n);\n\t}\n\tif (typeof cb === \"function\") {\n\t\tcb(null, bytes);\n\t\treturn undefined;\n\t}\n\treturn bytes;\n};\nexport const rng = randomBytes;\nexport const prng = randomBytes;\nexport const fips = false;\nexport const checkPrime = /* @__PURE__ */ notImplemented(\"crypto.checkPrime\");\nexport const checkPrimeSync = /* @__PURE__ */ notImplemented(\"crypto.checkPrimeSync\");\n/** @deprecated */\nexport const createCipher = /* @__PURE__ */ notImplemented(\"crypto.createCipher\");\n/** @deprecated */\nexport const createDecipher = /* @__PURE__ */ notImplemented(\"crypto.createDecipher\");\nexport const pseudoRandomBytes = /* @__PURE__ */ notImplemented(\"crypto.pseudoRandomBytes\");\nexport const createCipheriv = /* @__PURE__ */ notImplemented(\"crypto.createCipheriv\");\nexport const createDecipheriv = /* @__PURE__ */ notImplemented(\"crypto.createDecipheriv\");\nexport const createDiffieHellman = /* @__PURE__ */ notImplemented(\"crypto.createDiffieHellman\");\nexport const createDiffieHellmanGroup = /* @__PURE__ */ notImplemented(\"crypto.createDiffieHellmanGroup\");\nexport const createECDH = /* @__PURE__ */ notImplemented(\"crypto.createECDH\");\nexport const createHash = /* @__PURE__ */ notImplemented(\"crypto.createHash\");\nexport const createHmac = /* @__PURE__ */ notImplemented(\"crypto.createHmac\");\nexport const createPrivateKey = /* @__PURE__ */ notImplemented(\"crypto.createPrivateKey\");\nexport const createPublicKey = /* @__PURE__ */ notImplemented(\"crypto.createPublicKey\");\nexport const createSecretKey = /* @__PURE__ */ notImplemented(\"crypto.createSecretKey\");\nexport const createSign = /* @__PURE__ */ notImplemented(\"crypto.createSign\");\nexport const createVerify = /* @__PURE__ */ notImplemented(\"crypto.createVerify\");\nexport const diffieHellman = /* @__PURE__ */ notImplemented(\"crypto.diffieHellman\");\nexport const generatePrime = /* @__PURE__ */ notImplemented(\"crypto.generatePrime\");\nexport const generatePrimeSync = /* @__PURE__ */ notImplemented(\"crypto.generatePrimeSync\");\nexport const getCiphers = /* @__PURE__ */ notImplemented(\"crypto.getCiphers\");\nexport const getCipherInfo = /* @__PURE__ */ notImplemented(\"crypto.getCipherInfo\");\nexport const getCurves = /* @__PURE__ */ notImplemented(\"crypto.getCurves\");\nexport const getDiffieHellman = /* @__PURE__ */ notImplemented(\"crypto.getDiffieHellman\");\nexport const getHashes = /* @__PURE__ */ notImplemented(\"crypto.getHashes\");\nexport const hkdf = /* @__PURE__ */ notImplemented(\"crypto.hkdf\");\nexport const hkdfSync = /* @__PURE__ */ notImplemented(\"crypto.hkdfSync\");\nexport const pbkdf2 = /* @__PURE__ */ notImplemented(\"crypto.pbkdf2\");\nexport const pbkdf2Sync = /* @__PURE__ */ notImplemented(\"crypto.pbkdf2Sync\");\nexport const generateKeyPair = /* @__PURE__ */ notImplemented(\"crypto.generateKeyPair\");\nexport const generateKeyPairSync = /* @__PURE__ */ notImplemented(\"crypto.generateKeyPairSync\");\nexport const generateKey = /* @__PURE__ */ notImplemented(\"crypto.generateKey\");\nexport const generateKeySync = /* @__PURE__ */ notImplemented(\"crypto.generateKeySync\");\nexport const privateDecrypt = /* @__PURE__ */ notImplemented(\"crypto.privateDecrypt\");\nexport const privateEncrypt = /* @__PURE__ */ notImplemented(\"crypto.privateEncrypt\");\nexport const publicDecrypt = /* @__PURE__ */ notImplemented(\"crypto.publicDecrypt\");\nexport const publicEncrypt = /* @__PURE__ */ notImplemented(\"crypto.publicEncrypt\");\nexport const randomFill = /* @__PURE__ */ notImplemented(\"crypto.randomFill\");\nexport const randomFillSync = /* @__PURE__ */ notImplemented(\"crypto.randomFillSync\");\nexport const randomInt = /* @__PURE__ */ notImplemented(\"crypto.randomInt\");\nexport const scrypt = /* @__PURE__ */ notImplemented(\"crypto.scrypt\");\nexport const scryptSync = /* @__PURE__ */ notImplemented(\"crypto.scryptSync\");\nexport const sign = /* @__PURE__ */ notImplemented(\"crypto.sign\");\nexport const setEngine = /* @__PURE__ */ notImplemented(\"crypto.setEngine\");\nexport const timingSafeEqual = /* @__PURE__ */ notImplemented(\"crypto.timingSafeEqual\");\nexport const getFips = /* @__PURE__ */ notImplemented(\"crypto.getFips\");\nexport const setFips = /* @__PURE__ */ notImplemented(\"crypto.setFips\");\nexport const verify = /* @__PURE__ */ notImplemented(\"crypto.verify\");\nexport const secureHeapUsed = /* @__PURE__ */ notImplemented(\"crypto.secureHeapUsed\");\nexport const hash = /* @__PURE__ */ notImplemented(\"crypto.hash\");\nexport const Certificate = /* @__PURE__ */ notImplementedClass(\"crypto.Certificate\");\nexport const Cipher = /* @__PURE__ */ notImplementedClass(\"crypto.Cipher\");\nexport const Cipheriv = /* @__PURE__ */ notImplementedClass(\n\t\"crypto.Cipheriv\"\n\t// @ts-expect-error not typed yet\n);\nexport const Decipher = /* @__PURE__ */ notImplementedClass(\"crypto.Decipher\");\nexport const Decipheriv = /* @__PURE__ */ notImplementedClass(\n\t\"crypto.Decipheriv\"\n\t// @ts-expect-error not typed yet\n);\nexport const DiffieHellman = /* @__PURE__ */ notImplementedClass(\"crypto.DiffieHellman\");\nexport const DiffieHellmanGroup = /* @__PURE__ */ notImplementedClass(\"crypto.DiffieHellmanGroup\");\nexport const ECDH = /* @__PURE__ */ notImplementedClass(\"crypto.ECDH\");\nexport const Hash = /* @__PURE__ */ notImplementedClass(\"crypto.Hash\");\nexport const Hmac = /* @__PURE__ */ notImplementedClass(\"crypto.Hmac\");\nexport const KeyObject = /* @__PURE__ */ notImplementedClass(\"crypto.KeyObject\");\nexport const Sign = /* @__PURE__ */ notImplementedClass(\"crypto.Sign\");\nexport const Verify = /* @__PURE__ */ notImplementedClass(\"crypto.Verify\");\nexport const X509Certificate = /* @__PURE__ */ notImplementedClass(\"crypto.X509Certificate\");\n", "import { getRandomValues, randomUUID, subtle } from \"./internal/crypto/web.mjs\";\nimport { Certificate, Cipher, Cipheriv, Decipher, Dec<PERSON>her<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>manGroup, ECDH, Hash, Hmac, KeyObject, Sign, Verify, X509Certificate, checkPrime, checkPrimeSync, createCipheriv, createDecipheriv, createD<PERSON><PERSON><PERSON>ellman, createDiffieHellmanGroup, createECDH, createHash, createHmac, createPrivate<PERSON><PERSON>, createPublic<PERSON>ey, createS<PERSON>ret<PERSON><PERSON>, createSign, createVerify, diffie<PERSON><PERSON>man, fips, generateKey, generateKeyPair, generateKeyPairSync, generateKeySync, generatePrime, generatePrimeSync, getCipherInfo, getCiphers, getCurves, getDiffieHellman, getFips, getHashes, hash, hkdf, hkdfSync, pbkdf2, pbkdf2Sync, privateDecrypt, privateEncrypt, pseudoRandomBytes, publicDecrypt, prng, publicEncrypt, randomBytes, randomFill, randomFillSync, randomInt, rng, scrypt, scryptSync, secureHeapUsed, setEngine, setFips, sign, timingSafeEqual, verify, webcrypto } from \"./internal/crypto/node.mjs\";\nimport { OPENSSL_VERSION_NUMBER, SSL_OP_ALL, SSL_OP_ALLOW_NO_DHE_KEX, SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION, SSL_OP_CIPHER_SERVER_PREFERENCE, SSL_OP_CISCO_ANYCONNECT, SSL_OP_COOKIE_EXCHANGE, SSL_OP_CRYPTOPRO_TLSEXT_BUG, SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS, SSL_OP_LEGACY_SERVER_CONNECT, SSL_OP_NO_COMPRESSION, SSL_OP_NO_ENCRYPT_THEN_MAC, SSL_OP_NO_QUERY_MTU, SSL_OP_NO_RENEGOTIATION, SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION, SSL_OP_NO_SSLv2, SSL_OP_NO_SSLv3, SSL_OP_NO_TICKET, SSL_OP_NO_TLSv1, SSL_OP_NO_TLSv1_1, SSL_OP_NO_TLSv1_2, SSL_OP_NO_TLSv1_3, SSL_OP_PRIORITIZE_CHACHA, SSL_OP_TLS_ROLLBACK_BUG, ENGINE_METHOD_RSA, ENGINE_METHOD_DSA, ENGINE_METHOD_DH, ENGINE_METHOD_RAND, ENGINE_METHOD_EC, ENGINE_METHOD_CIPHERS, ENGINE_METHOD_DIGESTS, ENGINE_METHOD_PKEY_METHS, ENGINE_METHOD_PKEY_ASN1_METHS, ENGINE_METHOD_ALL, ENGINE_METHOD_NONE, DH_CHECK_P_NOT_SAFE_PRIME, DH_CHECK_P_NOT_PRIME, DH_UNABLE_TO_CHECK_GENERATOR, DH_NOT_SUITABLE_GENERATOR, RSA_PKCS1_PADDING, RSA_NO_PADDING, RSA_PKCS1_OAEP_PADDING, RSA_X931_PADDING, RSA_PKCS1_PSS_PADDING, RSA_PSS_SALTLEN_DIGEST, RSA_PSS_SALTLEN_MAX_SIGN, RSA_PSS_SALTLEN_AUTO, defaultCoreCipherList, TLS1_VERSION, TLS1_1_VERSION, TLS1_2_VERSION, TLS1_3_VERSION, POINT_CONVERSION_COMPRESSED, POINT_CONVERSION_UNCOMPRESSED, POINT_CONVERSION_HYBRID, defaultCipherList } from \"./internal/crypto/constants.mjs\";\nexport * from \"./internal/crypto/web.mjs\";\nexport * from \"./internal/crypto/node.mjs\";\nexport const constants = {\n\tOPENSSL_VERSION_NUMBER,\n\tSSL_OP_ALL,\n\tSSL_OP_ALLOW_NO_DHE_KEX,\n\tSSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION,\n\tSSL_OP_CIPHER_SERVER_PREFERENCE,\n\tSSL_OP_CISCO_ANYCONNECT,\n\tSSL_OP_COOKIE_EXCHANGE,\n\tSSL_OP_CRYPTOPRO_TLSEXT_BUG,\n\tSSL_OP_DONT_INSERT_EMPTY_FRAGMENTS,\n\tSSL_OP_LEGACY_SERVER_CONNECT,\n\tSSL_OP_NO_COMPRESSION,\n\tSSL_OP_NO_ENCRYPT_THEN_MAC,\n\tSSL_OP_NO_QUERY_MTU,\n\tSSL_OP_NO_RENEGOTIATION,\n\tSSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION,\n\tSSL_OP_NO_SSLv2,\n\tSSL_OP_NO_SSLv3,\n\tSSL_OP_NO_TICKET,\n\tSSL_OP_NO_TLSv1,\n\tSSL_OP_NO_TLSv1_1,\n\tSSL_OP_NO_TLSv1_2,\n\tSSL_OP_NO_TLSv1_3,\n\tSSL_OP_PRIORITIZE_CHACHA,\n\tSSL_OP_TLS_ROLLBACK_BUG,\n\tENGINE_METHOD_RSA,\n\tENGINE_METHOD_DSA,\n\tENGINE_METHOD_DH,\n\tENGINE_METHOD_RAND,\n\tENGINE_METHOD_EC,\n\tENGINE_METHOD_CIPHERS,\n\tENGINE_METHOD_DIGESTS,\n\tENGINE_METHOD_PKEY_METHS,\n\tENGINE_METHOD_PKEY_ASN1_METHS,\n\tENGINE_METHOD_ALL,\n\tENGINE_METHOD_NONE,\n\tDH_CHECK_P_NOT_SAFE_PRIME,\n\tDH_CHECK_P_NOT_PRIME,\n\tDH_UNABLE_TO_CHECK_GENERATOR,\n\tDH_NOT_SUITABLE_GENERATOR,\n\tRSA_PKCS1_PADDING,\n\tRSA_NO_PADDING,\n\tRSA_PKCS1_OAEP_PADDING,\n\tRSA_X931_PADDING,\n\tRSA_PKCS1_PSS_PADDING,\n\tRSA_PSS_SALTLEN_DIGEST,\n\tRSA_PSS_SALTLEN_MAX_SIGN,\n\tRSA_PSS_SALTLEN_AUTO,\n\tdefaultCoreCipherList,\n\tTLS1_VERSION,\n\tTLS1_1_VERSION,\n\tTLS1_2_VERSION,\n\tTLS1_3_VERSION,\n\tPOINT_CONVERSION_COMPRESSED,\n\tPOINT_CONVERSION_UNCOMPRESSED,\n\tPOINT_CONVERSION_HYBRID,\n\tdefaultCipherList\n};\nexport default {\n\tconstants,\n\tgetRandomValues,\n\trandomUUID,\n\tsubtle,\n\tCertificate,\n\tCipher,\n\tCipheriv,\n\tDecipher,\n\tDecipheriv,\n\tDiffieHellman,\n\tDiffieHellmanGroup,\n\tECDH,\n\tHash,\n\tHmac,\n\tKeyObject,\n\tSign,\n\tVerify,\n\tX509Certificate,\n\tcheckPrime,\n\tcheckPrimeSync,\n\tcreateCipheriv,\n\tcreateDecipheriv,\n\tcreateDiffieHellman,\n\tcreateDiffieHellmanGroup,\n\tcreateECDH,\n\tcreateHash,\n\tcreateHmac,\n\tcreatePrivateKey,\n\tcreatePublicKey,\n\tcreateSecretKey,\n\tcreateSign,\n\tcreateVerify,\n\tdiffieHellman,\n\tfips,\n\tgenerateKey,\n\tgenerateKeyPair,\n\tgenerateKeyPairSync,\n\tgenerateKeySync,\n\tgeneratePrime,\n\tgeneratePrimeSync,\n\tgetCipherInfo,\n\tgetCiphers,\n\tgetCurves,\n\tgetDiffieHellman,\n\tgetFips,\n\tgetHashes,\n\thash,\n\thkdf,\n\thkdfSync,\n\tpbkdf2,\n\tpbkdf2Sync,\n\tprivateDecrypt,\n\tprivateEncrypt,\n\tpseudoRandomBytes,\n\tpublicDecrypt,\n\tprng,\n\tpublicEncrypt,\n\trandomBytes,\n\trandomFill,\n\trandomFillSync,\n\trandomInt,\n\trng,\n\tscrypt,\n\tscryptSync,\n\tsecureHeapUsed,\n\tsetEngine,\n\tsetFips,\n\tsign,\n\ttimingSafeEqual,\n\tverify,\n\twebcrypto\n};\n", "import {\n  C<PERSON><PERSON>,\n  createCipher,\n  createD<PERSON><PERSON>her,\n  Decipher,\n  pseudoRandomBytes,\n  webcrypto as unenvCryptoWebcrypto\n} from \"unenv/node/crypto\";\nexport { Cipher, Decipher } from \"unenv/node/crypto\";\nconst workerdCrypto = process.getBuiltinModule(\"node:crypto\");\nexport const {\n  Certificate,\n  checkPrime,\n  checkPrimeSync,\n  constants,\n  // @ts-expect-error\n  Cipheriv,\n  createCipher<PERSON>,\n  createDecipher<PERSON>,\n  createD<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  createDiffieHellmanGroup,\n  createECDH,\n  createHash,\n  createHmac,\n  createPrivateKey,\n  createPublic<PERSON><PERSON>,\n  createSecret<PERSON>ey,\n  createSign,\n  createVerify,\n  // @ts-expect-error\n  Decipheriv,\n  diff<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>HellmanGroup,\n  ECDH,\n  fips,\n  generateKey,\n  generateKeyPair,\n  generateKeyPairSync,\n  generateKeySync,\n  generatePrime,\n  generatePrimeSync,\n  getCipherInfo,\n  getCip<PERSON>,\n  getCurves,\n  getD<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  getFips,\n  getHashes,\n  getRandomValues,\n  hash,\n  Hash,\n  hkdf,\n  hkdfSync,\n  Hmac,\n  KeyObject,\n  pbkdf2,\n  pbkdf2Sync,\n  privateDecrypt,\n  privateEncrypt,\n  publicDecrypt,\n  publicEncrypt,\n  randomBytes,\n  randomFill,\n  randomFillSync,\n  randomInt,\n  randomUUID,\n  scrypt,\n  scryptSync,\n  secureHeapUsed,\n  setEngine,\n  setFips,\n  sign,\n  Sign,\n  subtle,\n  timingSafeEqual,\n  verify,\n  Verify,\n  X509Certificate\n} = workerdCrypto;\nexport const webcrypto = {\n  // @ts-expect-error\n  CryptoKey: unenvCryptoWebcrypto.CryptoKey,\n  getRandomValues,\n  randomUUID,\n  subtle\n};\nexport default {\n  /**\n   * manually unroll unenv-polyfilled-symbols to make it tree-shakeable\n   */\n  Certificate,\n  Cipher,\n  Cipheriv,\n  Decipher,\n  Decipheriv,\n  ECDH,\n  Sign,\n  Verify,\n  X509Certificate,\n  constants,\n  createCipheriv,\n  createDecipheriv,\n  createECDH,\n  createSign,\n  createVerify,\n  diffieHellman,\n  getCipherInfo,\n  hash,\n  privateDecrypt,\n  privateEncrypt,\n  publicDecrypt,\n  publicEncrypt,\n  scrypt,\n  scryptSync,\n  sign,\n  verify,\n  // default-only export from unenv\n  // @ts-expect-error unenv has unknown type\n  createCipher,\n  // @ts-expect-error unenv has unknown type\n  createDecipher,\n  // @ts-expect-error unenv has unknown type\n  pseudoRandomBytes,\n  /**\n   * manually unroll workerd-polyfilled-symbols to make it tree-shakeable\n   */\n  DiffieHellman,\n  DiffieHellmanGroup,\n  Hash,\n  Hmac,\n  KeyObject,\n  checkPrime,\n  checkPrimeSync,\n  createDiffieHellman,\n  createDiffieHellmanGroup,\n  createHash,\n  createHmac,\n  createPrivateKey,\n  createPublicKey,\n  createSecretKey,\n  generateKey,\n  generateKeyPair,\n  generateKeyPairSync,\n  generateKeySync,\n  generatePrime,\n  generatePrimeSync,\n  getCiphers,\n  getCurves,\n  getDiffieHellman,\n  getFips,\n  getHashes,\n  getRandomValues,\n  hkdf,\n  hkdfSync,\n  pbkdf2,\n  pbkdf2Sync,\n  randomBytes,\n  randomFill,\n  randomFillSync,\n  randomInt,\n  randomUUID,\n  secureHeapUsed,\n  setEngine,\n  setFips,\n  subtle,\n  timingSafeEqual,\n  // default-only export from workerd\n  fips,\n  // special-cased deep merged symbols\n  webcrypto\n};\n", "import libDefault from 'crypto';\nmodule.exports = libDefault;", "/*\r\n Copyright (c) 2012 N<PERSON><PERSON> <nevins.bartolo<PERSON><EMAIL>>\r\n Copyright (c) 2012 <PERSON> <<EMAIL>>\r\n Copyright (c) 2014 <PERSON> <<EMAIL>>\r\n\r\n Redistribution and use in source and binary forms, with or without\r\n modification, are permitted provided that the following conditions\r\n are met:\r\n 1. Redistributions of source code must retain the above copyright\r\n notice, this list of conditions and the following disclaimer.\r\n 2. Redistributions in binary form must reproduce the above copyright\r\n notice, this list of conditions and the following disclaimer in the\r\n documentation and/or other materials provided with the distribution.\r\n 3. The name of the author may not be used to endorse or promote products\r\n derived from this software without specific prior written permission.\r\n\r\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\r\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\r\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\r\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\r\n INCIDENTAL, SPECIAL, EXEM<PERSON>AR<PERSON>, OR CONSEQUENTIAL DAMAGES (INCLUDI<PERSON>, BUT\r\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\r\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\r\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\r\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\r\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n */\r\n\r\n/**\r\n * @license bcrypt.js (c) 2013 Daniel Wirtz <<EMAIL>>\r\n * Released under the Apache License, Version 2.0\r\n * see: https://github.com/dcodeIO/bcrypt.js for details\r\n */\r\n(function(global, factory) {\r\n\r\n    /* AMD */ if (typeof define === 'function' && define[\"amd\"])\r\n        define([], factory);\r\n    /* CommonJS */ else if (typeof require === 'function' && typeof module === \"object\" && module && module[\"exports\"])\r\n        module[\"exports\"] = factory();\r\n    /* Global */ else\r\n        (global[\"dcodeIO\"] = global[\"dcodeIO\"] || {})[\"bcrypt\"] = factory();\r\n\r\n}(this, function() {\r\n    \"use strict\";\r\n\r\n    /**\r\n     * bcrypt namespace.\r\n     * @type {Object.<string,*>}\r\n     */\r\n    var bcrypt = {};\r\n\r\n    /**\r\n     * The random implementation to use as a fallback.\r\n     * @type {?function(number):!Array.<number>}\r\n     * @inner\r\n     */\r\n    var randomFallback = null;\r\n\r\n    /**\r\n     * Generates cryptographically secure random bytes.\r\n     * @function\r\n     * @param {number} len Bytes length\r\n     * @returns {!Array.<number>} Random bytes\r\n     * @throws {Error} If no random implementation is available\r\n     * @inner\r\n     */\r\n    function random(len) {\r\n        /* node */ if (typeof module !== 'undefined' && module && module['exports'])\r\n            try {\r\n                return require(\"crypto\")['randomBytes'](len);\r\n            } catch (e) {}\r\n        /* WCA */ try {\r\n            var a; (self['crypto']||self['msCrypto'])['getRandomValues'](a = new Uint32Array(len));\r\n            return Array.prototype.slice.call(a);\r\n        } catch (e) {}\r\n        /* fallback */ if (!randomFallback)\r\n            throw Error(\"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\");\r\n        return randomFallback(len);\r\n    }\r\n\r\n    // Test if any secure randomness source is available\r\n    var randomAvailable = false;\r\n    try {\r\n        random(1);\r\n        randomAvailable = true;\r\n    } catch (e) {}\r\n\r\n    // Default fallback, if any\r\n    randomFallback = null;\r\n    /**\r\n     * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\r\n     *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\r\n     *  is seeded properly!\r\n     * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\r\n     *  sole argument, returning the corresponding array of cryptographically secure random byte values.\r\n     * @see http://nodejs.org/api/crypto.html\r\n     * @see http://www.w3.org/TR/WebCryptoAPI/\r\n     */\r\n    bcrypt.setRandomFallback = function(random) {\r\n        randomFallback = random;\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a salt.\r\n     * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {number=} seed_length Not supported.\r\n     * @returns {string} Resulting salt\r\n     * @throws {Error} If a random fallback is required but not set\r\n     * @expose\r\n     */\r\n    bcrypt.genSaltSync = function(rounds, seed_length) {\r\n        rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof rounds !== 'number')\r\n            throw Error(\"Illegal arguments: \"+(typeof rounds)+\", \"+(typeof seed_length));\r\n        if (rounds < 4)\r\n            rounds = 4;\r\n        else if (rounds > 31)\r\n            rounds = 31;\r\n        var salt = [];\r\n        salt.push(\"$2a$\");\r\n        if (rounds < 10)\r\n            salt.push(\"0\");\r\n        salt.push(rounds.toString());\r\n        salt.push('$');\r\n        salt.push(base64_encode(random(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\r\n        return salt.join('');\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a salt.\r\n     * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {(number|function(Error, string=))=} seed_length Not supported.\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.genSalt = function(rounds, seed_length, callback) {\r\n        if (typeof seed_length === 'function')\r\n            callback = seed_length,\r\n            seed_length = undefined; // Not supported.\r\n        if (typeof rounds === 'function')\r\n            callback = rounds,\r\n            rounds = undefined;\r\n        if (typeof rounds === 'undefined')\r\n            rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        else if (typeof rounds !== 'number')\r\n            throw Error(\"illegal arguments: \"+(typeof rounds));\r\n\r\n        function _async(callback) {\r\n            nextTick(function() { // Pretty thin, but salting is fast enough\r\n                try {\r\n                    callback(null, bcrypt.genSaltSync(rounds));\r\n                } catch (err) {\r\n                    callback(err);\r\n                }\r\n            });\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\r\n     * @returns {string} Resulting hash\r\n     * @expose\r\n     */\r\n    bcrypt.hashSync = function(s, salt) {\r\n        if (typeof salt === 'undefined')\r\n            salt = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof salt === 'number')\r\n            salt = bcrypt.genSaltSync(salt);\r\n        if (typeof s !== 'string' || typeof salt !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt));\r\n        return _hash(s, salt);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {number|string} salt Salt length to generate or salt to use\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.hash = function(s, salt, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s === 'string' && typeof salt === 'number')\r\n                bcrypt.genSalt(salt, function(err, salt) {\r\n                    _hash(s, salt, callback, progressCallback);\r\n                });\r\n            else if (typeof s === 'string' && typeof salt === 'string')\r\n                _hash(s, salt, callback, progressCallback);\r\n            else\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt))));\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Compares two strings of the same length in constant time.\r\n     * @param {string} known Must be of the correct length\r\n     * @param {string} unknown Must be the same length as `known`\r\n     * @returns {boolean}\r\n     * @inner\r\n     */\r\n    function safeStringCompare(known, unknown) {\r\n        var right = 0,\r\n            wrong = 0;\r\n        for (var i=0, k=known.length; i<k; ++i) {\r\n            if (known.charCodeAt(i) === unknown.charCodeAt(i))\r\n                ++right;\r\n            else\r\n                ++wrong;\r\n        }\r\n        // Prevent removal of unused variables (never true, actually)\r\n        if (right < 0)\r\n            return false;\r\n        return wrong === 0;\r\n    }\r\n\r\n    /**\r\n     * Synchronously tests a string against a hash.\r\n     * @param {string} s String to compare\r\n     * @param {string} hash Hash to test against\r\n     * @returns {boolean} true if matching, otherwise false\r\n     * @throws {Error} If an argument is illegal\r\n     * @expose\r\n     */\r\n    bcrypt.compareSync = function(s, hash) {\r\n        if (typeof s !== \"string\" || typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash));\r\n        if (hash.length !== 60)\r\n            return false;\r\n        return safeStringCompare(bcrypt.hashSync(s, hash.substr(0, hash.length-31)), hash);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously compares the given data against the given hash.\r\n     * @param {string} s Data to compare\r\n     * @param {string} hash Data to be compared to\r\n     * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.compare = function(s, hash, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s !== \"string\" || typeof hash !== \"string\") {\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash))));\r\n                return;\r\n            }\r\n            if (hash.length !== 60) {\r\n                nextTick(callback.bind(this, null, false));\r\n                return;\r\n            }\r\n            bcrypt.hash(s, hash.substr(0, 29), function(err, comp) {\r\n                if (err)\r\n                    callback(err);\r\n                else\r\n                    callback(null, safeStringCompare(comp, hash));\r\n            }, progressCallback);\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Gets the number of rounds used to encrypt the specified hash.\r\n     * @param {string} hash Hash to extract the used number of rounds from\r\n     * @returns {number} Number of rounds used\r\n     * @throws {Error} If `hash` is not a string\r\n     * @expose\r\n     */\r\n    bcrypt.getRounds = function(hash) {\r\n        if (typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        return parseInt(hash.split(\"$\")[2], 10);\r\n    };\r\n\r\n    /**\r\n     * Gets the salt portion from a hash. Does not validate the hash.\r\n     * @param {string} hash Hash to extract the salt from\r\n     * @returns {string} Extracted salt part\r\n     * @throws {Error} If `hash` is not a string or otherwise invalid\r\n     * @expose\r\n     */\r\n    bcrypt.getSalt = function(hash) {\r\n        if (typeof hash !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        if (hash.length !== 60)\r\n            throw Error(\"Illegal hash length: \"+hash.length+\" != 60\");\r\n        return hash.substring(0, 29);\r\n    };\r\n\r\n    /**\r\n     * Continues with the callback on the next tick.\r\n     * @function\r\n     * @param {function(...[*])} callback Callback to execute\r\n     * @inner\r\n     */\r\n    var nextTick = typeof process !== 'undefined' && process && typeof process.nextTick === 'function'\r\n        ? (typeof setImmediate === 'function' ? setImmediate : process.nextTick)\r\n        : setTimeout;\r\n\r\n    /**\r\n     * Converts a JavaScript string to UTF8 bytes.\r\n     * @param {string} str String\r\n     * @returns {!Array.<number>} UTF8 bytes\r\n     * @inner\r\n     */\r\n    function stringToBytes(str) {\r\n        var out = [],\r\n            i = 0;\r\n        utfx.encodeUTF16toUTF8(function() {\r\n            if (i >= str.length) return null;\r\n            return str.charCodeAt(i++);\r\n        }, function(b) {\r\n            out.push(b);\r\n        });\r\n        return out;\r\n    }\r\n\r\n    // A base64 implementation for the bcrypt algorithm. This is partly non-standard.\r\n\r\n    /**\r\n     * bcrypt's own non-standard base64 dictionary.\r\n     * @type {!Array.<string>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_CODE = \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split('');\r\n\r\n    /**\r\n     * @type {!Array.<number>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_INDEX = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0,\r\n        1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, -1, -1, -1, -1, -1, -1,\r\n        -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,\r\n        20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28, 29, 30,\r\n        31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\r\n        48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1];\r\n\r\n    /**\r\n     * @type {!function(...number):string}\r\n     * @inner\r\n     */\r\n    var stringFromCharCode = String.fromCharCode;\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input.\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @inner\r\n     */\r\n    function base64_encode(b, len) {\r\n        var off = 0,\r\n            rs = [],\r\n            c1, c2;\r\n        if (len <= 0 || len > b.length)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < len) {\r\n            c1 = b[off++] & 0xff;\r\n            rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\r\n            c1 = (c1 & 0x03) << 4;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 4) & 0x0f;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            c1 = (c2 & 0x0f) << 2;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 6) & 0x03;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            rs.push(BASE64_CODE[c2 & 0x3f]);\r\n        }\r\n        return rs.join('');\r\n    }\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output.\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @inner\r\n     */\r\n    function base64_decode(s, len) {\r\n        var off = 0,\r\n            slen = s.length,\r\n            olen = 0,\r\n            rs = [],\r\n            c1, c2, c3, c4, o, code;\r\n        if (len <= 0)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < slen - 1 && olen < len) {\r\n            code = s.charCodeAt(off++);\r\n            c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            code = s.charCodeAt(off++);\r\n            c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c1 == -1 || c2 == -1)\r\n                break;\r\n            o = (c1 << 2) >>> 0;\r\n            o |= (c2 & 0x30) >> 4;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c3 == -1)\r\n                break;\r\n            o = ((c2 & 0x0f) << 4) >>> 0;\r\n            o |= (c3 & 0x3c) >> 2;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            o = ((c3 & 0x03) << 6) >>> 0;\r\n            o |= c4;\r\n            rs.push(stringFromCharCode(o));\r\n            ++olen;\r\n        }\r\n        var res = [];\r\n        for (off = 0; off<olen; off++)\r\n            res.push(rs[off].charCodeAt(0));\r\n        return res;\r\n    }\r\n\r\n    /**\r\n     * utfx-embeddable (c) 2014 Daniel Wirtz <<EMAIL>>\r\n     * Released under the Apache License, Version 2.0\r\n     * see: https://github.com/dcodeIO/utfx for details\r\n     */\r\n    var utfx = function() {\r\n        \"use strict\";\r\n\r\n        /**\r\n         * utfx namespace.\r\n         * @inner\r\n         * @type {!Object.<string,*>}\r\n         */\r\n        var utfx = {};\r\n\r\n        /**\r\n         * Maximum valid code point.\r\n         * @type {number}\r\n         * @const\r\n         */\r\n        utfx.MAX_CODEPOINT = 0x10FFFF;\r\n\r\n        /**\r\n         * Encodes UTF8 code points to UTF8 bytes.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte\r\n         */\r\n        utfx.encodeUTF8 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src,\r\n                src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp < 0x80)\r\n                    dst(cp&0x7F);\r\n                else if (cp < 0x800)\r\n                    dst(((cp>>6)&0x1F)|0xC0),\r\n                    dst((cp&0x3F)|0x80);\r\n                else if (cp < 0x10000)\r\n                    dst(((cp>>12)&0x0F)|0xE0),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                else\r\n                    dst(((cp>>18)&0x07)|0xF0),\r\n                    dst(((cp>>12)&0x3F)|0x80),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Decodes UTF8 bytes to UTF8 code points.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each decoded code point.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the\r\n         *  remaining bytes.\r\n         */\r\n        utfx.decodeUTF8 = function(src, dst) {\r\n            var a, b, c, d, fail = function(b) {\r\n                b = b.slice(0, b.indexOf(null));\r\n                var err = Error(b.toString());\r\n                err.name = \"TruncatedError\";\r\n                err['bytes'] = b;\r\n                throw err;\r\n            };\r\n            while ((a = src()) !== null) {\r\n                if ((a&0x80) === 0)\r\n                    dst(a);\r\n                else if ((a&0xE0) === 0xC0)\r\n                    ((b = src()) === null) && fail([a, b]),\r\n                    dst(((a&0x1F)<<6) | (b&0x3F));\r\n                else if ((a&0xF0) === 0xE0)\r\n                    ((b=src()) === null || (c=src()) === null) && fail([a, b, c]),\r\n                    dst(((a&0x0F)<<12) | ((b&0x3F)<<6) | (c&0x3F));\r\n                else if ((a&0xF8) === 0xF0)\r\n                    ((b=src()) === null || (c=src()) === null || (d=src()) === null) && fail([a, b, c ,d]),\r\n                    dst(((a&0x07)<<18) | ((b&0x3F)<<12) | ((c&0x3F)<<6) | (d&0x3F));\r\n                else throw RangeError(\"Illegal starting byte: \"+a);\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts UTF16 characters to UTF8 code points.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each converted code\r\n         *  point.\r\n         */\r\n        utfx.UTF16toUTF8 = function(src, dst) {\r\n            var c1, c2 = null;\r\n            while (true) {\r\n                if ((c1 = c2 !== null ? c2 : src()) === null)\r\n                    break;\r\n                if (c1 >= 0xD800 && c1 <= 0xDFFF) {\r\n                    if ((c2 = src()) !== null) {\r\n                        if (c2 >= 0xDC00 && c2 <= 0xDFFF) {\r\n                            dst((c1-0xD800)*0x400+c2-0xDC00+0x10000);\r\n                            c2 = null; continue;\r\n                        }\r\n                    }\r\n                }\r\n                dst(c1);\r\n            }\r\n            if (c2 !== null) dst(c2);\r\n        };\r\n\r\n        /**\r\n         * Converts UTF8 code points to UTF16 characters.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a code point is out of range\r\n         */\r\n        utfx.UTF8toUTF16 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src, src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp <= 0xFFFF)\r\n                    dst(cp);\r\n                else\r\n                    cp -= 0x10000,\r\n                    dst((cp>>10)+0xD800),\r\n                    dst((cp%0x400)+0xDC00);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts and encodes UTF16 characters to UTF8 bytes.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively `null`\r\n         *  if there are no more characters left.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte.\r\n         */\r\n        utfx.encodeUTF16toUTF8 = function(src, dst) {\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                utfx.encodeUTF8(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Decodes and converts UTF8 bytes to UTF16 characters.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the remaining bytes.\r\n         */\r\n        utfx.decodeUTF8toUTF16 = function(src, dst) {\r\n            utfx.decodeUTF8(src, function(cp) {\r\n                utfx.UTF8toUTF16(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Calculates the byte length of an UTF8 code point.\r\n         * @param {number} cp UTF8 code point\r\n         * @returns {number} Byte length\r\n         */\r\n        utfx.calculateCodePoint = function(cp) {\r\n            return (cp < 0x80) ? 1 : (cp < 0x800) ? 2 : (cp < 0x10000) ? 3 : 4;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 bytes required to store UTF8 code points.\r\n         * @param {(!function():number|null)} src Code points source as a function returning the next code point respectively\r\n         *  `null` if there are no more code points left.\r\n         * @returns {number} The number of UTF8 bytes required\r\n         */\r\n        utfx.calculateUTF8 = function(src) {\r\n            var cp, l=0;\r\n            while ((cp = src()) !== null)\r\n                l += utfx.calculateCodePoint(cp);\r\n            return l;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 code points respectively UTF8 bytes required to store UTF16 char codes.\r\n         * @param {(!function():number|null)} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @returns {!Array.<number>} The number of UTF8 code points at index 0 and the number of UTF8 bytes required at index 1.\r\n         */\r\n        utfx.calculateUTF16asUTF8 = function(src) {\r\n            var n=0, l=0;\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                ++n; l += utfx.calculateCodePoint(cp);\r\n            });\r\n            return [n,l];\r\n        };\r\n\r\n        return utfx;\r\n    }();\r\n\r\n    Date.now = Date.now || function() { return +new Date; };\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BCRYPT_SALT_LEN = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var GENSALT_DEFAULT_LOG2_ROUNDS = 10;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BLOWFISH_NUM_ROUNDS = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var MAX_EXECUTION_TIME = 100;\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var P_ORIG = [\r\n        0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822,\r\n        0x299f31d0, 0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377,\r\n        0xbe5466cf, 0x34e90c6c, 0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5,\r\n        0xb5470917, 0x9216d5d9, 0x8979fb1b\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var S_ORIG = [\r\n        0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed,\r\n        0x6a267e96, 0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7,\r\n        0x0801f2e2, 0x858efc16, 0x636920d8, 0x71574e69, 0xa458fea3,\r\n        0xf4933d7e, 0x0d95748f, 0x728eb658, 0x718bcd58, 0x82154aee,\r\n        0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013, 0xc5d1b023,\r\n        0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\r\n        0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda,\r\n        0x55605c60, 0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440,\r\n        0x55ca396a, 0x2aab10b6, 0xb4cc5c34, 0x1141e8ce, 0xa15486af,\r\n        0x7c72e993, 0xb3ee1411, 0x636fbc2a, 0x2ba9c55d, 0x741831f6,\r\n        0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c, 0x7a325381,\r\n        0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\r\n        0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d,\r\n        0xe98575b1, 0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5,\r\n        0x0f6d6ff3, 0x83f44239, 0x2e0b4482, 0xa4842004, 0x69c8f04a,\r\n        0x9e1f9b5e, 0x21c66842, 0xf6e96c9a, 0x670c9c61, 0xabd388f0,\r\n        0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3, 0x6eef0b6c,\r\n        0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\r\n        0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3,\r\n        0x3b8b5ebe, 0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6,\r\n        0x4ed3aa62, 0x363f7706, 0x1bfedf72, 0x429b023d, 0x37d0d724,\r\n        0xd00a1248, 0xdb0fead3, 0x49f1c09b, 0x075372c9, 0x80991b7b,\r\n        0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b, 0x976ce0bd,\r\n        0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\r\n        0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f,\r\n        0x9b30952c, 0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd,\r\n        0x660f2807, 0x192e4bb3, 0xc0cba857, 0x45c8740f, 0xd20b5f39,\r\n        0xb9d3fbdb, 0x5579c0bd, 0x1a60320a, 0xd6a100c6, 0x402c7279,\r\n        0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8, 0x3c7516df,\r\n        0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\r\n        0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e,\r\n        0xdf1769db, 0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573,\r\n        0x695b27b0, 0xbbca58c8, 0xe1ffa35d, 0xb8f011a0, 0x10fa3d98,\r\n        0xfd2183b8, 0x4afcb56c, 0x2dd1d35b, 0x9a53e479, 0xb6f84565,\r\n        0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33, 0x62fb1341,\r\n        0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\r\n        0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0,\r\n        0xafc725e0, 0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64,\r\n        0x8888b812, 0x900df01c, 0x4fad5ea0, 0x688fc31c, 0xd1cff191,\r\n        0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777, 0xea752dfe, 0x8b021fa1,\r\n        0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299, 0xb4a84fe0,\r\n        0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\r\n        0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5,\r\n        0xfb9d35cf, 0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49,\r\n        0x00250e2d, 0x2071b35e, 0x226800bb, 0x57b8e0af, 0x2464369b,\r\n        0xf009b91e, 0x5563911d, 0x59dfa6aa, 0x78c14389, 0xd95a537f,\r\n        0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9, 0x11c81968,\r\n        0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\r\n        0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5,\r\n        0x571be91f, 0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6,\r\n        0xff34052e, 0xc5855664, 0x53b02d5d, 0xa99f8fa1, 0x08ba4799,\r\n        0x6e85076a, 0x4b7a70e9, 0xb5b32944, 0xdb75092e, 0xc4192623,\r\n        0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266, 0xecaa8c71,\r\n        0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\r\n        0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6,\r\n        0x99f73fd6, 0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1,\r\n        0x4cdd2086, 0x8470eb26, 0x6382e9c6, 0x021ecc5e, 0x09686b3f,\r\n        0x3ebaefc9, 0x3c971814, 0x6b6a70a1, 0x687f3584, 0x52a0e286,\r\n        0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c, 0x8e7d44ec,\r\n        0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\r\n        0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9,\r\n        0x7ca92ff6, 0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc,\r\n        0xc8b57634, 0x9af3dda7, 0xa9446146, 0x0fd0030e, 0xecc8c73e,\r\n        0xa4751e41, 0xe238cd99, 0x3bea0e2f, 0x3280bba1, 0x183eb331,\r\n        0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf, 0x2cb81290,\r\n        0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\r\n        0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6,\r\n        0x9f84cd87, 0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c,\r\n        0xec7aec3a, 0xdb851dfa, 0x63094366, 0xc464c3d2, 0xef1c1847,\r\n        0x3215d908, 0xdd433b37, 0x24c2ba16, 0x12a14d43, 0x2a65c451,\r\n        0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55, 0x81ac77d6,\r\n        0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\r\n        0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570,\r\n        0xeae96fb1, 0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa,\r\n        0x2965dcb9, 0x99e71d0f, 0x803e89d6, 0x5266c825, 0x2e4cc978,\r\n        0x9c10b36a, 0xc6150eba, 0x94e2ea78, 0xa5fc3c53, 0x1e0a2df4,\r\n        0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960, 0x5223a708,\r\n        0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\r\n        0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185,\r\n        0x68ab9802, 0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84,\r\n        0x1521b628, 0x29076170, 0xecdd4775, 0x619f1510, 0x13cca830,\r\n        0xeb61bd96, 0x0334fe1e, 0xaa0363cf, 0xb5735c90, 0x4c70a239,\r\n        0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7, 0x9cab5cab,\r\n        0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\r\n        0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19,\r\n        0x875fa099, 0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77,\r\n        0x11ed935f, 0x16681281, 0x0e358829, 0xc7e61fd6, 0x96dedfa1,\r\n        0x7858ba99, 0x57f584a5, 0x1b227263, 0x9b83c3ff, 0x1ac24696,\r\n        0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128, 0x58ebf2ef,\r\n        0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\r\n        0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15,\r\n        0xfacb4fd0, 0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105,\r\n        0xd81e799e, 0x86854dc7, 0xe44b476a, 0x3d816250, 0xcf62a1f2,\r\n        0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3, 0x7f1524c3, 0x69cb7492,\r\n        0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d, 0x1462b174,\r\n        0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\r\n        0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759,\r\n        0xcbee7460, 0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e,\r\n        0xe8efd855, 0x61d99735, 0xa969a7aa, 0xc50c06c2, 0x5a04abfc,\r\n        0x800bcadc, 0x9e447a2e, 0xc3453484, 0xfdd56705, 0x0e1e9ec9,\r\n        0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340, 0xc5c43465,\r\n        0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\r\n        0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c,\r\n        0x94692934, 0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068,\r\n        0xd4082471, 0x3320f46a, 0x43b7d4b7, 0x500061af, 0x1e39f62e,\r\n        0x97244546, 0x14214f74, 0xbf8b8840, 0x4d95fc1d, 0x96b591af,\r\n        0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785, 0x7fac6dd0,\r\n        0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\r\n        0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462,\r\n        0xd7486900, 0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c,\r\n        0xb58ce006, 0x7af4d6b6, 0xaace1e7c, 0xd3375fec, 0xce78a399,\r\n        0x406b2a42, 0x20fe9e35, 0xd9f385b9, 0xee39d7ab, 0x3b124e8b,\r\n        0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2, 0x3a6efa74,\r\n        0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\r\n        0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7,\r\n        0xd096954b, 0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33,\r\n        0xa62a4a56, 0x3f3125f9, 0x5ef47e1c, 0x9029317c, 0xfdf8e802,\r\n        0x04272f70, 0x80bb155c, 0x05282ce3, 0x95c11548, 0xe4c66d22,\r\n        0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f, 0x404779a4,\r\n        0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\r\n        0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2,\r\n        0x02e1329e, 0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1,\r\n        0x3b240b62, 0xeebeb922, 0x85b2a20e, 0xe6ba0d99, 0xde720c8c,\r\n        0x2da2f728, 0xd0127845, 0x95b794fd, 0x647d0862, 0xe7ccf5f0,\r\n        0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e, 0x0a476341,\r\n        0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\r\n        0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b,\r\n        0xdcd0e804, 0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b,\r\n        0x667b9ffb, 0xcedb7d9c, 0xa091cf0b, 0xd9155ea3, 0xbb132f88,\r\n        0x515bad24, 0x7b9479bf, 0x763bd6eb, 0x37392eb3, 0xcc115979,\r\n        0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b, 0x12754ccc,\r\n        0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\r\n        0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659,\r\n        0x0a121386, 0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f,\r\n        0xbebfe988, 0x64e4c3fe, 0x9dbc8057, 0xf0f7c086, 0x60787bf8,\r\n        0x6003604d, 0xd1fd8346, 0xf6381fb0, 0x7745ae04, 0xd736fccc,\r\n        0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f, 0x77a057be,\r\n        0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\r\n        0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255,\r\n        0x46fcd9b9, 0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2,\r\n        0x466e598e, 0x20b45770, 0x8cd55591, 0xc902de4c, 0xb90bace1,\r\n        0xbb8205d0, 0x11a86248, 0x7574a99e, 0xb77f19b6, 0xe0a9dc09,\r\n        0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c, 0x4a99a025,\r\n        0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\r\n        0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01,\r\n        0xa70683fa, 0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641,\r\n        0xc3604c06, 0x61a806b5, 0xf0177a28, 0xc0f586e0, 0x006058aa,\r\n        0x30dc7d62, 0x11e69ed7, 0x2338ea63, 0x53c2dd94, 0xc2c21634,\r\n        0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76, 0x6f05e409,\r\n        0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\r\n        0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3,\r\n        0x4dad0fc4, 0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c,\r\n        0x6fd5c7e7, 0x56e14ec4, 0x362abfce, 0xddc6c837, 0xd79a3234,\r\n        0x92638212, 0x670efa8e, 0x406000e0, 0x3a39ce37, 0xd3faf5cf,\r\n        0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742, 0xd3822740,\r\n        0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\r\n        0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f,\r\n        0xbc946e79, 0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d,\r\n        0xd5730a1d, 0x4cd04dc6, 0x2939bbdb, 0xa9ba4650, 0xac9526e8,\r\n        0xbe5ee304, 0xa1fad5f0, 0x6a2d519a, 0x63ef8ce2, 0x9a86ee22,\r\n        0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4, 0x83c061ba,\r\n        0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\r\n        0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69,\r\n        0x77fa0a59, 0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593,\r\n        0xe990fd5a, 0x9e34d797, 0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a,\r\n        0x017da67d, 0xd1cf3ed6, 0x7c7d2d28, 0x1f9f25cf, 0xadf2b89b,\r\n        0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6, 0x47b0acfd,\r\n        0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\r\n        0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4,\r\n        0x88f46dba, 0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2,\r\n        0x97271aec, 0xa93a072a, 0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb,\r\n        0x26dcf319, 0x7533d928, 0xb155fdf5, 0x03563482, 0x8aba3cbb,\r\n        0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f, 0x4de81751,\r\n        0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\r\n        0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369,\r\n        0x6413e680, 0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166,\r\n        0xb39a460a, 0x6445c0dd, 0x586cdecf, 0x1c20c8ae, 0x5bbef7dd,\r\n        0x1b588d40, 0xccd2017f, 0x6bb4e3bb, 0xdda26a7e, 0x3a59ff45,\r\n        0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb, 0x8d6612ae,\r\n        0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\r\n        0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08,\r\n        0x4eb4e2cc, 0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d,\r\n        0x06b89fb4, 0xce6ea048, 0x6f3f3b82, 0x3520ab82, 0x011a1d4b,\r\n        0x277227f8, 0x611560b1, 0xe7933fdc, 0xbb3a792b, 0x344525bd,\r\n        0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9, 0xe01cc87e,\r\n        0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\r\n        0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c,\r\n        0xe0b12b4f, 0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c,\r\n        0xbf97222c, 0x15e6fc2a, 0x0f91fc71, 0x9b941525, 0xfae59361,\r\n        0xceb69ceb, 0xc2a86459, 0x12baa8d1, 0xb6c1075e, 0xe3056a0c,\r\n        0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b, 0x4c98a0be,\r\n        0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\r\n        0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d,\r\n        0x9b992f2e, 0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891,\r\n        0xce6279cf, 0xcd3e7e6f, 0x1618b166, 0xfd2c1d05, 0x848fd2c5,\r\n        0xf6fb2299, 0xf523f357, 0xa6327623, 0x93a83531, 0x56cccd02,\r\n        0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc, 0xde966292,\r\n        0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\r\n        0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2,\r\n        0x35bdd2f6, 0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b,\r\n        0x53113ec0, 0x1640e3d3, 0x38abbd60, 0x2547adf0, 0xba38209c,\r\n        0xf746ce76, 0x77afa1c5, 0x20756060, 0x85cbfe4e, 0x8ae88dd8,\r\n        0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c, 0x01c36ae4,\r\n        0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\r\n        0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var C_ORIG = [\r\n        0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944,\r\n        0x6f756274\r\n    ];\r\n\r\n    /**\r\n     * @param {Array.<number>} lr\r\n     * @param {number} off\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @returns {Array.<number>}\r\n     * @inner\r\n     */\r\n    function _encipher(lr, off, P, S) { // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\r\n        var n,\r\n            l = lr[off],\r\n            r = lr[off + 1];\r\n\r\n        l ^= P[0];\r\n\r\n        /*\r\n        for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\r\n            // Feistel substitution on left word\r\n            n  = S[l >>> 24],\r\n            n += S[0x100 | ((l >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((l >> 8) & 0xff)],\r\n            n += S[0x300 | (l & 0xff)],\r\n            r ^= n ^ P[++i],\r\n            // Feistel substitution on right word\r\n            n  = S[r >>> 24],\r\n            n += S[0x100 | ((r >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((r >> 8) & 0xff)],\r\n            n += S[0x300 | (r & 0xff)],\r\n            l ^= n ^ P[++i];\r\n        */\r\n\r\n        //The following is an unrolled version of the above loop.\r\n        //Iteration 0\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[1];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[2];\r\n        //Iteration 1\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[3];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[4];\r\n        //Iteration 2\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[5];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[6];\r\n        //Iteration 3\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[7];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[8];\r\n        //Iteration 4\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[9];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[10];\r\n        //Iteration 5\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[11];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[12];\r\n        //Iteration 6\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[13];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[14];\r\n        //Iteration 7\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[15];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[16];\r\n\r\n        lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\r\n        lr[off + 1] = l;\r\n        return lr;\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} data\r\n     * @param {number} offp\r\n     * @returns {{key: number, offp: number}}\r\n     * @inner\r\n     */\r\n    function _streamtoword(data, offp) {\r\n        for (var i = 0, word = 0; i < 4; ++i)\r\n            word = (word << 8) | (data[offp] & 0xff),\r\n            offp = (offp + 1) % data.length;\r\n        return { key: word, offp: offp };\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _key(key, P, S) {\r\n        var offset = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offset),\r\n            offset = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        for (i = 0; i < plen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Expensive key schedule Blowfish.\r\n     * @param {Array.<number>} data\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _ekskey(data, key, P, S) {\r\n        var offp = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offp),\r\n            offp = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        offp = 0;\r\n        for (i = 0; i < plen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Internaly crypts a string.\r\n     * @param {Array.<number>} b Bytes to crypt\r\n     * @param {Array.<number>} salt Salt bytes to use\r\n     * @param {number} rounds Number of rounds\r\n     * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\r\n     *  omitted, the operation will be performed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _crypt(b, salt, rounds, callback, progressCallback) {\r\n        var cdata = C_ORIG.slice(),\r\n            clen = cdata.length,\r\n            err;\r\n\r\n        // Validate\r\n        if (rounds < 4 || rounds > 31) {\r\n            err = Error(\"Illegal number of rounds (4-31): \"+rounds);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        if (salt.length !== BCRYPT_SALT_LEN) {\r\n            err =Error(\"Illegal salt length: \"+salt.length+\" != \"+BCRYPT_SALT_LEN);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        rounds = (1 << rounds) >>> 0;\r\n\r\n        var P, S, i = 0, j;\r\n\r\n        //Use typed arrays when available - huge speedup!\r\n        if (Int32Array) {\r\n            P = new Int32Array(P_ORIG);\r\n            S = new Int32Array(S_ORIG);\r\n        } else {\r\n            P = P_ORIG.slice();\r\n            S = S_ORIG.slice();\r\n        }\r\n\r\n        _ekskey(salt, b, P, S);\r\n\r\n        /**\r\n         * Calcualtes the next round.\r\n         * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\r\n         * @inner\r\n         */\r\n        function next() {\r\n            if (progressCallback)\r\n                progressCallback(i / rounds);\r\n            if (i < rounds) {\r\n                var start = Date.now();\r\n                for (; i < rounds;) {\r\n                    i = i + 1;\r\n                    _key(b, P, S);\r\n                    _key(salt, P, S);\r\n                    if (Date.now() - start > MAX_EXECUTION_TIME)\r\n                        break;\r\n                }\r\n            } else {\r\n                for (i = 0; i < 64; i++)\r\n                    for (j = 0; j < (clen >> 1); j++)\r\n                        _encipher(cdata, j << 1, P, S);\r\n                var ret = [];\r\n                for (i = 0; i < clen; i++)\r\n                    ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\r\n                    ret.push((cdata[i] & 0xff) >>> 0);\r\n                if (callback) {\r\n                    callback(null, ret);\r\n                    return;\r\n                } else\r\n                    return ret;\r\n            }\r\n            if (callback)\r\n                nextTick(next);\r\n        }\r\n\r\n        // Async\r\n        if (typeof callback !== 'undefined') {\r\n            next();\r\n\r\n            // Sync\r\n        } else {\r\n            var res;\r\n            while (true)\r\n                if (typeof(res = next()) !== 'undefined')\r\n                    return res || [];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Internally hashes a string.\r\n     * @param {string} s String to hash\r\n     * @param {?string} salt Salt to use, actually never null\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\r\n     *  hashing is perormed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _hash(s, salt, callback, progressCallback) {\r\n        var err;\r\n        if (typeof s !== 'string' || typeof salt !== 'string') {\r\n            err = Error(\"Invalid string / salt: Not a string\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n\r\n        // Validate the salt\r\n        var minor, offset;\r\n        if (salt.charAt(0) !== '$' || salt.charAt(1) !== '2') {\r\n            err = Error(\"Invalid salt version: \"+salt.substring(0,2));\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n        if (salt.charAt(2) === '$')\r\n            minor = String.fromCharCode(0),\r\n            offset = 3;\r\n        else {\r\n            minor = salt.charAt(2);\r\n            if ((minor !== 'a' && minor !== 'b' && minor !== 'y') || salt.charAt(3) !== '$') {\r\n                err = Error(\"Invalid salt revision: \"+salt.substring(2,4));\r\n                if (callback) {\r\n                    nextTick(callback.bind(this, err));\r\n                    return;\r\n                } else\r\n                    throw err;\r\n            }\r\n            offset = 4;\r\n        }\r\n\r\n        // Extract number of rounds\r\n        if (salt.charAt(offset + 2) > '$') {\r\n            err = Error(\"Missing salt rounds\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\r\n            r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\r\n            rounds = r1 + r2,\r\n            real_salt = salt.substring(offset + 3, offset + 25);\r\n        s += minor >= 'a' ? \"\\x00\" : \"\";\r\n\r\n        var passwordb = stringToBytes(s),\r\n            saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\r\n\r\n        /**\r\n         * Finishes hashing.\r\n         * @param {Array.<number>} bytes Byte array\r\n         * @returns {string}\r\n         * @inner\r\n         */\r\n        function finish(bytes) {\r\n            var res = [];\r\n            res.push(\"$2\");\r\n            if (minor >= 'a')\r\n                res.push(minor);\r\n            res.push(\"$\");\r\n            if (rounds < 10)\r\n                res.push(\"0\");\r\n            res.push(rounds.toString());\r\n            res.push(\"$\");\r\n            res.push(base64_encode(saltb, saltb.length));\r\n            res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\r\n            return res.join('');\r\n        }\r\n\r\n        // Sync\r\n        if (typeof callback == 'undefined')\r\n            return finish(_crypt(passwordb, saltb, rounds));\r\n\r\n        // Async\r\n        else {\r\n            _crypt(passwordb, saltb, rounds, function(err, bytes) {\r\n                if (err)\r\n                    callback(err, null);\r\n                else\r\n                    callback(null, finish(bytes));\r\n            }, progressCallback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @expose\r\n     */\r\n    bcrypt.encodeBase64 = base64_encode;\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @expose\r\n     */\r\n    bcrypt.decodeBase64 = base64_decode;\r\n\r\n    return bcrypt;\r\n}));\r\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\workers\\\\api\\\\.wrangler\\\\tmp\\\\bundle-fNTuON\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\workers\\\\api\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\workers\\\\api\\\\.wrangler\\\\tmp\\\\bundle-fNTuON\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\workers\\\\api\\\\.wrangler\\\\tmp\\\\bundle-fNTuON\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\workers\\\\api\\\\src\\\\index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\workers\\\\api\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\workers\\\\api\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\workers\\\\api\\\\src\\\\index.ts\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "import { Hono } from 'hono';\nimport { cors } from 'hono/cors';\nimport { authRoutes } from './routes/auth';\nimport { userRoutes } from './routes/user';\nimport { campaignRoutes } from './routes/campaigns';\n\nexport interface Env {\n  DB: D1Database;\n  SESSIONS: KVNamespace;\n  STORAGE: R2Bucket;\n  JWT_SECRET: string;\n  CORS_ORIGINS: string;\n}\n\nconst app = new Hono<{ Bindings: Env }>();\n\n// CORS middleware\napp.use('*', async (c, next) => {\n  const corsOrigins = c.env.CORS_ORIGINS.split(',');\n  return cors({\n    origin: corsOrigins,\n    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],\n    allowHeaders: ['Content-Type', 'Authorization'],\n    credentials: true,\n  })(c, next);\n});\n\n// Health check\napp.get('/', (c) => {\n  return c.json({ \n    message: 'Monetizr API is running',\n    timestamp: new Date().toISOString(),\n    version: '1.0.0'\n  });\n});\n\n// API routes\napp.route('/auth', authRoutes);\napp.route('/user', userRoutes);\napp.route('/campaigns', campaignRoutes);\n\n// Global error handler\napp.onError((err, c) => {\n  console.error('API Error:', err);\n  return c.json({ \n    error: 'Internal Server Error',\n    message: err.message \n  }, 500);\n});\n\nexport default app;\n", "// src/index.ts\nimport { Hono } from \"./hono.js\";\nexport {\n  Hono\n};\n", "// src/hono.ts\nimport { HonoBase } from \"./hono-base.js\";\nimport { RegExpRouter } from \"./router/reg-exp-router/index.js\";\nimport { SmartRouter } from \"./router/smart-router/index.js\";\nimport { TrieRouter } from \"./router/trie-router/index.js\";\nvar Hono = class extends HonoBase {\n  constructor(options = {}) {\n    super(options);\n    this.router = options.router ?? new SmartRouter({\n      routers: [new RegExpRouter(), new TrieRouter()]\n    });\n  }\n};\nexport {\n  Hono\n};\n", "// src/hono-base.ts\nimport { compose } from \"./compose.js\";\nimport { Context } from \"./context.js\";\nimport { METHODS, METHOD_NAME_ALL, METHOD_NAME_ALL_LOWERCASE } from \"./router.js\";\nimport { COMPOSED_HANDLER } from \"./utils/constants.js\";\nimport { getPath, getPathNoStrict, mergePath } from \"./utils/url.js\";\nvar notFoundHandler = (c) => {\n  return c.text(\"404 Not Found\", 404);\n};\nvar errorHandler = (err, c) => {\n  if (\"getResponse\" in err) {\n    const res = err.getResponse();\n    return c.newResponse(res.body, res);\n  }\n  console.error(err);\n  return c.text(\"Internal Server Error\", 500);\n};\nvar Hono = class {\n  get;\n  post;\n  put;\n  delete;\n  options;\n  patch;\n  all;\n  on;\n  use;\n  router;\n  getPath;\n  _basePath = \"/\";\n  #path = \"/\";\n  routes = [];\n  constructor(options = {}) {\n    const allMethods = [...METHODS, METHOD_NAME_ALL_LOWERCASE];\n    allMethods.forEach((method) => {\n      this[method] = (args1, ...args) => {\n        if (typeof args1 === \"string\") {\n          this.#path = args1;\n        } else {\n          this.#addRoute(method, this.#path, args1);\n        }\n        args.forEach((handler) => {\n          this.#addRoute(method, this.#path, handler);\n        });\n        return this;\n      };\n    });\n    this.on = (method, path, ...handlers) => {\n      for (const p of [path].flat()) {\n        this.#path = p;\n        for (const m of [method].flat()) {\n          handlers.map((handler) => {\n            this.#addRoute(m.toUpperCase(), this.#path, handler);\n          });\n        }\n      }\n      return this;\n    };\n    this.use = (arg1, ...handlers) => {\n      if (typeof arg1 === \"string\") {\n        this.#path = arg1;\n      } else {\n        this.#path = \"*\";\n        handlers.unshift(arg1);\n      }\n      handlers.forEach((handler) => {\n        this.#addRoute(METHOD_NAME_ALL, this.#path, handler);\n      });\n      return this;\n    };\n    const { strict, ...optionsWithoutStrict } = options;\n    Object.assign(this, optionsWithoutStrict);\n    this.getPath = strict ?? true ? options.getPath ?? getPath : getPathNoStrict;\n  }\n  #clone() {\n    const clone = new Hono({\n      router: this.router,\n      getPath: this.getPath\n    });\n    clone.errorHandler = this.errorHandler;\n    clone.#notFoundHandler = this.#notFoundHandler;\n    clone.routes = this.routes;\n    return clone;\n  }\n  #notFoundHandler = notFoundHandler;\n  errorHandler = errorHandler;\n  route(path, app) {\n    const subApp = this.basePath(path);\n    app.routes.map((r) => {\n      let handler;\n      if (app.errorHandler === errorHandler) {\n        handler = r.handler;\n      } else {\n        handler = async (c, next) => (await compose([], app.errorHandler)(c, () => r.handler(c, next))).res;\n        handler[COMPOSED_HANDLER] = r.handler;\n      }\n      subApp.#addRoute(r.method, r.path, handler);\n    });\n    return this;\n  }\n  basePath(path) {\n    const subApp = this.#clone();\n    subApp._basePath = mergePath(this._basePath, path);\n    return subApp;\n  }\n  onError = (handler) => {\n    this.errorHandler = handler;\n    return this;\n  };\n  notFound = (handler) => {\n    this.#notFoundHandler = handler;\n    return this;\n  };\n  mount(path, applicationHandler, options) {\n    let replaceRequest;\n    let optionHandler;\n    if (options) {\n      if (typeof options === \"function\") {\n        optionHandler = options;\n      } else {\n        optionHandler = options.optionHandler;\n        if (options.replaceRequest === false) {\n          replaceRequest = (request) => request;\n        } else {\n          replaceRequest = options.replaceRequest;\n        }\n      }\n    }\n    const getOptions = optionHandler ? (c) => {\n      const options2 = optionHandler(c);\n      return Array.isArray(options2) ? options2 : [options2];\n    } : (c) => {\n      let executionContext = void 0;\n      try {\n        executionContext = c.executionCtx;\n      } catch {\n      }\n      return [c.env, executionContext];\n    };\n    replaceRequest ||= (() => {\n      const mergedPath = mergePath(this._basePath, path);\n      const pathPrefixLength = mergedPath === \"/\" ? 0 : mergedPath.length;\n      return (request) => {\n        const url = new URL(request.url);\n        url.pathname = url.pathname.slice(pathPrefixLength) || \"/\";\n        return new Request(url, request);\n      };\n    })();\n    const handler = async (c, next) => {\n      const res = await applicationHandler(replaceRequest(c.req.raw), ...getOptions(c));\n      if (res) {\n        return res;\n      }\n      await next();\n    };\n    this.#addRoute(METHOD_NAME_ALL, mergePath(path, \"*\"), handler);\n    return this;\n  }\n  #addRoute(method, path, handler) {\n    method = method.toUpperCase();\n    path = mergePath(this._basePath, path);\n    const r = { basePath: this._basePath, path, method, handler };\n    this.router.add(method, path, [handler, r]);\n    this.routes.push(r);\n  }\n  #handleError(err, c) {\n    if (err instanceof Error) {\n      return this.errorHandler(err, c);\n    }\n    throw err;\n  }\n  #dispatch(request, executionCtx, env, method) {\n    if (method === \"HEAD\") {\n      return (async () => new Response(null, await this.#dispatch(request, executionCtx, env, \"GET\")))();\n    }\n    const path = this.getPath(request, { env });\n    const matchResult = this.router.match(method, path);\n    const c = new Context(request, {\n      path,\n      matchResult,\n      env,\n      executionCtx,\n      notFoundHandler: this.#notFoundHandler\n    });\n    if (matchResult[0].length === 1) {\n      let res;\n      try {\n        res = matchResult[0][0][0][0](c, async () => {\n          c.res = await this.#notFoundHandler(c);\n        });\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n      return res instanceof Promise ? res.then(\n        (resolved) => resolved || (c.finalized ? c.res : this.#notFoundHandler(c))\n      ).catch((err) => this.#handleError(err, c)) : res ?? this.#notFoundHandler(c);\n    }\n    const composed = compose(matchResult[0], this.errorHandler, this.#notFoundHandler);\n    return (async () => {\n      try {\n        const context = await composed(c);\n        if (!context.finalized) {\n          throw new Error(\n            \"Context is not finalized. Did you forget to return a Response object or `await next()`?\"\n          );\n        }\n        return context.res;\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n    })();\n  }\n  fetch = (request, ...rest) => {\n    return this.#dispatch(request, rest[1], rest[0], request.method);\n  };\n  request = (input, requestInit, Env, executionCtx) => {\n    if (input instanceof Request) {\n      return this.fetch(requestInit ? new Request(input, requestInit) : input, Env, executionCtx);\n    }\n    input = input.toString();\n    return this.fetch(\n      new Request(\n        /^https?:\\/\\//.test(input) ? input : `http://localhost${mergePath(\"/\", input)}`,\n        requestInit\n      ),\n      Env,\n      executionCtx\n    );\n  };\n  fire = () => {\n    addEventListener(\"fetch\", (event) => {\n      event.respondWith(this.#dispatch(event.request, event, void 0, event.request.method));\n    });\n  };\n};\nexport {\n  Hono as HonoBase\n};\n", "// src/compose.ts\nvar compose = (middleware, onError, onNotFound) => {\n  return (context, next) => {\n    let index = -1;\n    return dispatch(0);\n    async function dispatch(i) {\n      if (i <= index) {\n        throw new Error(\"next() called multiple times\");\n      }\n      index = i;\n      let res;\n      let isError = false;\n      let handler;\n      if (middleware[i]) {\n        handler = middleware[i][0][0];\n        context.req.routeIndex = i;\n      } else {\n        handler = i === middleware.length && next || void 0;\n      }\n      if (handler) {\n        try {\n          res = await handler(context, () => dispatch(i + 1));\n        } catch (err) {\n          if (err instanceof Error && onError) {\n            context.error = err;\n            res = await onError(err, context);\n            isError = true;\n          } else {\n            throw err;\n          }\n        }\n      } else {\n        if (context.finalized === false && onNotFound) {\n          res = await onNotFound(context);\n        }\n      }\n      if (res && (context.finalized === false || isError)) {\n        context.res = res;\n      }\n      return context;\n    }\n  };\n};\nexport {\n  compose\n};\n", "// src/context.ts\nimport { HonoRequest } from \"./request.js\";\nimport { HtmlEscapedCallbackPhase, resolveCallback } from \"./utils/html.js\";\nvar TEXT_PLAIN = \"text/plain; charset=UTF-8\";\nvar setDefaultContentType = (contentType, headers) => {\n  return {\n    \"Content-Type\": contentType,\n    ...headers\n  };\n};\nvar Context = class {\n  #rawRequest;\n  #req;\n  env = {};\n  #var;\n  finalized = false;\n  error;\n  #status;\n  #executionCtx;\n  #res;\n  #layout;\n  #renderer;\n  #notFoundHandler;\n  #preparedHeaders;\n  #matchResult;\n  #path;\n  constructor(req, options) {\n    this.#rawRequest = req;\n    if (options) {\n      this.#executionCtx = options.executionCtx;\n      this.env = options.env;\n      this.#notFoundHandler = options.notFoundHandler;\n      this.#path = options.path;\n      this.#matchResult = options.matchResult;\n    }\n  }\n  get req() {\n    this.#req ??= new HonoRequest(this.#rawRequest, this.#path, this.#matchResult);\n    return this.#req;\n  }\n  get event() {\n    if (this.#executionCtx && \"respondWith\" in this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no FetchEvent\");\n    }\n  }\n  get executionCtx() {\n    if (this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no ExecutionContext\");\n    }\n  }\n  get res() {\n    return this.#res ||= new Response(null, {\n      headers: this.#preparedHeaders ??= new Headers()\n    });\n  }\n  set res(_res) {\n    if (this.#res && _res) {\n      _res = new Response(_res.body, _res);\n      for (const [k, v] of this.#res.headers.entries()) {\n        if (k === \"content-type\") {\n          continue;\n        }\n        if (k === \"set-cookie\") {\n          const cookies = this.#res.headers.getSetCookie();\n          _res.headers.delete(\"set-cookie\");\n          for (const cookie of cookies) {\n            _res.headers.append(\"set-cookie\", cookie);\n          }\n        } else {\n          _res.headers.set(k, v);\n        }\n      }\n    }\n    this.#res = _res;\n    this.finalized = true;\n  }\n  render = (...args) => {\n    this.#renderer ??= (content) => this.html(content);\n    return this.#renderer(...args);\n  };\n  setLayout = (layout) => this.#layout = layout;\n  getLayout = () => this.#layout;\n  setRenderer = (renderer) => {\n    this.#renderer = renderer;\n  };\n  header = (name, value, options) => {\n    if (this.finalized) {\n      this.#res = new Response(this.#res.body, this.#res);\n    }\n    const headers = this.#res ? this.#res.headers : this.#preparedHeaders ??= new Headers();\n    if (value === void 0) {\n      headers.delete(name);\n    } else if (options?.append) {\n      headers.append(name, value);\n    } else {\n      headers.set(name, value);\n    }\n  };\n  status = (status) => {\n    this.#status = status;\n  };\n  set = (key, value) => {\n    this.#var ??= /* @__PURE__ */ new Map();\n    this.#var.set(key, value);\n  };\n  get = (key) => {\n    return this.#var ? this.#var.get(key) : void 0;\n  };\n  get var() {\n    if (!this.#var) {\n      return {};\n    }\n    return Object.fromEntries(this.#var);\n  }\n  #newResponse(data, arg, headers) {\n    const responseHeaders = this.#res ? new Headers(this.#res.headers) : this.#preparedHeaders ?? new Headers();\n    if (typeof arg === \"object\" && \"headers\" in arg) {\n      const argHeaders = arg.headers instanceof Headers ? arg.headers : new Headers(arg.headers);\n      for (const [key, value] of argHeaders) {\n        if (key.toLowerCase() === \"set-cookie\") {\n          responseHeaders.append(key, value);\n        } else {\n          responseHeaders.set(key, value);\n        }\n      }\n    }\n    if (headers) {\n      for (const [k, v] of Object.entries(headers)) {\n        if (typeof v === \"string\") {\n          responseHeaders.set(k, v);\n        } else {\n          responseHeaders.delete(k);\n          for (const v2 of v) {\n            responseHeaders.append(k, v2);\n          }\n        }\n      }\n    }\n    const status = typeof arg === \"number\" ? arg : arg?.status ?? this.#status;\n    return new Response(data, { status, headers: responseHeaders });\n  }\n  newResponse = (...args) => this.#newResponse(...args);\n  body = (data, arg, headers) => this.#newResponse(data, arg, headers);\n  text = (text, arg, headers) => {\n    return !this.#preparedHeaders && !this.#status && !arg && !headers && !this.finalized ? new Response(text) : this.#newResponse(\n      text,\n      arg,\n      setDefaultContentType(TEXT_PLAIN, headers)\n    );\n  };\n  json = (object, arg, headers) => {\n    return this.#newResponse(\n      JSON.stringify(object),\n      arg,\n      setDefaultContentType(\"application/json\", headers)\n    );\n  };\n  html = (html, arg, headers) => {\n    const res = (html2) => this.#newResponse(html2, arg, setDefaultContentType(\"text/html; charset=UTF-8\", headers));\n    return typeof html === \"object\" ? resolveCallback(html, HtmlEscapedCallbackPhase.Stringify, false, {}).then(res) : res(html);\n  };\n  redirect = (location, status) => {\n    this.header(\"Location\", String(location));\n    return this.newResponse(null, status ?? 302);\n  };\n  notFound = () => {\n    this.#notFoundHandler ??= () => new Response();\n    return this.#notFoundHandler(this);\n  };\n};\nexport {\n  Context,\n  TEXT_PLAIN\n};\n", "// src/request.ts\nimport { GET_MATCH_RESULT } from \"./request/constants.js\";\nimport { parseBody } from \"./utils/body.js\";\nimport { decodeURIComponent_, getQueryParam, getQueryParams, tryDecode } from \"./utils/url.js\";\nvar tryDecodeURIComponent = (str) => tryDecode(str, decodeURIComponent_);\nvar HonoRequest = class {\n  raw;\n  #validatedData;\n  #matchResult;\n  routeIndex = 0;\n  path;\n  bodyCache = {};\n  constructor(request, path = \"/\", matchResult = [[]]) {\n    this.raw = request;\n    this.path = path;\n    this.#matchResult = matchResult;\n    this.#validatedData = {};\n  }\n  param(key) {\n    return key ? this.#getDecodedParam(key) : this.#getAllDecodedParams();\n  }\n  #getDecodedParam(key) {\n    const paramKey = this.#matchResult[0][this.routeIndex][1][key];\n    const param = this.#getParamValue(paramKey);\n    return param ? /\\%/.test(param) ? tryDecodeURIComponent(param) : param : void 0;\n  }\n  #getAllDecodedParams() {\n    const decoded = {};\n    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);\n    for (const key of keys) {\n      const value = this.#getParamValue(this.#matchResult[0][this.routeIndex][1][key]);\n      if (value && typeof value === \"string\") {\n        decoded[key] = /\\%/.test(value) ? tryDecodeURIComponent(value) : value;\n      }\n    }\n    return decoded;\n  }\n  #getParamValue(paramKey) {\n    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;\n  }\n  query(key) {\n    return getQueryParam(this.url, key);\n  }\n  queries(key) {\n    return getQueryParams(this.url, key);\n  }\n  header(name) {\n    if (name) {\n      return this.raw.headers.get(name) ?? void 0;\n    }\n    const headerData = {};\n    this.raw.headers.forEach((value, key) => {\n      headerData[key] = value;\n    });\n    return headerData;\n  }\n  async parseBody(options) {\n    return this.bodyCache.parsedBody ??= await parseBody(this, options);\n  }\n  #cachedBody = (key) => {\n    const { bodyCache, raw } = this;\n    const cachedBody = bodyCache[key];\n    if (cachedBody) {\n      return cachedBody;\n    }\n    const anyCachedKey = Object.keys(bodyCache)[0];\n    if (anyCachedKey) {\n      return bodyCache[anyCachedKey].then((body) => {\n        if (anyCachedKey === \"json\") {\n          body = JSON.stringify(body);\n        }\n        return new Response(body)[key]();\n      });\n    }\n    return bodyCache[key] = raw[key]();\n  };\n  json() {\n    return this.#cachedBody(\"json\");\n  }\n  text() {\n    return this.#cachedBody(\"text\");\n  }\n  arrayBuffer() {\n    return this.#cachedBody(\"arrayBuffer\");\n  }\n  blob() {\n    return this.#cachedBody(\"blob\");\n  }\n  formData() {\n    return this.#cachedBody(\"formData\");\n  }\n  addValidatedData(target, data) {\n    this.#validatedData[target] = data;\n  }\n  valid(target) {\n    return this.#validatedData[target];\n  }\n  get url() {\n    return this.raw.url;\n  }\n  get method() {\n    return this.raw.method;\n  }\n  get [GET_MATCH_RESULT]() {\n    return this.#matchResult;\n  }\n  get matchedRoutes() {\n    return this.#matchResult[0].map(([[, route]]) => route);\n  }\n  get routePath() {\n    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;\n  }\n};\nexport {\n  HonoRequest\n};\n", "// src/request/constants.ts\nvar GET_MATCH_RESULT = Symbol();\nexport {\n  GET_MATCH_RESULT\n};\n", "// src/utils/body.ts\nimport { HonoRequest } from \"../request.js\";\nvar parseBody = async (request, options = /* @__PURE__ */ Object.create(null)) => {\n  const { all = false, dot = false } = options;\n  const headers = request instanceof HonoRequest ? request.raw.headers : request.headers;\n  const contentType = headers.get(\"Content-Type\");\n  if (contentType?.startsWith(\"multipart/form-data\") || contentType?.startsWith(\"application/x-www-form-urlencoded\")) {\n    return parseFormData(request, { all, dot });\n  }\n  return {};\n};\nasync function parseFormData(request, options) {\n  const formData = await request.formData();\n  if (formData) {\n    return convertFormDataToBodyData(formData, options);\n  }\n  return {};\n}\nfunction convertFormDataToBodyData(formData, options) {\n  const form = /* @__PURE__ */ Object.create(null);\n  formData.forEach((value, key) => {\n    const shouldParseAllValues = options.all || key.endsWith(\"[]\");\n    if (!shouldParseAllValues) {\n      form[key] = value;\n    } else {\n      handleParsingAllValues(form, key, value);\n    }\n  });\n  if (options.dot) {\n    Object.entries(form).forEach(([key, value]) => {\n      const shouldParseDotValues = key.includes(\".\");\n      if (shouldParseDotValues) {\n        handleParsingNestedValues(form, key, value);\n        delete form[key];\n      }\n    });\n  }\n  return form;\n}\nvar handleParsingAllValues = (form, key, value) => {\n  if (form[key] !== void 0) {\n    if (Array.isArray(form[key])) {\n      ;\n      form[key].push(value);\n    } else {\n      form[key] = [form[key], value];\n    }\n  } else {\n    if (!key.endsWith(\"[]\")) {\n      form[key] = value;\n    } else {\n      form[key] = [value];\n    }\n  }\n};\nvar handleParsingNestedValues = (form, key, value) => {\n  let nestedForm = form;\n  const keys = key.split(\".\");\n  keys.forEach((key2, index) => {\n    if (index === keys.length - 1) {\n      nestedForm[key2] = value;\n    } else {\n      if (!nestedForm[key2] || typeof nestedForm[key2] !== \"object\" || Array.isArray(nestedForm[key2]) || nestedForm[key2] instanceof File) {\n        nestedForm[key2] = /* @__PURE__ */ Object.create(null);\n      }\n      nestedForm = nestedForm[key2];\n    }\n  });\n};\nexport {\n  parseBody\n};\n", "// src/utils/url.ts\nvar splitPath = (path) => {\n  const paths = path.split(\"/\");\n  if (paths[0] === \"\") {\n    paths.shift();\n  }\n  return paths;\n};\nvar splitRoutingPath = (routePath) => {\n  const { groups, path } = extractGroupsFromPath(routePath);\n  const paths = splitPath(path);\n  return replaceGroupMarks(paths, groups);\n};\nvar extractGroupsFromPath = (path) => {\n  const groups = [];\n  path = path.replace(/\\{[^}]+\\}/g, (match, index) => {\n    const mark = `@${index}`;\n    groups.push([mark, match]);\n    return mark;\n  });\n  return { groups, path };\n};\nvar replaceGroupMarks = (paths, groups) => {\n  for (let i = groups.length - 1; i >= 0; i--) {\n    const [mark] = groups[i];\n    for (let j = paths.length - 1; j >= 0; j--) {\n      if (paths[j].includes(mark)) {\n        paths[j] = paths[j].replace(mark, groups[i][1]);\n        break;\n      }\n    }\n  }\n  return paths;\n};\nvar patternCache = {};\nvar getPattern = (label, next) => {\n  if (label === \"*\") {\n    return \"*\";\n  }\n  const match = label.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n  if (match) {\n    const cacheKey = `${label}#${next}`;\n    if (!patternCache[cacheKey]) {\n      if (match[2]) {\n        patternCache[cacheKey] = next && next[0] !== \":\" && next[0] !== \"*\" ? [cacheKey, match[1], new RegExp(`^${match[2]}(?=/${next})`)] : [label, match[1], new RegExp(`^${match[2]}$`)];\n      } else {\n        patternCache[cacheKey] = [label, match[1], true];\n      }\n    }\n    return patternCache[cacheKey];\n  }\n  return null;\n};\nvar tryDecode = (str, decoder) => {\n  try {\n    return decoder(str);\n  } catch {\n    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {\n      try {\n        return decoder(match);\n      } catch {\n        return match;\n      }\n    });\n  }\n};\nvar tryDecodeURI = (str) => tryDecode(str, decodeURI);\nvar getPath = (request) => {\n  const url = request.url;\n  const start = url.indexOf(\n    \"/\",\n    url.charCodeAt(9) === 58 ? 13 : 8\n  );\n  let i = start;\n  for (; i < url.length; i++) {\n    const charCode = url.charCodeAt(i);\n    if (charCode === 37) {\n      const queryIndex = url.indexOf(\"?\", i);\n      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);\n      return tryDecodeURI(path.includes(\"%25\") ? path.replace(/%25/g, \"%2525\") : path);\n    } else if (charCode === 63) {\n      break;\n    }\n  }\n  return url.slice(start, i);\n};\nvar getQueryStrings = (url) => {\n  const queryIndex = url.indexOf(\"?\", 8);\n  return queryIndex === -1 ? \"\" : \"?\" + url.slice(queryIndex + 1);\n};\nvar getPathNoStrict = (request) => {\n  const result = getPath(request);\n  return result.length > 1 && result.at(-1) === \"/\" ? result.slice(0, -1) : result;\n};\nvar mergePath = (base, sub, ...rest) => {\n  if (rest.length) {\n    sub = mergePath(sub, ...rest);\n  }\n  return `${base?.[0] === \"/\" ? \"\" : \"/\"}${base}${sub === \"/\" ? \"\" : `${base?.at(-1) === \"/\" ? \"\" : \"/\"}${sub?.[0] === \"/\" ? sub.slice(1) : sub}`}`;\n};\nvar checkOptionalParameter = (path) => {\n  if (path.charCodeAt(path.length - 1) !== 63 || !path.includes(\":\")) {\n    return null;\n  }\n  const segments = path.split(\"/\");\n  const results = [];\n  let basePath = \"\";\n  segments.forEach((segment) => {\n    if (segment !== \"\" && !/\\:/.test(segment)) {\n      basePath += \"/\" + segment;\n    } else if (/\\:/.test(segment)) {\n      if (/\\?/.test(segment)) {\n        if (results.length === 0 && basePath === \"\") {\n          results.push(\"/\");\n        } else {\n          results.push(basePath);\n        }\n        const optionalSegment = segment.replace(\"?\", \"\");\n        basePath += \"/\" + optionalSegment;\n        results.push(basePath);\n      } else {\n        basePath += \"/\" + segment;\n      }\n    }\n  });\n  return results.filter((v, i, a) => a.indexOf(v) === i);\n};\nvar _decodeURI = (value) => {\n  if (!/[%+]/.test(value)) {\n    return value;\n  }\n  if (value.indexOf(\"+\") !== -1) {\n    value = value.replace(/\\+/g, \" \");\n  }\n  return value.indexOf(\"%\") !== -1 ? tryDecode(value, decodeURIComponent_) : value;\n};\nvar _getQueryParam = (url, key, multiple) => {\n  let encoded;\n  if (!multiple && key && !/[%+]/.test(key)) {\n    let keyIndex2 = url.indexOf(`?${key}`, 8);\n    if (keyIndex2 === -1) {\n      keyIndex2 = url.indexOf(`&${key}`, 8);\n    }\n    while (keyIndex2 !== -1) {\n      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);\n      if (trailingKeyCode === 61) {\n        const valueIndex = keyIndex2 + key.length + 2;\n        const endIndex = url.indexOf(\"&\", valueIndex);\n        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));\n      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {\n        return \"\";\n      }\n      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);\n    }\n    encoded = /[%+]/.test(url);\n    if (!encoded) {\n      return void 0;\n    }\n  }\n  const results = {};\n  encoded ??= /[%+]/.test(url);\n  let keyIndex = url.indexOf(\"?\", 8);\n  while (keyIndex !== -1) {\n    const nextKeyIndex = url.indexOf(\"&\", keyIndex + 1);\n    let valueIndex = url.indexOf(\"=\", keyIndex);\n    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {\n      valueIndex = -1;\n    }\n    let name = url.slice(\n      keyIndex + 1,\n      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex\n    );\n    if (encoded) {\n      name = _decodeURI(name);\n    }\n    keyIndex = nextKeyIndex;\n    if (name === \"\") {\n      continue;\n    }\n    let value;\n    if (valueIndex === -1) {\n      value = \"\";\n    } else {\n      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);\n      if (encoded) {\n        value = _decodeURI(value);\n      }\n    }\n    if (multiple) {\n      if (!(results[name] && Array.isArray(results[name]))) {\n        results[name] = [];\n      }\n      ;\n      results[name].push(value);\n    } else {\n      results[name] ??= value;\n    }\n  }\n  return key ? results[key] : results;\n};\nvar getQueryParam = _getQueryParam;\nvar getQueryParams = (url, key) => {\n  return _getQueryParam(url, key, true);\n};\nvar decodeURIComponent_ = decodeURIComponent;\nexport {\n  checkOptionalParameter,\n  decodeURIComponent_,\n  getPath,\n  getPathNoStrict,\n  getPattern,\n  getQueryParam,\n  getQueryParams,\n  getQueryStrings,\n  mergePath,\n  splitPath,\n  splitRoutingPath,\n  tryDecode\n};\n", "// src/utils/html.ts\nvar HtmlEscapedCallbackPhase = {\n  Stringify: 1,\n  BeforeStream: 2,\n  Stream: 3\n};\nvar raw = (value, callbacks) => {\n  const escapedString = new String(value);\n  escapedString.isEscaped = true;\n  escapedString.callbacks = callbacks;\n  return escapedString;\n};\nvar escapeRe = /[&<>'\"]/;\nvar stringBufferToString = async (buffer, callbacks) => {\n  let str = \"\";\n  callbacks ||= [];\n  const resolvedBuffer = await Promise.all(buffer);\n  for (let i = resolvedBuffer.length - 1; ; i--) {\n    str += resolvedBuffer[i];\n    i--;\n    if (i < 0) {\n      break;\n    }\n    let r = resolvedBuffer[i];\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    const isEscaped = r.isEscaped;\n    r = await (typeof r === \"object\" ? r.toString() : r);\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    if (r.isEscaped ?? isEscaped) {\n      str += r;\n    } else {\n      const buf = [str];\n      escapeToBuffer(r, buf);\n      str = buf[0];\n    }\n  }\n  return raw(str, callbacks);\n};\nvar escapeToBuffer = (str, buffer) => {\n  const match = str.search(escapeRe);\n  if (match === -1) {\n    buffer[0] += str;\n    return;\n  }\n  let escape;\n  let index;\n  let lastIndex = 0;\n  for (index = match; index < str.length; index++) {\n    switch (str.charCodeAt(index)) {\n      case 34:\n        escape = \"&quot;\";\n        break;\n      case 39:\n        escape = \"&#39;\";\n        break;\n      case 38:\n        escape = \"&amp;\";\n        break;\n      case 60:\n        escape = \"&lt;\";\n        break;\n      case 62:\n        escape = \"&gt;\";\n        break;\n      default:\n        continue;\n    }\n    buffer[0] += str.substring(lastIndex, index) + escape;\n    lastIndex = index + 1;\n  }\n  buffer[0] += str.substring(lastIndex, index);\n};\nvar resolveCallbackSync = (str) => {\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return str;\n  }\n  const buffer = [str];\n  const context = {};\n  callbacks.forEach((c) => c({ phase: HtmlEscapedCallbackPhase.Stringify, buffer, context }));\n  return buffer[0];\n};\nvar resolveCallback = async (str, phase, preserveCallbacks, context, buffer) => {\n  if (typeof str === \"object\" && !(str instanceof String)) {\n    if (!(str instanceof Promise)) {\n      str = str.toString();\n    }\n    if (str instanceof Promise) {\n      str = await str;\n    }\n  }\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return Promise.resolve(str);\n  }\n  if (buffer) {\n    buffer[0] += str;\n  } else {\n    buffer = [str];\n  }\n  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(\n    (res) => Promise.all(\n      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))\n    ).then(() => buffer[0])\n  );\n  if (preserveCallbacks) {\n    return raw(await resStr, callbacks);\n  } else {\n    return resStr;\n  }\n};\nexport {\n  HtmlEscapedCallbackPhase,\n  escapeToBuffer,\n  raw,\n  resolveCallback,\n  resolveCallbackSync,\n  stringBufferToString\n};\n", "// src/router.ts\nvar METHOD_NAME_ALL = \"ALL\";\nvar METHOD_NAME_ALL_LOWERCASE = \"all\";\nvar METHODS = [\"get\", \"post\", \"put\", \"delete\", \"options\", \"patch\"];\nvar MESSAGE_MATCHER_IS_ALREADY_BUILT = \"Can not add a route since the matcher is already built.\";\nvar UnsupportedPathError = class extends Error {\n};\nexport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHODS,\n  METHOD_NAME_ALL,\n  METHOD_NAME_ALL_LOWERCASE,\n  UnsupportedPathError\n};\n", "// src/utils/constants.ts\nvar COMPOSED_HANDLER = \"__COMPOSED_HANDLER\";\nexport {\n  COMPOSED_HANDLER\n};\n", "// src/router/reg-exp-router/index.ts\nimport { RegExpRouter } from \"./router.js\";\nexport {\n  RegExpRouter\n};\n", "// src/router/reg-exp-router/router.ts\nimport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHOD_NAME_ALL,\n  UnsupportedPathError\n} from \"../../router.js\";\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { PATH_ERROR } from \"./node.js\";\nimport { Trie } from \"./trie.js\";\nvar emptyParam = [];\nvar nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];\nvar wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\nfunction buildWildcardRegExp(path) {\n  return wildcardRegExpCache[path] ??= new RegExp(\n    path === \"*\" ? \"\" : `^${path.replace(\n      /\\/\\*$|([.\\\\+*[^\\]$()])/g,\n      (_, metaChar) => metaChar ? `\\\\${metaChar}` : \"(?:|/.*)\"\n    )}$`\n  );\n}\nfunction clearWildcardRegExpCache() {\n  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\n}\nfunction buildMatcherFromPreprocessedRoutes(routes) {\n  const trie = new Trie();\n  const handlerData = [];\n  if (routes.length === 0) {\n    return nullMatcher;\n  }\n  const routesWithStaticPathFlag = routes.map(\n    (route) => [!/\\*|\\/:/.test(route[0]), ...route]\n  ).sort(\n    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length\n  );\n  const staticMap = /* @__PURE__ */ Object.create(null);\n  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {\n    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];\n    if (pathErrorCheckOnly) {\n      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];\n    } else {\n      j++;\n    }\n    let paramAssoc;\n    try {\n      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);\n    } catch (e) {\n      throw e === PATH_ERROR ? new UnsupportedPathError(path) : e;\n    }\n    if (pathErrorCheckOnly) {\n      continue;\n    }\n    handlerData[j] = handlers.map(([h, paramCount]) => {\n      const paramIndexMap = /* @__PURE__ */ Object.create(null);\n      paramCount -= 1;\n      for (; paramCount >= 0; paramCount--) {\n        const [key, value] = paramAssoc[paramCount];\n        paramIndexMap[key] = value;\n      }\n      return [h, paramIndexMap];\n    });\n  }\n  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();\n  for (let i = 0, len = handlerData.length; i < len; i++) {\n    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {\n      const map = handlerData[i][j]?.[1];\n      if (!map) {\n        continue;\n      }\n      const keys = Object.keys(map);\n      for (let k = 0, len3 = keys.length; k < len3; k++) {\n        map[keys[k]] = paramReplacementMap[map[keys[k]]];\n      }\n    }\n  }\n  const handlerMap = [];\n  for (const i in indexReplacementMap) {\n    handlerMap[i] = handlerData[indexReplacementMap[i]];\n  }\n  return [regexp, handlerMap, staticMap];\n}\nfunction findMiddleware(middleware, path) {\n  if (!middleware) {\n    return void 0;\n  }\n  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {\n    if (buildWildcardRegExp(k).test(path)) {\n      return [...middleware[k]];\n    }\n  }\n  return void 0;\n}\nvar RegExpRouter = class {\n  name = \"RegExpRouter\";\n  #middleware;\n  #routes;\n  constructor() {\n    this.#middleware = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n    this.#routes = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n  }\n  add(method, path, handler) {\n    const middleware = this.#middleware;\n    const routes = this.#routes;\n    if (!middleware || !routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    if (!middleware[method]) {\n      ;\n      [middleware, routes].forEach((handlerMap) => {\n        handlerMap[method] = /* @__PURE__ */ Object.create(null);\n        Object.keys(handlerMap[METHOD_NAME_ALL]).forEach((p) => {\n          handlerMap[method][p] = [...handlerMap[METHOD_NAME_ALL][p]];\n        });\n      });\n    }\n    if (path === \"/*\") {\n      path = \"*\";\n    }\n    const paramCount = (path.match(/\\/:/g) || []).length;\n    if (/\\*$/.test(path)) {\n      const re = buildWildcardRegExp(path);\n      if (method === METHOD_NAME_ALL) {\n        Object.keys(middleware).forEach((m) => {\n          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n        });\n      } else {\n        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n      }\n      Object.keys(middleware).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(middleware[m]).forEach((p) => {\n            re.test(p) && middleware[m][p].push([handler, paramCount]);\n          });\n        }\n      });\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(routes[m]).forEach(\n            (p) => re.test(p) && routes[m][p].push([handler, paramCount])\n          );\n        }\n      });\n      return;\n    }\n    const paths = checkOptionalParameter(path) || [path];\n    for (let i = 0, len = paths.length; i < len; i++) {\n      const path2 = paths[i];\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          routes[m][path2] ||= [\n            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[METHOD_NAME_ALL], path2) || []\n          ];\n          routes[m][path2].push([handler, paramCount - len + i + 1]);\n        }\n      });\n    }\n  }\n  match(method, path) {\n    clearWildcardRegExpCache();\n    const matchers = this.#buildAllMatchers();\n    this.match = (method2, path2) => {\n      const matcher = matchers[method2] || matchers[METHOD_NAME_ALL];\n      const staticMatch = matcher[2][path2];\n      if (staticMatch) {\n        return staticMatch;\n      }\n      const match = path2.match(matcher[0]);\n      if (!match) {\n        return [[], emptyParam];\n      }\n      const index = match.indexOf(\"\", 1);\n      return [matcher[1][index], match];\n    };\n    return this.match(method, path);\n  }\n  #buildAllMatchers() {\n    const matchers = /* @__PURE__ */ Object.create(null);\n    Object.keys(this.#routes).concat(Object.keys(this.#middleware)).forEach((method) => {\n      matchers[method] ||= this.#buildMatcher(method);\n    });\n    this.#middleware = this.#routes = void 0;\n    return matchers;\n  }\n  #buildMatcher(method) {\n    const routes = [];\n    let hasOwnRoute = method === METHOD_NAME_ALL;\n    [this.#middleware, this.#routes].forEach((r) => {\n      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];\n      if (ownRoute.length !== 0) {\n        hasOwnRoute ||= true;\n        routes.push(...ownRoute);\n      } else if (method !== METHOD_NAME_ALL) {\n        routes.push(\n          ...Object.keys(r[METHOD_NAME_ALL]).map((path) => [path, r[METHOD_NAME_ALL][path]])\n        );\n      }\n    });\n    if (!hasOwnRoute) {\n      return null;\n    } else {\n      return buildMatcherFromPreprocessedRoutes(routes);\n    }\n  }\n};\nexport {\n  RegExpRouter\n};\n", "// src/router/reg-exp-router/node.ts\nvar LABEL_REG_EXP_STR = \"[^/]+\";\nvar ONLY_WILDCARD_REG_EXP_STR = \".*\";\nvar TAIL_WILDCARD_REG_EXP_STR = \"(?:|/.*)\";\nvar PATH_ERROR = Symbol();\nvar regExpMetaChars = new Set(\".\\\\+*[^]$()\");\nfunction compareKey(a, b) {\n  if (a.length === 1) {\n    return b.length === 1 ? a < b ? -1 : 1 : -1;\n  }\n  if (b.length === 1) {\n    return 1;\n  }\n  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {\n    return 1;\n  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {\n    return -1;\n  }\n  if (a === LABEL_REG_EXP_STR) {\n    return 1;\n  } else if (b === LABEL_REG_EXP_STR) {\n    return -1;\n  }\n  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;\n}\nvar Node = class {\n  #index;\n  #varIndex;\n  #children = /* @__PURE__ */ Object.create(null);\n  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {\n    if (tokens.length === 0) {\n      if (this.#index !== void 0) {\n        throw PATH_ERROR;\n      }\n      if (pathErrorCheckOnly) {\n        return;\n      }\n      this.#index = index;\n      return;\n    }\n    const [token, ...restTokens] = tokens;\n    const pattern = token === \"*\" ? restTokens.length === 0 ? [\"\", \"\", ONLY_WILDCARD_REG_EXP_STR] : [\"\", \"\", LABEL_REG_EXP_STR] : token === \"/*\" ? [\"\", \"\", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n    let node;\n    if (pattern) {\n      const name = pattern[1];\n      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;\n      if (name && pattern[2]) {\n        regexpStr = regexpStr.replace(/^\\((?!\\?:)(?=[^)]+\\)$)/, \"(?:\");\n        if (/\\((?!\\?:)/.test(regexpStr)) {\n          throw PATH_ERROR;\n        }\n      }\n      node = this.#children[regexpStr];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[regexpStr] = new Node();\n        if (name !== \"\") {\n          node.#varIndex = context.varIndex++;\n        }\n      }\n      if (!pathErrorCheckOnly && name !== \"\") {\n        paramMap.push([name, node.#varIndex]);\n      }\n    } else {\n      node = this.#children[token];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[token] = new Node();\n      }\n    }\n    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);\n  }\n  buildRegExpStr() {\n    const childKeys = Object.keys(this.#children).sort(compareKey);\n    const strList = childKeys.map((k) => {\n      const c = this.#children[k];\n      return (typeof c.#varIndex === \"number\" ? `(${k})@${c.#varIndex}` : regExpMetaChars.has(k) ? `\\\\${k}` : k) + c.buildRegExpStr();\n    });\n    if (typeof this.#index === \"number\") {\n      strList.unshift(`#${this.#index}`);\n    }\n    if (strList.length === 0) {\n      return \"\";\n    }\n    if (strList.length === 1) {\n      return strList[0];\n    }\n    return \"(?:\" + strList.join(\"|\") + \")\";\n  }\n};\nexport {\n  Node,\n  PATH_ERROR\n};\n", "// src/router/reg-exp-router/trie.ts\nimport { Node } from \"./node.js\";\nvar Trie = class {\n  #context = { varIndex: 0 };\n  #root = new Node();\n  insert(path, index, pathErrorCheckOnly) {\n    const paramAssoc = [];\n    const groups = [];\n    for (let i = 0; ; ) {\n      let replaced = false;\n      path = path.replace(/\\{[^}]+\\}/g, (m) => {\n        const mark = `@\\\\${i}`;\n        groups[i] = [mark, m];\n        i++;\n        replaced = true;\n        return mark;\n      });\n      if (!replaced) {\n        break;\n      }\n    }\n    const tokens = path.match(/(?::[^\\/]+)|(?:\\/\\*$)|./g) || [];\n    for (let i = groups.length - 1; i >= 0; i--) {\n      const [mark] = groups[i];\n      for (let j = tokens.length - 1; j >= 0; j--) {\n        if (tokens[j].indexOf(mark) !== -1) {\n          tokens[j] = tokens[j].replace(mark, groups[i][1]);\n          break;\n        }\n      }\n    }\n    this.#root.insert(tokens, index, paramAssoc, this.#context, pathErrorCheckOnly);\n    return paramAssoc;\n  }\n  buildRegExp() {\n    let regexp = this.#root.buildRegExpStr();\n    if (regexp === \"\") {\n      return [/^$/, [], []];\n    }\n    let captureIndex = 0;\n    const indexReplacementMap = [];\n    const paramReplacementMap = [];\n    regexp = regexp.replace(/#(\\d+)|@(\\d+)|\\.\\*\\$/g, (_, handlerIndex, paramIndex) => {\n      if (handlerIndex !== void 0) {\n        indexReplacementMap[++captureIndex] = Number(handlerIndex);\n        return \"$()\";\n      }\n      if (paramIndex !== void 0) {\n        paramReplacementMap[Number(paramIndex)] = ++captureIndex;\n        return \"\";\n      }\n      return \"\";\n    });\n    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];\n  }\n};\nexport {\n  Trie\n};\n", "// src/router/smart-router/index.ts\nimport { SmartRouter } from \"./router.js\";\nexport {\n  SmartRouter\n};\n", "// src/router/smart-router/router.ts\nimport { MESSAGE_MATCHER_IS_ALREADY_BUILT, UnsupportedPathError } from \"../../router.js\";\nvar SmartRouter = class {\n  name = \"SmartRouter\";\n  #routers = [];\n  #routes = [];\n  constructor(init) {\n    this.#routers = init.routers;\n  }\n  add(method, path, handler) {\n    if (!this.#routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    this.#routes.push([method, path, handler]);\n  }\n  match(method, path) {\n    if (!this.#routes) {\n      throw new Error(\"Fatal error\");\n    }\n    const routers = this.#routers;\n    const routes = this.#routes;\n    const len = routers.length;\n    let i = 0;\n    let res;\n    for (; i < len; i++) {\n      const router = routers[i];\n      try {\n        for (let i2 = 0, len2 = routes.length; i2 < len2; i2++) {\n          router.add(...routes[i2]);\n        }\n        res = router.match(method, path);\n      } catch (e) {\n        if (e instanceof UnsupportedPathError) {\n          continue;\n        }\n        throw e;\n      }\n      this.match = router.match.bind(router);\n      this.#routers = [router];\n      this.#routes = void 0;\n      break;\n    }\n    if (i === len) {\n      throw new Error(\"Fatal error\");\n    }\n    this.name = `SmartRouter + ${this.activeRouter.name}`;\n    return res;\n  }\n  get activeRouter() {\n    if (this.#routes || this.#routers.length !== 1) {\n      throw new Error(\"No active router has been determined yet.\");\n    }\n    return this.#routers[0];\n  }\n};\nexport {\n  SmartRouter\n};\n", "// src/router/trie-router/index.ts\nimport { <PERSON>eRouter } from \"./router.js\";\nexport {\n  TrieRouter\n};\n", "// src/router/trie-router/router.ts\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { Node } from \"./node.js\";\nvar TrieRouter = class {\n  name = \"TrieRouter\";\n  #node;\n  constructor() {\n    this.#node = new Node();\n  }\n  add(method, path, handler) {\n    const results = checkOptionalParameter(path);\n    if (results) {\n      for (let i = 0, len = results.length; i < len; i++) {\n        this.#node.insert(method, results[i], handler);\n      }\n      return;\n    }\n    this.#node.insert(method, path, handler);\n  }\n  match(method, path) {\n    return this.#node.search(method, path);\n  }\n};\nexport {\n  TrieRouter\n};\n", "// src/router/trie-router/node.ts\nimport { METHOD_NAME_ALL } from \"../../router.js\";\nimport { getPattern, splitPath, splitRoutingPath } from \"../../utils/url.js\";\nvar emptyParams = /* @__PURE__ */ Object.create(null);\nvar Node = class {\n  #methods;\n  #children;\n  #patterns;\n  #order = 0;\n  #params = emptyParams;\n  constructor(method, handler, children) {\n    this.#children = children || /* @__PURE__ */ Object.create(null);\n    this.#methods = [];\n    if (method && handler) {\n      const m = /* @__PURE__ */ Object.create(null);\n      m[method] = { handler, possibleKeys: [], score: 0 };\n      this.#methods = [m];\n    }\n    this.#patterns = [];\n  }\n  insert(method, path, handler) {\n    this.#order = ++this.#order;\n    let curNode = this;\n    const parts = splitRoutingPath(path);\n    const possibleKeys = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const p = parts[i];\n      const nextP = parts[i + 1];\n      const pattern = getPattern(p, nextP);\n      const key = Array.isArray(pattern) ? pattern[0] : p;\n      if (key in curNode.#children) {\n        curNode = curNode.#children[key];\n        if (pattern) {\n          possibleKeys.push(pattern[1]);\n        }\n        continue;\n      }\n      curNode.#children[key] = new Node();\n      if (pattern) {\n        curNode.#patterns.push(pattern);\n        possibleKeys.push(pattern[1]);\n      }\n      curNode = curNode.#children[key];\n    }\n    curNode.#methods.push({\n      [method]: {\n        handler,\n        possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),\n        score: this.#order\n      }\n    });\n    return curNode;\n  }\n  #getHandlerSets(node, method, nodeParams, params) {\n    const handlerSets = [];\n    for (let i = 0, len = node.#methods.length; i < len; i++) {\n      const m = node.#methods[i];\n      const handlerSet = m[method] || m[METHOD_NAME_ALL];\n      const processedSet = {};\n      if (handlerSet !== void 0) {\n        handlerSet.params = /* @__PURE__ */ Object.create(null);\n        handlerSets.push(handlerSet);\n        if (nodeParams !== emptyParams || params && params !== emptyParams) {\n          for (let i2 = 0, len2 = handlerSet.possibleKeys.length; i2 < len2; i2++) {\n            const key = handlerSet.possibleKeys[i2];\n            const processed = processedSet[handlerSet.score];\n            handlerSet.params[key] = params?.[key] && !processed ? params[key] : nodeParams[key] ?? params?.[key];\n            processedSet[handlerSet.score] = true;\n          }\n        }\n      }\n    }\n    return handlerSets;\n  }\n  search(method, path) {\n    const handlerSets = [];\n    this.#params = emptyParams;\n    const curNode = this;\n    let curNodes = [curNode];\n    const parts = splitPath(path);\n    const curNodesQueue = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const part = parts[i];\n      const isLast = i === len - 1;\n      const tempNodes = [];\n      for (let j = 0, len2 = curNodes.length; j < len2; j++) {\n        const node = curNodes[j];\n        const nextNode = node.#children[part];\n        if (nextNode) {\n          nextNode.#params = node.#params;\n          if (isLast) {\n            if (nextNode.#children[\"*\"]) {\n              handlerSets.push(\n                ...this.#getHandlerSets(nextNode.#children[\"*\"], method, node.#params)\n              );\n            }\n            handlerSets.push(...this.#getHandlerSets(nextNode, method, node.#params));\n          } else {\n            tempNodes.push(nextNode);\n          }\n        }\n        for (let k = 0, len3 = node.#patterns.length; k < len3; k++) {\n          const pattern = node.#patterns[k];\n          const params = node.#params === emptyParams ? {} : { ...node.#params };\n          if (pattern === \"*\") {\n            const astNode = node.#children[\"*\"];\n            if (astNode) {\n              handlerSets.push(...this.#getHandlerSets(astNode, method, node.#params));\n              astNode.#params = params;\n              tempNodes.push(astNode);\n            }\n            continue;\n          }\n          if (!part) {\n            continue;\n          }\n          const [key, name, matcher] = pattern;\n          const child = node.#children[key];\n          const restPathString = parts.slice(i).join(\"/\");\n          if (matcher instanceof RegExp) {\n            const m = matcher.exec(restPathString);\n            if (m) {\n              params[name] = m[0];\n              handlerSets.push(...this.#getHandlerSets(child, method, node.#params, params));\n              if (Object.keys(child.#children).length) {\n                child.#params = params;\n                const componentCount = m[0].match(/\\//)?.length ?? 0;\n                const targetCurNodes = curNodesQueue[componentCount] ||= [];\n                targetCurNodes.push(child);\n              }\n              continue;\n            }\n          }\n          if (matcher === true || matcher.test(part)) {\n            params[name] = part;\n            if (isLast) {\n              handlerSets.push(...this.#getHandlerSets(child, method, params, node.#params));\n              if (child.#children[\"*\"]) {\n                handlerSets.push(\n                  ...this.#getHandlerSets(child.#children[\"*\"], method, params, node.#params)\n                );\n              }\n            } else {\n              child.#params = params;\n              tempNodes.push(child);\n            }\n          }\n        }\n      }\n      curNodes = tempNodes.concat(curNodesQueue.shift() ?? []);\n    }\n    if (handlerSets.length > 1) {\n      handlerSets.sort((a, b) => {\n        return a.score - b.score;\n      });\n    }\n    return [handlerSets.map(({ handler, params }) => [handler, params])];\n  }\n};\nexport {\n  Node\n};\n", "// src/middleware/cors/index.ts\nvar cors = (options) => {\n  const defaults = {\n    origin: \"*\",\n    allowMethods: [\"GET\", \"HEAD\", \"PUT\", \"POST\", \"DELETE\", \"PATCH\"],\n    allowHeaders: [],\n    exposeHeaders: []\n  };\n  const opts = {\n    ...defaults,\n    ...options\n  };\n  const findAllowOrigin = ((optsOrigin) => {\n    if (typeof optsOrigin === \"string\") {\n      if (optsOrigin === \"*\") {\n        return () => optsOrigin;\n      } else {\n        return (origin) => optsOrigin === origin ? origin : null;\n      }\n    } else if (typeof optsOrigin === \"function\") {\n      return optsOrigin;\n    } else {\n      return (origin) => optsOrigin.includes(origin) ? origin : null;\n    }\n  })(opts.origin);\n  const findAllowMethods = ((optsAllowMethods) => {\n    if (typeof optsAllowMethods === \"function\") {\n      return optsAllowMethods;\n    } else if (Array.isArray(optsAllowMethods)) {\n      return () => optsAllowMethods;\n    } else {\n      return () => [];\n    }\n  })(opts.allowMethods);\n  return async function cors2(c, next) {\n    function set(key, value) {\n      c.res.headers.set(key, value);\n    }\n    const allowOrigin = findAllowOrigin(c.req.header(\"origin\") || \"\", c);\n    if (allowOrigin) {\n      set(\"Access-Control-Allow-Origin\", allowOrigin);\n    }\n    if (opts.origin !== \"*\") {\n      const existingVary = c.req.header(\"Vary\");\n      if (existingVary) {\n        set(\"Vary\", existingVary);\n      } else {\n        set(\"Vary\", \"Origin\");\n      }\n    }\n    if (opts.credentials) {\n      set(\"Access-Control-Allow-Credentials\", \"true\");\n    }\n    if (opts.exposeHeaders?.length) {\n      set(\"Access-Control-Expose-Headers\", opts.exposeHeaders.join(\",\"));\n    }\n    if (c.req.method === \"OPTIONS\") {\n      if (opts.maxAge != null) {\n        set(\"Access-Control-Max-Age\", opts.maxAge.toString());\n      }\n      const allowMethods = findAllowMethods(c.req.header(\"origin\") || \"\", c);\n      if (allowMethods.length) {\n        set(\"Access-Control-Allow-Methods\", allowMethods.join(\",\"));\n      }\n      let headers = opts.allowHeaders;\n      if (!headers?.length) {\n        const requestHeaders = c.req.header(\"Access-Control-Request-Headers\");\n        if (requestHeaders) {\n          headers = requestHeaders.split(/\\s*,\\s*/);\n        }\n      }\n      if (headers?.length) {\n        set(\"Access-Control-Allow-Headers\", headers.join(\",\"));\n        c.res.headers.append(\"Vary\", \"Access-Control-Request-Headers\");\n      }\n      c.res.headers.delete(\"Content-Length\");\n      c.res.headers.delete(\"Content-Type\");\n      return new Response(null, {\n        headers: c.res.headers,\n        status: 204,\n        statusText: \"No Content\"\n      });\n    }\n    await next();\n  };\n};\nexport {\n  cors\n};\n", "import { Hono } from 'hono';\nimport { sign, verify } from 'hono/jwt';\nimport bcrypt from 'bcryptjs';\nimport type { Env } from '../index';\n\n// Validation schemas\nconst loginSchema = {\n  email: (email: string) => email.includes('@'),\n  password: (password: string) => password.length > 0,\n};\n\nconst registerSchema = {\n  name: (name: string) => name.length > 0,\n  email: (email: string) => email.includes('@'),\n  password: (password: string) => password.length >= 8,\n  role: (role: string) => ['creator', 'promoter'].includes(role),\n};\n\nconst auth = new Hono<{ Bindings: Env }>();\n\n// Simple validation functions\nfunction validateLogin(data: any) {\n  if (!data.email || !loginSchema.email(data.email)) {\n    throw new Error('Email tidak valid');\n  }\n  if (!data.password || !loginSchema.password(data.password)) {\n    throw new Error('Password tidak boleh kosong');\n  }\n  return data;\n}\n\nfunction validateRegister(data: any) {\n  if (!data.name || !registerSchema.name(data.name)) {\n    throw new Error('Nama tidak boleh kosong');\n  }\n  if (!data.email || !registerSchema.email(data.email)) {\n    throw new Error('Email tidak valid');\n  }\n  if (!data.password || !registerSchema.password(data.password)) {\n    throw new Error('Password harus minimal 8 karakter');\n  }\n  if (data.role && !registerSchema.role(data.role)) {\n    throw new Error('Role tidak valid');\n  }\n  return { ...data, role: data.role || 'promoter' };\n}\n\n// Register endpoint\nauth.post('/register', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { name, email, password, role } = validateRegister(body);\n\n    // Check if user already exists\n    const existingUser = await c.env.DB.prepare(\n      'SELECT id FROM users WHERE email = ?'\n    ).bind(email).first();\n\n    if (existingUser) {\n      return c.json({ error: 'Email sudah terdaftar' }, 400);\n    }\n\n    // Hash password\n    const passwordHash = await bcrypt.hash(password, 10);\n    const userId = crypto.randomUUID();\n    const now = new Date().toISOString();\n\n    // Create user\n    await c.env.DB.prepare(`\n      INSERT INTO users (id, email, name, password_hash, role, created_at, updated_at, is_active)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n    `).bind(userId, email, name, passwordHash, role, now, now, true).run();\n\n    // Create wallet for user\n    await c.env.DB.prepare(`\n      INSERT INTO wallets (id, user_id, balance, updated_at)\n      VALUES (?, ?, ?, ?)\n    `).bind(crypto.randomUUID(), userId, 0, now).run();\n\n    // Generate JWT token\n    const token = await sign({\n      userId,\n      email,\n      role,\n      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours\n    }, c.env.JWT_SECRET);\n\n    return c.json({\n      success: true,\n      message: 'Akun berhasil dibuat',\n      token,\n      user: { id: userId, email, name, role }\n    });\n  } catch (error: any) {\n    console.error('Registration error:', error);\n    return c.json({ \n      error: error.message || 'Terjadi kesalahan saat mendaftar' \n    }, 500);\n  }\n});\n\n// Login endpoint\nauth.post('/login', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { email, password } = validateLogin(body);\n\n    // Find user\n    const user = await c.env.DB.prepare(\n      'SELECT id, email, name, password_hash, role, is_active FROM users WHERE email = ?'\n    ).bind(email).first() as any;\n\n    if (!user || !user.is_active) {\n      return c.json({ error: 'Email atau password salah' }, 401);\n    }\n\n    // Verify password\n    const isValidPassword = await bcrypt.compare(password, user.password_hash);\n    if (!isValidPassword) {\n      return c.json({ error: 'Email atau password salah' }, 401);\n    }\n\n    // Generate JWT token\n    const token = await sign({\n      userId: user.id,\n      email: user.email,\n      role: user.role,\n      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours\n    }, c.env.JWT_SECRET);\n\n    return c.json({\n      success: true,\n      message: 'Login berhasil',\n      token,\n      user: {\n        id: user.id,\n        email: user.email,\n        name: user.name,\n        role: user.role\n      }\n    });\n  } catch (error: any) {\n    console.error('Login error:', error);\n    return c.json({ \n      error: error.message || 'Terjadi kesalahan saat login' \n    }, 500);\n  }\n});\n\n// Verify token endpoint\nauth.get('/verify', async (c) => {\n  try {\n    const authHeader = c.req.header('Authorization');\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return c.json({ error: 'Token tidak ditemukan' }, 401);\n    }\n\n    const token = authHeader.substring(7);\n    const payload = await verify(token, c.env.JWT_SECRET) as any;\n\n    // Get fresh user data\n    const user = await c.env.DB.prepare(\n      'SELECT id, email, name, role, is_active FROM users WHERE id = ?'\n    ).bind(payload.userId).first() as any;\n\n    if (!user || !user.is_active) {\n      return c.json({ error: 'User tidak ditemukan atau tidak aktif' }, 401);\n    }\n\n    return c.json({\n      success: true,\n      user: {\n        id: user.id,\n        email: user.email,\n        name: user.name,\n        role: user.role\n      }\n    });\n  } catch (error: any) {\n    console.error('Token verification error:', error);\n    return c.json({ error: 'Token tidak valid' }, 401);\n  }\n});\n\nexport { auth as authRoutes };\n", "// src/middleware/jwt/index.ts\nimport { jwt, verify, decode, sign } from \"./jwt.js\";\nexport {\n  decode,\n  jwt,\n  sign,\n  verify\n};\n", "// src/middleware/jwt/jwt.ts\nimport { getCookie, getSignedCookie } from \"../../helper/cookie/index.js\";\nimport { HTTPException } from \"../../http-exception.js\";\nimport { Jwt } from \"../../utils/jwt/index.js\";\nimport \"../../context.js\";\nvar jwt = (options) => {\n  if (!options || !options.secret) {\n    throw new Error('JWT auth middleware requires options for \"secret\"');\n  }\n  if (!crypto.subtle || !crypto.subtle.importKey) {\n    throw new Error(\"`crypto.subtle.importKey` is undefined. JWT auth middleware requires it.\");\n  }\n  return async function jwt2(ctx, next) {\n    const headerName = options.headerName || \"Authorization\";\n    const credentials = ctx.req.raw.headers.get(headerName);\n    let token;\n    if (credentials) {\n      const parts = credentials.split(/\\s+/);\n      if (parts.length !== 2) {\n        const errDescription = \"invalid credentials structure\";\n        throw new HTTPException(401, {\n          message: errDescription,\n          res: unauthorizedResponse({\n            ctx,\n            error: \"invalid_request\",\n            errDescription\n          })\n        });\n      } else {\n        token = parts[1];\n      }\n    } else if (options.cookie) {\n      if (typeof options.cookie == \"string\") {\n        token = getCookie(ctx, options.cookie);\n      } else if (options.cookie.secret) {\n        if (options.cookie.prefixOptions) {\n          token = await getSignedCookie(\n            ctx,\n            options.cookie.secret,\n            options.cookie.key,\n            options.cookie.prefixOptions\n          );\n        } else {\n          token = await getSignedCookie(ctx, options.cookie.secret, options.cookie.key);\n        }\n      } else {\n        if (options.cookie.prefixOptions) {\n          token = getCookie(ctx, options.cookie.key, options.cookie.prefixOptions);\n        } else {\n          token = getCookie(ctx, options.cookie.key);\n        }\n      }\n    }\n    if (!token) {\n      const errDescription = \"no authorization included in request\";\n      throw new HTTPException(401, {\n        message: errDescription,\n        res: unauthorizedResponse({\n          ctx,\n          error: \"invalid_request\",\n          errDescription\n        })\n      });\n    }\n    let payload;\n    let cause;\n    try {\n      payload = await Jwt.verify(token, options.secret, options.alg);\n    } catch (e) {\n      cause = e;\n    }\n    if (!payload) {\n      throw new HTTPException(401, {\n        message: \"Unauthorized\",\n        res: unauthorizedResponse({\n          ctx,\n          error: \"invalid_token\",\n          statusText: \"Unauthorized\",\n          errDescription: \"token verification failure\"\n        }),\n        cause\n      });\n    }\n    ctx.set(\"jwtPayload\", payload);\n    await next();\n  };\n};\nfunction unauthorizedResponse(opts) {\n  return new Response(\"Unauthorized\", {\n    status: 401,\n    statusText: opts.statusText,\n    headers: {\n      \"WWW-Authenticate\": `Bearer realm=\"${opts.ctx.req.url}\",error=\"${opts.error}\",error_description=\"${opts.errDescription}\"`\n    }\n  });\n}\nvar verify = Jwt.verify;\nvar decode = Jwt.decode;\nvar sign = Jwt.sign;\nexport {\n  decode,\n  jwt,\n  sign,\n  verify\n};\n", "// src/helper/cookie/index.ts\nimport { parse, parseSigned, serialize, serializeSigned } from \"../../utils/cookie.js\";\nvar getCookie = (c, key, prefix) => {\n  const cookie = c.req.raw.headers.get(\"Cookie\");\n  if (typeof key === \"string\") {\n    if (!cookie) {\n      return void 0;\n    }\n    let finalKey = key;\n    if (prefix === \"secure\") {\n      finalKey = \"__Secure-\" + key;\n    } else if (prefix === \"host\") {\n      finalKey = \"__Host-\" + key;\n    }\n    const obj2 = parse(cookie, finalKey);\n    return obj2[finalKey];\n  }\n  if (!cookie) {\n    return {};\n  }\n  const obj = parse(cookie);\n  return obj;\n};\nvar getSignedCookie = async (c, secret, key, prefix) => {\n  const cookie = c.req.raw.headers.get(\"Cookie\");\n  if (typeof key === \"string\") {\n    if (!cookie) {\n      return void 0;\n    }\n    let finalKey = key;\n    if (prefix === \"secure\") {\n      finalKey = \"__Secure-\" + key;\n    } else if (prefix === \"host\") {\n      finalKey = \"__Host-\" + key;\n    }\n    const obj2 = await parseSigned(cookie, secret, finalKey);\n    return obj2[finalKey];\n  }\n  if (!cookie) {\n    return {};\n  }\n  const obj = await parseSigned(cookie, secret);\n  return obj;\n};\nvar setCookie = (c, name, value, opt) => {\n  let cookie;\n  if (opt?.prefix === \"secure\") {\n    cookie = serialize(\"__Secure-\" + name, value, { path: \"/\", ...opt, secure: true });\n  } else if (opt?.prefix === \"host\") {\n    cookie = serialize(\"__Host-\" + name, value, {\n      ...opt,\n      path: \"/\",\n      secure: true,\n      domain: void 0\n    });\n  } else {\n    cookie = serialize(name, value, { path: \"/\", ...opt });\n  }\n  c.header(\"Set-Cookie\", cookie, { append: true });\n};\nvar setSignedCookie = async (c, name, value, secret, opt) => {\n  let cookie;\n  if (opt?.prefix === \"secure\") {\n    cookie = await serializeSigned(\"__Secure-\" + name, value, secret, {\n      path: \"/\",\n      ...opt,\n      secure: true\n    });\n  } else if (opt?.prefix === \"host\") {\n    cookie = await serializeSigned(\"__Host-\" + name, value, secret, {\n      ...opt,\n      path: \"/\",\n      secure: true,\n      domain: void 0\n    });\n  } else {\n    cookie = await serializeSigned(name, value, secret, { path: \"/\", ...opt });\n  }\n  c.header(\"set-cookie\", cookie, { append: true });\n};\nvar deleteCookie = (c, name, opt) => {\n  const deletedCookie = getCookie(c, name, opt?.prefix);\n  setCookie(c, name, \"\", { ...opt, maxAge: 0 });\n  return deletedCookie;\n};\nexport {\n  deleteCookie,\n  getCookie,\n  getSignedCookie,\n  setCookie,\n  setSignedCookie\n};\n", "// src/utils/cookie.ts\nimport { decodeURIComponent_ } from \"./url.js\";\nvar algorithm = { name: \"HMAC\", hash: \"SHA-256\" };\nvar getCryptoKey = async (secret) => {\n  const secretBuf = typeof secret === \"string\" ? new TextEncoder().encode(secret) : secret;\n  return await crypto.subtle.importKey(\"raw\", secretBuf, algorithm, false, [\"sign\", \"verify\"]);\n};\nvar makeSignature = async (value, secret) => {\n  const key = await getCryptoKey(secret);\n  const signature = await crypto.subtle.sign(algorithm.name, key, new TextEncoder().encode(value));\n  return btoa(String.fromCharCode(...new Uint8Array(signature)));\n};\nvar verifySignature = async (base64Signature, value, secret) => {\n  try {\n    const signatureBinStr = atob(base64Signature);\n    const signature = new Uint8Array(signatureBinStr.length);\n    for (let i = 0, len = signatureBinStr.length; i < len; i++) {\n      signature[i] = signatureBinStr.charCodeAt(i);\n    }\n    return await crypto.subtle.verify(algorithm, secret, signature, new TextEncoder().encode(value));\n  } catch {\n    return false;\n  }\n};\nvar validCookieNameRegEx = /^[\\w!#$%&'*.^`|~+-]+$/;\nvar validCookieValueRegEx = /^[ !#-:<-[\\]-~]*$/;\nvar parse = (cookie, name) => {\n  if (name && cookie.indexOf(name) === -1) {\n    return {};\n  }\n  const pairs = cookie.trim().split(\";\");\n  const parsedCookie = {};\n  for (let pairStr of pairs) {\n    pairStr = pairStr.trim();\n    const valueStartPos = pairStr.indexOf(\"=\");\n    if (valueStartPos === -1) {\n      continue;\n    }\n    const cookieName = pairStr.substring(0, valueStartPos).trim();\n    if (name && name !== cookieName || !validCookieNameRegEx.test(cookieName)) {\n      continue;\n    }\n    let cookieValue = pairStr.substring(valueStartPos + 1).trim();\n    if (cookieValue.startsWith('\"') && cookieValue.endsWith('\"')) {\n      cookieValue = cookieValue.slice(1, -1);\n    }\n    if (validCookieValueRegEx.test(cookieValue)) {\n      parsedCookie[cookieName] = decodeURIComponent_(cookieValue);\n      if (name) {\n        break;\n      }\n    }\n  }\n  return parsedCookie;\n};\nvar parseSigned = async (cookie, secret, name) => {\n  const parsedCookie = {};\n  const secretKey = await getCryptoKey(secret);\n  for (const [key, value] of Object.entries(parse(cookie, name))) {\n    const signatureStartPos = value.lastIndexOf(\".\");\n    if (signatureStartPos < 1) {\n      continue;\n    }\n    const signedValue = value.substring(0, signatureStartPos);\n    const signature = value.substring(signatureStartPos + 1);\n    if (signature.length !== 44 || !signature.endsWith(\"=\")) {\n      continue;\n    }\n    const isVerified = await verifySignature(signature, signedValue, secretKey);\n    parsedCookie[key] = isVerified ? signedValue : false;\n  }\n  return parsedCookie;\n};\nvar _serialize = (name, value, opt = {}) => {\n  let cookie = `${name}=${value}`;\n  if (name.startsWith(\"__Secure-\") && !opt.secure) {\n    throw new Error(\"__Secure- Cookie must have Secure attributes\");\n  }\n  if (name.startsWith(\"__Host-\")) {\n    if (!opt.secure) {\n      throw new Error(\"__Host- Cookie must have Secure attributes\");\n    }\n    if (opt.path !== \"/\") {\n      throw new Error('__Host- Cookie must have Path attributes with \"/\"');\n    }\n    if (opt.domain) {\n      throw new Error(\"__Host- Cookie must not have Domain attributes\");\n    }\n  }\n  if (opt && typeof opt.maxAge === \"number\" && opt.maxAge >= 0) {\n    if (opt.maxAge > 3456e4) {\n      throw new Error(\n        \"Cookies Max-Age SHOULD NOT be greater than 400 days (34560000 seconds) in duration.\"\n      );\n    }\n    cookie += `; Max-Age=${opt.maxAge | 0}`;\n  }\n  if (opt.domain && opt.prefix !== \"host\") {\n    cookie += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    cookie += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (opt.expires.getTime() - Date.now() > 3456e7) {\n      throw new Error(\n        \"Cookies Expires SHOULD NOT be greater than 400 days (34560000 seconds) in the future.\"\n      );\n    }\n    cookie += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) {\n    cookie += \"; HttpOnly\";\n  }\n  if (opt.secure) {\n    cookie += \"; Secure\";\n  }\n  if (opt.sameSite) {\n    cookie += `; SameSite=${opt.sameSite.charAt(0).toUpperCase() + opt.sameSite.slice(1)}`;\n  }\n  if (opt.priority) {\n    cookie += `; Priority=${opt.priority}`;\n  }\n  if (opt.partitioned) {\n    if (!opt.secure) {\n      throw new Error(\"Partitioned Cookie must have Secure attributes\");\n    }\n    cookie += \"; Partitioned\";\n  }\n  return cookie;\n};\nvar serialize = (name, value, opt) => {\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\nvar serializeSigned = async (name, value, secret, opt = {}) => {\n  const signature = await makeSignature(value, secret);\n  value = `${value}.${signature}`;\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\nexport {\n  parse,\n  parseSigned,\n  serialize,\n  serializeSigned\n};\n", "// src/http-exception.ts\nvar HTTPException = class extends Error {\n  res;\n  status;\n  constructor(status = 500, options) {\n    super(options?.message, { cause: options?.cause });\n    this.res = options?.res;\n    this.status = status;\n  }\n  getResponse() {\n    if (this.res) {\n      const newResponse = new Response(this.res.body, {\n        status: this.status,\n        headers: this.res.headers\n      });\n      return newResponse;\n    }\n    return new Response(this.message, {\n      status: this.status\n    });\n  }\n};\nexport {\n  HTTPException\n};\n", "// src/utils/jwt/index.ts\nimport { decode, sign, verify, verifyFromJwks } from \"./jwt.js\";\nvar Jwt = { sign, verify, decode, verifyFromJwks };\nexport {\n  Jwt\n};\n", "// src/utils/jwt/jwt.ts\nimport { decodeBase64Url, encodeBase64Url } from \"../../utils/encode.js\";\nimport { AlgorithmTypes } from \"./jwa.js\";\nimport { signing, verifying } from \"./jws.js\";\nimport {\n  JwtHeaderInvalid,\n  JwtHeaderRequiresKid,\n  JwtTokenExpired,\n  JwtTokenInvalid,\n  JwtTokenIssuedAt,\n  JwtTokenNotBefore,\n  JwtTokenSignatureMismatched\n} from \"./types.js\";\nimport { utf8Decoder, utf8Encoder } from \"./utf8.js\";\nvar encodeJwtPart = (part) => encodeBase64Url(utf8Encoder.encode(JSON.stringify(part)).buffer).replace(/=/g, \"\");\nvar encodeSignaturePart = (buf) => encodeBase64Url(buf).replace(/=/g, \"\");\nvar decodeJwtPart = (part) => JSON.parse(utf8Decoder.decode(decodeBase64Url(part)));\nfunction isTokenHeader(obj) {\n  if (typeof obj === \"object\" && obj !== null) {\n    const objWithAlg = obj;\n    return \"alg\" in objWithAlg && Object.values(AlgorithmTypes).includes(objWithAlg.alg) && (!(\"typ\" in objWithAlg) || objWithAlg.typ === \"JWT\");\n  }\n  return false;\n}\nvar sign = async (payload, privateKey, alg = \"HS256\") => {\n  const encodedPayload = encodeJwtPart(payload);\n  let encodedHeader;\n  if (typeof privateKey === \"object\" && \"alg\" in privateKey) {\n    alg = privateKey.alg;\n    encodedHeader = encodeJwtPart({ alg, typ: \"JWT\", kid: privateKey.kid });\n  } else {\n    encodedHeader = encodeJwtPart({ alg, typ: \"JWT\" });\n  }\n  const partialToken = `${encodedHeader}.${encodedPayload}`;\n  const signaturePart = await signing(privateKey, alg, utf8Encoder.encode(partialToken));\n  const signature = encodeSignaturePart(signaturePart);\n  return `${partialToken}.${signature}`;\n};\nvar verify = async (token, publicKey, alg = \"HS256\") => {\n  const tokenParts = token.split(\".\");\n  if (tokenParts.length !== 3) {\n    throw new JwtTokenInvalid(token);\n  }\n  const { header, payload } = decode(token);\n  if (!isTokenHeader(header)) {\n    throw new JwtHeaderInvalid(header);\n  }\n  const now = Date.now() / 1e3 | 0;\n  if (payload.nbf && payload.nbf > now) {\n    throw new JwtTokenNotBefore(token);\n  }\n  if (payload.exp && payload.exp <= now) {\n    throw new JwtTokenExpired(token);\n  }\n  if (payload.iat && now < payload.iat) {\n    throw new JwtTokenIssuedAt(now, payload.iat);\n  }\n  const headerPayload = token.substring(0, token.lastIndexOf(\".\"));\n  const verified = await verifying(\n    publicKey,\n    alg,\n    decodeBase64Url(tokenParts[2]),\n    utf8Encoder.encode(headerPayload)\n  );\n  if (!verified) {\n    throw new JwtTokenSignatureMismatched(token);\n  }\n  return payload;\n};\nvar verifyFromJwks = async (token, options, init) => {\n  const header = decodeHeader(token);\n  if (!isTokenHeader(header)) {\n    throw new JwtHeaderInvalid(header);\n  }\n  if (!header.kid) {\n    throw new JwtHeaderRequiresKid(header);\n  }\n  if (options.jwks_uri) {\n    const response = await fetch(options.jwks_uri, init);\n    if (!response.ok) {\n      throw new Error(`failed to fetch JWKS from ${options.jwks_uri}`);\n    }\n    const data = await response.json();\n    if (!data.keys) {\n      throw new Error('invalid JWKS response. \"keys\" field is missing');\n    }\n    if (!Array.isArray(data.keys)) {\n      throw new Error('invalid JWKS response. \"keys\" field is not an array');\n    }\n    if (options.keys) {\n      options.keys.push(...data.keys);\n    } else {\n      options.keys = data.keys;\n    }\n  } else if (!options.keys) {\n    throw new Error('verifyFromJwks requires options for either \"keys\" or \"jwks_uri\" or both');\n  }\n  const matchingKey = options.keys.find((key) => key.kid === header.kid);\n  if (!matchingKey) {\n    throw new JwtTokenInvalid(token);\n  }\n  return await verify(token, matchingKey, matchingKey.alg || header.alg);\n};\nvar decode = (token) => {\n  try {\n    const [h, p] = token.split(\".\");\n    const header = decodeJwtPart(h);\n    const payload = decodeJwtPart(p);\n    return {\n      header,\n      payload\n    };\n  } catch {\n    throw new JwtTokenInvalid(token);\n  }\n};\nvar decodeHeader = (token) => {\n  try {\n    const [h] = token.split(\".\");\n    return decodeJwtPart(h);\n  } catch {\n    throw new JwtTokenInvalid(token);\n  }\n};\nexport {\n  decode,\n  decodeHeader,\n  isTokenHeader,\n  sign,\n  verify,\n  verifyFromJwks\n};\n", "// src/utils/encode.ts\nvar decodeBase64Url = (str) => {\n  return decodeBase64(str.replace(/_|-/g, (m) => ({ _: \"/\", \"-\": \"+\" })[m] ?? m));\n};\nvar encodeBase64Url = (buf) => encodeBase64(buf).replace(/\\/|\\+/g, (m) => ({ \"/\": \"_\", \"+\": \"-\" })[m] ?? m);\nvar encodeBase64 = (buf) => {\n  let binary = \"\";\n  const bytes = new Uint8Array(buf);\n  for (let i = 0, len = bytes.length; i < len; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return btoa(binary);\n};\nvar decodeBase64 = (str) => {\n  const binary = atob(str);\n  const bytes = new Uint8Array(new ArrayBuffer(binary.length));\n  const half = binary.length / 2;\n  for (let i = 0, j = binary.length - 1; i <= half; i++, j--) {\n    bytes[i] = binary.charCodeAt(i);\n    bytes[j] = binary.charCodeAt(j);\n  }\n  return bytes;\n};\nexport {\n  decodeBase64,\n  decodeBase64Url,\n  encodeBase64,\n  encodeBase64Url\n};\n", "// src/utils/jwt/jwa.ts\nvar AlgorithmTypes = /* @__PURE__ */ ((AlgorithmTypes2) => {\n  AlgorithmTypes2[\"HS256\"] = \"HS256\";\n  AlgorithmTypes2[\"HS384\"] = \"HS384\";\n  AlgorithmTypes2[\"HS512\"] = \"HS512\";\n  AlgorithmTypes2[\"RS256\"] = \"RS256\";\n  AlgorithmTypes2[\"RS384\"] = \"RS384\";\n  AlgorithmTypes2[\"RS512\"] = \"RS512\";\n  AlgorithmTypes2[\"PS256\"] = \"PS256\";\n  AlgorithmTypes2[\"PS384\"] = \"PS384\";\n  AlgorithmTypes2[\"PS512\"] = \"PS512\";\n  AlgorithmTypes2[\"ES256\"] = \"ES256\";\n  AlgorithmTypes2[\"ES384\"] = \"ES384\";\n  AlgorithmTypes2[\"ES512\"] = \"ES512\";\n  AlgorithmTypes2[\"EdDSA\"] = \"EdDSA\";\n  return AlgorithmTypes2;\n})(AlgorithmTypes || {});\nexport {\n  AlgorithmTypes\n};\n", "// src/utils/jwt/jws.ts\nimport { getRuntime<PERSON><PERSON> } from \"../../helper/adapter/index.js\";\nimport { decodeBase64 } from \"../encode.js\";\nimport { CryptoKeyUsage, JwtAlgorithmNotImplemented } from \"./types.js\";\nimport { utf8Encoder } from \"./utf8.js\";\nasync function signing(privateKey, alg, data) {\n  const algorithm = getKeyAlgorithm(alg);\n  const cryptoKey = await importPrivateKey(privateKey, algorithm);\n  return await crypto.subtle.sign(algorithm, cryptoKey, data);\n}\nasync function verifying(publicKey, alg, signature, data) {\n  const algorithm = getKeyAlgorithm(alg);\n  const cryptoKey = await importPublicKey(publicKey, algorithm);\n  return await crypto.subtle.verify(algorithm, cryptoKey, signature, data);\n}\nfunction pemToBinary(pem) {\n  return decodeBase64(pem.replace(/-+(BEGIN|END).*/g, \"\").replace(/\\s/g, \"\"));\n}\nasync function importPrivateKey(key, alg) {\n  if (!crypto.subtle || !crypto.subtle.importKey) {\n    throw new Error(\"`crypto.subtle.importKey` is undefined. JWT auth middleware requires it.\");\n  }\n  if (isCryptoKey(key)) {\n    if (key.type !== \"private\" && key.type !== \"secret\") {\n      throw new Error(\n        `unexpected key type: CryptoKey.type is ${key.type}, expected private or secret`\n      );\n    }\n    return key;\n  }\n  const usages = [CryptoKeyUsage.Sign];\n  if (typeof key === \"object\") {\n    return await crypto.subtle.importKey(\"jwk\", key, alg, false, usages);\n  }\n  if (key.includes(\"PRIVATE\")) {\n    return await crypto.subtle.importKey(\"pkcs8\", pemToBinary(key), alg, false, usages);\n  }\n  return await crypto.subtle.importKey(\"raw\", utf8Encoder.encode(key), alg, false, usages);\n}\nasync function importPublicKey(key, alg) {\n  if (!crypto.subtle || !crypto.subtle.importKey) {\n    throw new Error(\"`crypto.subtle.importKey` is undefined. JWT auth middleware requires it.\");\n  }\n  if (isCryptoKey(key)) {\n    if (key.type === \"public\" || key.type === \"secret\") {\n      return key;\n    }\n    key = await exportPublicJwkFrom(key);\n  }\n  if (typeof key === \"string\" && key.includes(\"PRIVATE\")) {\n    const privateKey = await crypto.subtle.importKey(\"pkcs8\", pemToBinary(key), alg, true, [\n      CryptoKeyUsage.Sign\n    ]);\n    key = await exportPublicJwkFrom(privateKey);\n  }\n  const usages = [CryptoKeyUsage.Verify];\n  if (typeof key === \"object\") {\n    return await crypto.subtle.importKey(\"jwk\", key, alg, false, usages);\n  }\n  if (key.includes(\"PUBLIC\")) {\n    return await crypto.subtle.importKey(\"spki\", pemToBinary(key), alg, false, usages);\n  }\n  return await crypto.subtle.importKey(\"raw\", utf8Encoder.encode(key), alg, false, usages);\n}\nasync function exportPublicJwkFrom(privateKey) {\n  if (privateKey.type !== \"private\") {\n    throw new Error(`unexpected key type: ${privateKey.type}`);\n  }\n  if (!privateKey.extractable) {\n    throw new Error(\"unexpected private key is unextractable\");\n  }\n  const jwk = await crypto.subtle.exportKey(\"jwk\", privateKey);\n  const { kty } = jwk;\n  const { alg, e, n } = jwk;\n  const { crv, x, y } = jwk;\n  return { kty, alg, e, n, crv, x, y, key_ops: [CryptoKeyUsage.Verify] };\n}\nfunction getKeyAlgorithm(name) {\n  switch (name) {\n    case \"HS256\":\n      return {\n        name: \"HMAC\",\n        hash: {\n          name: \"SHA-256\"\n        }\n      };\n    case \"HS384\":\n      return {\n        name: \"HMAC\",\n        hash: {\n          name: \"SHA-384\"\n        }\n      };\n    case \"HS512\":\n      return {\n        name: \"HMAC\",\n        hash: {\n          name: \"SHA-512\"\n        }\n      };\n    case \"RS256\":\n      return {\n        name: \"RSASSA-PKCS1-v1_5\",\n        hash: {\n          name: \"SHA-256\"\n        }\n      };\n    case \"RS384\":\n      return {\n        name: \"RSASSA-PKCS1-v1_5\",\n        hash: {\n          name: \"SHA-384\"\n        }\n      };\n    case \"RS512\":\n      return {\n        name: \"RSASSA-PKCS1-v1_5\",\n        hash: {\n          name: \"SHA-512\"\n        }\n      };\n    case \"PS256\":\n      return {\n        name: \"RSA-PSS\",\n        hash: {\n          name: \"SHA-256\"\n        },\n        saltLength: 32\n      };\n    case \"PS384\":\n      return {\n        name: \"RSA-PSS\",\n        hash: {\n          name: \"SHA-384\"\n        },\n        saltLength: 48\n      };\n    case \"PS512\":\n      return {\n        name: \"RSA-PSS\",\n        hash: {\n          name: \"SHA-512\"\n        },\n        saltLength: 64\n      };\n    case \"ES256\":\n      return {\n        name: \"ECDSA\",\n        hash: {\n          name: \"SHA-256\"\n        },\n        namedCurve: \"P-256\"\n      };\n    case \"ES384\":\n      return {\n        name: \"ECDSA\",\n        hash: {\n          name: \"SHA-384\"\n        },\n        namedCurve: \"P-384\"\n      };\n    case \"ES512\":\n      return {\n        name: \"ECDSA\",\n        hash: {\n          name: \"SHA-512\"\n        },\n        namedCurve: \"P-521\"\n      };\n    case \"EdDSA\":\n      return {\n        name: \"Ed25519\",\n        namedCurve: \"Ed25519\"\n      };\n    default:\n      throw new JwtAlgorithmNotImplemented(name);\n  }\n}\nfunction isCryptoKey(key) {\n  const runtime = getRuntimeKey();\n  if (runtime === \"node\" && !!crypto.webcrypto) {\n    return key instanceof crypto.webcrypto.CryptoKey;\n  }\n  return key instanceof CryptoKey;\n}\nexport {\n  signing,\n  verifying\n};\n", "// src/helper/adapter/index.ts\nvar env = (c, runtime) => {\n  const global = globalThis;\n  const globalEnv = global?.process?.env;\n  runtime ??= getRuntimeKey();\n  const runtimeEnvHandlers = {\n    bun: () => globalEnv,\n    node: () => globalEnv,\n    \"edge-light\": () => globalEnv,\n    deno: () => {\n      return Deno.env.toObject();\n    },\n    workerd: () => c.env,\n    fastly: () => ({}),\n    other: () => ({})\n  };\n  return runtimeEnvHandlers[runtime]();\n};\nvar knownUserAgents = {\n  deno: \"Deno\",\n  bun: \"Bun\",\n  workerd: \"Cloudflare-Workers\",\n  node: \"Node.js\"\n};\nvar getRuntimeKey = () => {\n  const global = globalThis;\n  const userAgentSupported = typeof navigator !== \"undefined\" && typeof navigator.userAgent === \"string\";\n  if (userAgentSupported) {\n    for (const [runtimeKey, userAgent] of Object.entries(knownUserAgents)) {\n      if (checkUserAgentEquals(userAgent)) {\n        return runtimeKey;\n      }\n    }\n  }\n  if (typeof global?.EdgeRuntime === \"string\") {\n    return \"edge-light\";\n  }\n  if (global?.fastly !== void 0) {\n    return \"fastly\";\n  }\n  if (global?.process?.release?.name === \"node\") {\n    return \"node\";\n  }\n  return \"other\";\n};\nvar checkUserAgentEquals = (platform) => {\n  const userAgent = navigator.userAgent;\n  return userAgent.startsWith(platform);\n};\nexport {\n  checkUserAgentEquals,\n  env,\n  getRuntimeKey,\n  knownUserAgents\n};\n", "// src/utils/jwt/types.ts\nvar JwtAlgorithmNotImplemented = class extends Error {\n  constructor(alg) {\n    super(`${alg} is not an implemented algorithm`);\n    this.name = \"JwtAlgorithmNotImplemented\";\n  }\n};\nvar JwtTokenInvalid = class extends Error {\n  constructor(token) {\n    super(`invalid JWT token: ${token}`);\n    this.name = \"JwtTokenInvalid\";\n  }\n};\nvar JwtTokenNotBefore = class extends Error {\n  constructor(token) {\n    super(`token (${token}) is being used before it's valid`);\n    this.name = \"JwtTokenNotBefore\";\n  }\n};\nvar JwtTokenExpired = class extends Error {\n  constructor(token) {\n    super(`token (${token}) expired`);\n    this.name = \"JwtTokenExpired\";\n  }\n};\nvar JwtTokenIssuedAt = class extends Error {\n  constructor(currentTimestamp, iat) {\n    super(`Incorrect \"iat\" claim must be a older than \"${currentTimestamp}\" (iat: \"${iat}\")`);\n    this.name = \"JwtTokenIssuedAt\";\n  }\n};\nvar JwtHeaderInvalid = class extends Error {\n  constructor(header) {\n    super(`jwt header is invalid: ${JSON.stringify(header)}`);\n    this.name = \"JwtHeaderInvalid\";\n  }\n};\nvar JwtHeaderRequiresKid = class extends Error {\n  constructor(header) {\n    super(`required \"kid\" in jwt header: ${JSON.stringify(header)}`);\n    this.name = \"JwtHeaderRequiresKid\";\n  }\n};\nvar JwtTokenSignatureMismatched = class extends Error {\n  constructor(token) {\n    super(`token(${token}) signature mismatched`);\n    this.name = \"JwtTokenSignatureMismatched\";\n  }\n};\nvar CryptoKeyUsage = /* @__PURE__ */ ((CryptoKeyUsage2) => {\n  CryptoKeyUsage2[\"Encrypt\"] = \"encrypt\";\n  CryptoKeyUsage2[\"Decrypt\"] = \"decrypt\";\n  CryptoKeyUsage2[\"Sign\"] = \"sign\";\n  CryptoKeyUsage2[\"Verify\"] = \"verify\";\n  CryptoKeyUsage2[\"DeriveKey\"] = \"deriveKey\";\n  CryptoKeyUsage2[\"DeriveBits\"] = \"deriveBits\";\n  CryptoKeyUsage2[\"WrapKey\"] = \"wrapKey\";\n  CryptoKeyUsage2[\"UnwrapKey\"] = \"unwrapKey\";\n  return CryptoKeyUsage2;\n})(CryptoKeyUsage || {});\nexport {\n  CryptoKeyUsage,\n  JwtAlgorithmNotImplemented,\n  JwtHeaderInvalid,\n  JwtHeaderRequiresKid,\n  JwtTokenExpired,\n  JwtTokenInvalid,\n  JwtTokenIssuedAt,\n  JwtTokenNotBefore,\n  JwtTokenSignatureMismatched\n};\n", "// src/utils/jwt/utf8.ts\nvar utf8Encoder = new TextEncoder();\nvar utf8Decoder = new TextDecoder();\nexport {\n  utf8Decoder,\n  utf8Encoder\n};\n", "import { Hono } from 'hono';\nimport { verify } from 'hono/jwt';\nimport type { Env } from '../index';\n\nconst user = new Hono<{ Bindings: Env }>();\n\n// Middleware to verify JWT token\nconst authMiddleware = async (c: any, next: any) => {\n  try {\n    const authHeader = c.req.header('Authorization');\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return c.json({ error: 'Token tidak ditemukan' }, 401);\n    }\n\n    const token = authHeader.substring(7);\n    const payload = await verify(token, c.env.JWT_SECRET) as any;\n    c.set('user', payload);\n    await next();\n  } catch (error) {\n    return c.json({ error: 'Token tidak valid' }, 401);\n  }\n};\n\n// Get user profile\nuser.get('/profile', authMiddleware, async (c) => {\n  try {\n    const userPayload = c.get('user');\n    \n    const userData = await c.env.DB.prepare(`\n      SELECT u.id, u.email, u.name, u.role, u.bio, u.created_at,\n             w.balance\n      FROM users u\n      LEFT JOIN wallets w ON u.id = w.user_id\n      WHERE u.id = ? AND u.is_active = true\n    `).bind(userPayload.userId).first() as any;\n\n    if (!userData) {\n      return c.json({ error: 'User tidak ditemukan' }, 404);\n    }\n\n    return c.json({\n      success: true,\n      user: {\n        id: userData.id,\n        email: userData.email,\n        name: userData.name,\n        role: userData.role,\n        bio: userData.bio,\n        balance: userData.balance || 0,\n        created_at: userData.created_at\n      }\n    });\n  } catch (error: any) {\n    console.error('Get profile error:', error);\n    return c.json({ error: 'Terjadi kesalahan' }, 500);\n  }\n});\n\n// Update user profile\nuser.put('/profile', authMiddleware, async (c) => {\n  try {\n    const userPayload = c.get('user');\n    const body = await c.req.json();\n    const { name, bio } = body;\n\n    if (!name || name.trim().length === 0) {\n      return c.json({ error: 'Nama tidak boleh kosong' }, 400);\n    }\n\n    const now = new Date().toISOString();\n    \n    await c.env.DB.prepare(`\n      UPDATE users \n      SET name = ?, bio = ?, updated_at = ?\n      WHERE id = ?\n    `).bind(name.trim(), bio || null, now, userPayload.userId).run();\n\n    return c.json({\n      success: true,\n      message: 'Profil berhasil diperbarui'\n    });\n  } catch (error: any) {\n    console.error('Update profile error:', error);\n    return c.json({ error: 'Terjadi kesalahan' }, 500);\n  }\n});\n\n// Get user bank account\nuser.get('/bank-account', authMiddleware, async (c) => {\n  try {\n    const userPayload = c.get('user');\n    \n    const bankAccount = await c.env.DB.prepare(`\n      SELECT id, bank_name, account_holder_name, account_number, created_at\n      FROM bank_accounts\n      WHERE user_id = ?\n    `).bind(userPayload.userId).first() as any;\n\n    return c.json({\n      success: true,\n      bankAccount: bankAccount || null\n    });\n  } catch (error: any) {\n    console.error('Get bank account error:', error);\n    return c.json({ error: 'Terjadi kesalahan' }, 500);\n  }\n});\n\n// Update user bank account\nuser.put('/bank-account', authMiddleware, async (c) => {\n  try {\n    const userPayload = c.get('user');\n    const body = await c.req.json();\n    const { bank_name, account_holder_name, account_number } = body;\n\n    if (!bank_name || !account_holder_name || !account_number) {\n      return c.json({ error: 'Semua field harus diisi' }, 400);\n    }\n\n    const now = new Date().toISOString();\n    \n    // Check if bank account exists\n    const existing = await c.env.DB.prepare(\n      'SELECT id FROM bank_accounts WHERE user_id = ?'\n    ).bind(userPayload.userId).first();\n\n    if (existing) {\n      // Update existing\n      await c.env.DB.prepare(`\n        UPDATE bank_accounts \n        SET bank_name = ?, account_holder_name = ?, account_number = ?, updated_at = ?\n        WHERE user_id = ?\n      `).bind(bank_name, account_holder_name, account_number, now, userPayload.userId).run();\n    } else {\n      // Create new\n      await c.env.DB.prepare(`\n        INSERT INTO bank_accounts (id, user_id, bank_name, account_holder_name, account_number, created_at, updated_at)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `).bind(crypto.randomUUID(), userPayload.userId, bank_name, account_holder_name, account_number, now, now).run();\n    }\n\n    return c.json({\n      success: true,\n      message: 'Rekening bank berhasil diperbarui'\n    });\n  } catch (error: any) {\n    console.error('Update bank account error:', error);\n    return c.json({ error: 'Terjadi kesalahan' }, 500);\n  }\n});\n\nexport { user as userRoutes };\n", "import { Hono } from 'hono';\nimport { verify } from 'hono/jwt';\nimport type { Env } from '../index';\n\nconst campaigns = new Hono<{ Bindings: Env }>();\n\n// Middleware to verify JWT token\nconst authMiddleware = async (c: any, next: any) => {\n  try {\n    const authHeader = c.req.header('Authorization');\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return c.json({ error: 'Token tidak ditemukan' }, 401);\n    }\n\n    const token = authHeader.substring(7);\n    const payload = await verify(token, c.env.JWT_SECRET) as any;\n    c.set('user', payload);\n    await next();\n  } catch (error) {\n    return c.json({ error: 'Token tidak valid' }, 401);\n  }\n};\n\n// Get all campaigns (for promoters)\ncampaigns.get('/', authMiddleware, async (c) => {\n  try {\n    const userPayload = c.get('user');\n    \n    let query = `\n      SELECT c.id, c.title, c.description, c.budget, c.price_per_view, \n             c.requirements, c.status, c.created_at, c.expires_at,\n             u.name as creator_name\n      FROM campaigns c\n      JOIN users u ON c.creator_id = u.id\n      WHERE c.status = 'active'\n    `;\n    \n    // If user is creator, show their campaigns\n    if (userPayload.role === 'creator') {\n      query = `\n        SELECT c.id, c.title, c.description, c.budget, c.price_per_view, \n               c.requirements, c.status, c.created_at, c.expires_at,\n               u.name as creator_name,\n               COUNT(p.id) as promotion_count\n        FROM campaigns c\n        JOIN users u ON c.creator_id = u.id\n        LEFT JOIN promotions p ON c.id = p.campaign_id\n        WHERE c.creator_id = ?\n        GROUP BY c.id\n        ORDER BY c.created_at DESC\n      `;\n    }\n\n    const campaigns = userPayload.role === 'creator' \n      ? await c.env.DB.prepare(query).bind(userPayload.userId).all()\n      : await c.env.DB.prepare(query).all();\n\n    return c.json({\n      success: true,\n      campaigns: campaigns.results || []\n    });\n  } catch (error: any) {\n    console.error('Get campaigns error:', error);\n    return c.json({ error: 'Terjadi kesalahan' }, 500);\n  }\n});\n\n// Create new campaign (creators only)\ncampaigns.post('/', authMiddleware, async (c) => {\n  try {\n    const userPayload = c.get('user');\n    \n    if (userPayload.role !== 'creator') {\n      return c.json({ error: 'Hanya creator yang dapat membuat campaign' }, 403);\n    }\n\n    const body = await c.req.json();\n    const { title, description, budget, price_per_view, requirements, material_url } = body;\n\n    if (!title || !description || !budget || !price_per_view) {\n      return c.json({ error: 'Field wajib harus diisi' }, 400);\n    }\n\n    const campaignId = crypto.randomUUID();\n    const now = new Date().toISOString();\n\n    await c.env.DB.prepare(`\n      INSERT INTO campaigns (id, creator_id, title, description, budget, price_per_view, \n                           requirements, material_url, status, created_at, updated_at)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'draft', ?, ?)\n    `).bind(\n      campaignId, userPayload.userId, title, description, \n      budget, price_per_view, requirements || '', material_url || '', \n      now, now\n    ).run();\n\n    return c.json({\n      success: true,\n      message: 'Campaign berhasil dibuat',\n      campaignId\n    });\n  } catch (error: any) {\n    console.error('Create campaign error:', error);\n    return c.json({ error: 'Terjadi kesalahan' }, 500);\n  }\n});\n\n// Get campaign by ID\ncampaigns.get('/:id', authMiddleware, async (c) => {\n  try {\n    const campaignId = c.req.param('id');\n    \n    const campaign = await c.env.DB.prepare(`\n      SELECT c.*, u.name as creator_name\n      FROM campaigns c\n      JOIN users u ON c.creator_id = u.id\n      WHERE c.id = ?\n    `).bind(campaignId).first() as any;\n\n    if (!campaign) {\n      return c.json({ error: 'Campaign tidak ditemukan' }, 404);\n    }\n\n    return c.json({\n      success: true,\n      campaign\n    });\n  } catch (error: any) {\n    console.error('Get campaign error:', error);\n    return c.json({ error: 'Terjadi kesalahan' }, 500);\n  }\n});\n\n// Update campaign status\ncampaigns.put('/:id/status', authMiddleware, async (c) => {\n  try {\n    const userPayload = c.get('user');\n    const campaignId = c.req.param('id');\n    const body = await c.req.json();\n    const { status } = body;\n\n    if (!['draft', 'active', 'paused', 'completed'].includes(status)) {\n      return c.json({ error: 'Status tidak valid' }, 400);\n    }\n\n    // Check if user owns the campaign\n    const campaign = await c.env.DB.prepare(\n      'SELECT creator_id FROM campaigns WHERE id = ?'\n    ).bind(campaignId).first() as any;\n\n    if (!campaign) {\n      return c.json({ error: 'Campaign tidak ditemukan' }, 404);\n    }\n\n    if (campaign.creator_id !== userPayload.userId && userPayload.role !== 'admin') {\n      return c.json({ error: 'Tidak memiliki akses' }, 403);\n    }\n\n    const now = new Date().toISOString();\n    \n    await c.env.DB.prepare(`\n      UPDATE campaigns \n      SET status = ?, updated_at = ?\n      WHERE id = ?\n    `).bind(status, now, campaignId).run();\n\n    return c.json({\n      success: true,\n      message: 'Status campaign berhasil diperbarui'\n    });\n  } catch (error: any) {\n    console.error('Update campaign status error:', error);\n    return c.json({ error: 'Terjadi kesalahan' }, 500);\n  }\n});\n\nexport { campaigns as campaignRoutes };\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBO,SAAS,0BAA0B,MAAM;AAC/C,SAAO,IAAI,MAAM,WAAW,IAAI,0BAA0B;AAC3D;AAAA;AAEO,SAAS,eAAe,MAAM;AACpC,QAAM,KAAK,6BAAM;AAChB,UAAM,0CAA0B,IAAI;AAAA,EACrC,GAFW;AAGX,SAAO,OAAO,OAAO,IAAI,EAAE,WAAW,KAAK,CAAC;AAC7C;AAAA;AASO,SAAS,oBAAoB,MAAM;AACzC,SAAO,MAAM;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AACb,YAAM,IAAI,MAAM,WAAW,IAAI,0BAA0B;AAAA,IAC1D;AAAA,EACD;AACD;AAhDA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAA;AAuBgB;AAIA;AAcA;AAAA;AAAA;;;ACzChB,IACM,aACA,iBACA,YAsBO,kBAwBA,iBASA,oBAGA,2BAwBA,8BAYA,aAsFA,qBAgCA;AAvNb;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAA;AACA,IAAM,cAAc,WAAW,aAAa,cAAc,KAAK,IAAI;AACnE,IAAM,kBAAkB,WAAW,aAAa,MAAM,WAAW,YAAY,IAAI,KAAK,WAAW,WAAW,IAAI,MAAM,KAAK,IAAI,IAAI;AACnI,IAAM,aAAa;AAAA,MAClB,MAAM;AAAA,MACN,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,MACX,SAAS;AAAA,MACT,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe;AAAA,QACd,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,eAAe;AAAA,MAChB;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AACR,eAAO;AAAA,MACR;AAAA,IACD;AACO,IAAM,mBAAN,MAAuB;AAAA,MAzB9B,OAyB8B;AAAA;AAAA;AAAA,MAC7B,YAAY;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,YAAY,MAAM,SAAS;AAC1B,aAAK,OAAO;AACZ,aAAK,YAAY,SAAS,aAAa,gBAAgB;AACvD,aAAK,SAAS,SAAS;AAAA,MACxB;AAAA,MACA,IAAI,WAAW;AACd,eAAO,gBAAgB,IAAI,KAAK;AAAA,MACjC;AAAA,MACA,SAAS;AACR,eAAO;AAAA,UACN,MAAM,KAAK;AAAA,UACX,WAAW,KAAK;AAAA,UAChB,WAAW,KAAK;AAAA,UAChB,UAAU,KAAK;AAAA,UACf,QAAQ,KAAK;AAAA,QACd;AAAA,MACD;AAAA,IACD;AACO,IAAM,kBAAkB,MAAMC,yBAAwB,iBAAiB;AAAA,MAjD9E,OAiD8E;AAAA;AAAA;AAAA,MAC7E,YAAY;AAAA,MACZ,cAAc;AACb,cAAM,GAAG,SAAS;AAAA,MACnB;AAAA,MACA,IAAI,WAAW;AACd,eAAO;AAAA,MACR;AAAA,IACD;AACO,IAAM,qBAAN,cAAiC,iBAAiB;AAAA,MA1DzD,OA0DyD;AAAA;AAAA;AAAA,MACxD,YAAY;AAAA,IACb;AACO,IAAM,4BAAN,cAAwC,iBAAiB;AAAA,MA7DhE,OA6DgE;AAAA;AAAA;AAAA,MAC/D,YAAY;AAAA,MACZ,eAAe,CAAC;AAAA,MAChB,aAAa;AAAA,MACb,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,cAAc;AAAA,MACd,iBAAiB;AAAA,IAClB;AACO,IAAM,+BAAN,MAAmC;AAAA,MArF1C,OAqF0C;AAAA;AAAA;AAAA,MACzC,YAAY;AAAA,MACZ,aAAa;AACZ,eAAO,CAAC;AAAA,MACT;AAAA,MACA,iBAAiB,OAAO,OAAO;AAC9B,eAAO,CAAC;AAAA,MACT;AAAA,MACA,iBAAiB,MAAM;AACtB,eAAO,CAAC;AAAA,MACT;AAAA,IACD;AACO,IAAM,cAAN,MAAkB;AAAA,MAjGzB,OAiGyB;AAAA;AAAA;AAAA,MACxB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,cAAc,oBAAI,IAAI;AAAA,MACtB,WAAW,CAAC;AAAA,MACZ,4BAA4B;AAAA,MAC5B,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS,KAAK,UAAU;AACvB,cAAM,0BAA0B,sBAAsB;AAAA,MACvD;AAAA,MACA,IAAI,aAAa;AAChB,eAAO;AAAA,MACR;AAAA,MACA,uBAAuB;AACtB,eAAO,CAAC;AAAA,MACT;AAAA,MACA,qBAAqB;AACpB,eAAO,IAAI,0BAA0B,EAAE;AAAA,MACxC;AAAA,MACA,6BAA6B;AAAA,MAC7B,MAAM;AACL,YAAI,KAAK,eAAe,aAAa;AACpC,iBAAO,gBAAgB;AAAA,QACxB;AACA,eAAO,KAAK,IAAI,IAAI,KAAK;AAAA,MAC1B;AAAA,MACA,WAAW,UAAU;AACpB,aAAK,WAAW,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,QAAQ,IAAI,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,MAAM;AAAA,MACjI;AAAA,MACA,cAAc,aAAa;AAC1B,aAAK,WAAW,cAAc,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,WAAW,IAAI,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,SAAS;AAAA,MAC1I;AAAA,MACA,uBAAuB;AACtB,aAAK,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,cAAc,EAAE,cAAc,YAAY;AAAA,MACvG;AAAA,MACA,aAAa;AACZ,eAAO,KAAK;AAAA,MACb;AAAA,MACA,iBAAiB,MAAM,MAAM;AAC5B,eAAO,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,SAAS,CAAC,QAAQ,EAAE,cAAc,KAAK;AAAA,MACtF;AAAA,MACA,iBAAiB,MAAM;AACtB,eAAO,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,IAAI;AAAA,MACxD;AAAA,MACA,KAAK,MAAM,SAAS;AACnB,cAAM,QAAQ,IAAI,gBAAgB,MAAM,OAAO;AAC/C,aAAK,SAAS,KAAK,KAAK;AACxB,eAAO;AAAA,MACR;AAAA,MACA,QAAQ,aAAa,uBAAuB,SAAS;AACpD,YAAI;AACJ,YAAI;AACJ,YAAI,OAAO,0BAA0B,UAAU;AAC9C,kBAAQ,KAAK,iBAAiB,uBAAuB,MAAM,EAAE,CAAC,GAAG;AACjE,gBAAM,KAAK,iBAAiB,SAAS,MAAM,EAAE,CAAC,GAAG;AAAA,QAClD,OAAO;AACN,kBAAQ,OAAO,WAAW,uBAAuB,KAAK,KAAK,KAAK,IAAI;AACpE,gBAAM,OAAO,WAAW,uBAAuB,GAAG,KAAK,KAAK,IAAI;AAAA,QACjE;AACA,cAAM,QAAQ,IAAI,mBAAmB,aAAa;AAAA,UACjD,WAAW;AAAA,UACX,QAAQ;AAAA,YACP;AAAA,YACA;AAAA,UACD;AAAA,QACD,CAAC;AACD,aAAK,SAAS,KAAK,KAAK;AACxB,eAAO;AAAA,MACR;AAAA,MACA,4BAA4B,SAAS;AACpC,aAAK,4BAA4B;AAAA,MAClC;AAAA,MACA,iBAAiB,MAAM,UAAU,SAAS;AACzC,cAAM,0BAA0B,8BAA8B;AAAA,MAC/D;AAAA,MACA,oBAAoB,MAAM,UAAU,SAAS;AAC5C,cAAM,0BAA0B,iCAAiC;AAAA,MAClE;AAAA,MACA,cAAc,OAAO;AACpB,cAAM,0BAA0B,2BAA2B;AAAA,MAC5D;AAAA,MACA,SAAS;AACR,eAAO;AAAA,MACR;AAAA,IACD;AACO,IAAM,sBAAN,MAA0B;AAAA,MAvLjC,OAuLiC;AAAA;AAAA;AAAA,MAChC,YAAY;AAAA,MACZ,OAAO,sBAAsB,CAAC;AAAA,MAC9B,YAAY;AAAA,MACZ,YAAY,UAAU;AACrB,aAAK,YAAY;AAAA,MAClB;AAAA,MACA,cAAc;AACb,eAAO,CAAC;AAAA,MACT;AAAA,MACA,aAAa;AACZ,cAAM,0BAA0B,gCAAgC;AAAA,MACjE;AAAA,MACA,QAAQ,SAAS;AAChB,cAAM,0BAA0B,6BAA6B;AAAA,MAC9D;AAAA,MACA,KAAK,IAAI;AACR,eAAO;AAAA,MACR;AAAA,MACA,gBAAgB,IAAI,YAAY,MAAM;AACrC,eAAO,GAAG,KAAK,SAAS,GAAG,IAAI;AAAA,MAChC;AAAA,MACA,UAAU;AACT,eAAO;AAAA,MACR;AAAA,MACA,iBAAiB;AAChB,eAAO;AAAA,MACR;AAAA,MACA,cAAc;AACb,eAAO;AAAA,MACR;AAAA,IACD;AACO,IAAM,cAAc,WAAW,eAAe,sBAAsB,WAAW,cAAc,WAAW,cAAc,IAAI,YAAY;AAAA;AAAA;;;ACvN7I;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAEA;AAAA;AAAA;;;ACFA,IAAAC,oBAAA;AAAA;AAAA;AAUA,eAAW,cAAc;AACzB,eAAW,cAAc;AACzB,eAAW,mBAAmB;AAC9B,eAAW,kBAAkB;AAC7B,eAAW,qBAAqB;AAChC,eAAW,sBAAsB;AACjC,eAAW,+BAA+B;AAC1C,eAAW,4BAA4B;AAAA;AAAA;;;ACjBvC,IAAO;AAAP;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAA,IAAO,eAAQ,OAAO,OAAO,MAAM;AAAA,IAAC,GAAG,EAAE,WAAW,KAAK,CAAC;AAAA;AAAA;;;ACA1D,SAAS,gBAAgB;AAAzB,IAGM,UACO,eACA,SACA,SACA,KACA,MACA,OACA,OACA,OACA,OACA,MACA,YAEA,OACA,OACA,YACA,KACA,QACA,OACA,UACA,gBACA,SACA,YACA,MACA,SACA,SACA,WACA,SACA,QAIA,qBACA;AApCb;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AACA;AACA;AACA,IAAM,WAAW,WAAW;AACrB,IAAM,gBAAgB;AACtB,IAAM,UAAU,IAAI,SAAS;AAC7B,IAAM,UAAU,IAAI,SAAS;AAC7B,IAAM,MAAM,UAAU,OAAO;AAC7B,IAAM,OAAO,UAAU,QAAQ;AAC/B,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,OAAO,UAAU,QAAQ;AAC/B,IAAM,aAAa,UAAU,cAA8B,+BAAe,oBAAoB;AAE9F,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,aAAa,UAAU,cAAc;AAC3C,IAAM,MAAM,UAAU,OAAO;AAC7B,IAAM,SAAS,UAAU,UAAU;AACnC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,WAAW,UAAU,YAAY;AACvC,IAAM,iBAAiB,UAAU,kBAAkB;AACnD,IAAM,UAAU,UAAU,WAAW;AACrC,IAAM,aAAa,UAAU,cAAc;AAC3C,IAAM,OAAO,UAAU,QAAQ;AAC/B,IAAM,UAAU,UAAU,WAAW;AACrC,IAAM,UAAU,UAAU,WAAW;AACrC,IAAM,YAAY,UAAU,aAAa;AACzC,IAAM,UAAU,UAAU,WAA2B,oCAAoB,iBAAiB;AAC1F,IAAM,SAAyB,oBAAI,IAAI;AAIvC,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AAAA;AAAA;;;ACpCnC,IAkBM,gBAEJ,QACAC,QAEA,SACAC,QACAC,aAEAC,aACAC,QACAC,MACAC,SACAC,QACAC,QACAC,iBACAC,WACAC,OACAC,MACAC,UACAC,aACAC,QACAC,OACAC,UACAC,UACAC,YACAC,QACAC,OAWK;AAxDP,IAAAC,gBAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAA;AAkBA,IAAM,iBAAiB,WAAW,SAAS;AACpC,KAAM;AAAA,MACX;AAAA,MACA,OAAAvB;AAAA,MAEA;AAAA;AAAA;AAAA;AAAA,MACA,OAAAC;AAAA,MACA,YAAAC;AAAA,MAEA;AAAA;AAAA,QAAAC;AAAA;AAAA,MACA,OAAAC;AAAA,MACA,KAAAC;AAAA,MACA,QAAAC;AAAA,MACA,OAAAC;AAAA,MACA,OAAAC;AAAA,MACA,gBAAAC;AAAA,MACA,UAAAC;AAAA,MACA,MAAAC;AAAA,MACA,KAAAC;AAAA,MACA,SAAAC;AAAA,MACA,YAAAC;AAAA,MACA,OAAAC;AAAA,MACA,MAAAC;AAAA,MACA,SAAAC;AAAA,MACA,SAAAC;AAAA,MACA,WAAAC;AAAA,MACA,OAAAC;AAAA,MACA,MAAAC;AAAA,QACE;AACJ,WAAO,OAAO,gBAAgB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,IAAO,kBAAQ;AAAA;AAAA;;;ACxDf;AAAA;AAAA,IAAAG;AACA,eAAW,UAAU;AAAA;AAAA;;;ACDrB,IAAa;AAAb;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAO,IAAM,SAAyB,uBAAO,OAAO,gCAASC,QAAO,WAAW;AAC9E,YAAM,MAAM,KAAK,IAAI;AACrB,YAAM,UAAU,KAAK,MAAM,MAAM,GAAG;AACpC,YAAM,QAAQ,MAAM,MAAM;AAC1B,UAAI,WAAW;AACd,YAAI,cAAc,UAAU,UAAU,CAAC;AACvC,YAAI,YAAY,QAAQ,UAAU,CAAC;AACnC,YAAI,YAAY,GAAG;AAClB,wBAAc,cAAc;AAC5B,sBAAY,MAAM;AAAA,QACnB;AACA,eAAO,CAAC,aAAa,SAAS;AAAA,MAC/B;AACA,aAAO,CAAC,SAAS,KAAK;AAAA,IACvB,GAdoD,WAcjD,EAAE,QAAQ,gCAAS,SAAS;AAC9B,aAAO,OAAO,KAAK,IAAI,IAAI,GAAG;AAAA,IAC/B,GAFa,UAEX,CAAC;AAAA;AAAA;;;AChBH,IAAa;AAAb;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAO,IAAM,cAAN,MAAkB;AAAA,MAAzB,OAAyB;AAAA;AAAA;AAAA,MACxB;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,YAAY,IAAI;AACf,aAAK,KAAK;AAAA,MACX;AAAA,MACA,UAAUC,MAAK,UAAU;AACxB,oBAAY,SAAS;AACrB,eAAO;AAAA,MACR;AAAA,MACA,gBAAgB,UAAU;AACzB,oBAAY,SAAS;AACrB,eAAO;AAAA,MACR;AAAA,MACA,SAAS,GAAG,GAAG,UAAU;AACxB,oBAAY,OAAO,aAAa,cAAc,SAAS;AACvD,eAAO;AAAA,MACR;AAAA,MACA,WAAW,IAAI,IAAI,UAAU;AAC5B,oBAAY,SAAS;AACrB,eAAO;AAAA,MACR;AAAA,MACA,cAAcC,MAAK;AAClB,eAAO;AAAA,MACR;AAAA,MACA,UAAUC,QAAOD,MAAK;AACrB,eAAO;AAAA,MACR;AAAA,MACA,gBAAgB;AACf,eAAO,CAAC,KAAK,SAAS,KAAK,IAAI;AAAA,MAChC;AAAA,MACA,MAAM,KAAK,UAAU,IAAI;AACxB,YAAI,eAAe,YAAY;AAC9B,gBAAM,IAAI,YAAY,EAAE,OAAO,GAAG;AAAA,QACnC;AACA,YAAI;AACH,kBAAQ,IAAI,GAAG;AAAA,QAChB,QAAQ;AAAA,QAAC;AACT,cAAM,OAAO,OAAO,cAAc,GAAG;AACrC,eAAO;AAAA,MACR;AAAA,IACD;AAAA;AAAA;;;AC3CA,IAAa;AAAb;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAE;AAAO,IAAM,aAAN,MAAiB;AAAA,MAAxB,OAAwB;AAAA;AAAA;AAAA,MACvB;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,YAAY,IAAI;AACf,aAAK,KAAK;AAAA,MACX;AAAA,MACA,WAAW,MAAM;AAChB,aAAK,QAAQ;AACb,eAAO;AAAA,MACR;AAAA,IACD;AAAA;AAAA;;;ACXA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AACA;AACA;AAAA;AAAA;;;ACFA,IAAa;AAAb;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAO,IAAM,eAAe;AAAA;AAAA;;;ACA5B,SAAS,oBAAoB;AAA7B,IAIa;AAJb;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AACA;AACA;AACA;AACO,IAAM,UAAN,MAAM,iBAAgB,aAAa;AAAA,MAJ1C,OAI0C;AAAA;AAAA;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AACjB,cAAM;AACN,aAAK,MAAM,KAAK;AAChB,aAAK,SAAS,KAAK;AACnB,aAAK,WAAW,KAAK;AACrB,mBAAW,QAAQ,CAAC,GAAG,OAAO,oBAAoB,SAAQ,SAAS,GAAG,GAAG,OAAO,oBAAoB,aAAa,SAAS,CAAC,GAAG;AAC7H,gBAAM,QAAQ,KAAK,IAAI;AACvB,cAAI,OAAO,UAAU,YAAY;AAChC,iBAAK,IAAI,IAAI,MAAM,KAAK,IAAI;AAAA,UAC7B;AAAA,QACD;AAAA,MACD;AAAA,MACA,YAAY,SAAS,MAAM,MAAM;AAChC,gBAAQ,KAAK,GAAG,OAAO,IAAI,IAAI,OAAO,EAAE,GAAG,OAAO,GAAG,IAAI,OAAO,EAAE,GAAG,OAAO,EAAE;AAAA,MAC/E;AAAA,MACA,QAAQ,MAAM;AACb,eAAO,MAAM,KAAK,GAAG,IAAI;AAAA,MAC1B;AAAA,MACA,UAAU,WAAW;AACpB,eAAO,MAAM,UAAU,SAAS;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI,QAAQ;AACX,eAAO,KAAK,WAAW,IAAI,WAAW,CAAC;AAAA,MACxC;AAAA,MACA,IAAI,SAAS;AACZ,eAAO,KAAK,YAAY,IAAI,YAAY,CAAC;AAAA,MAC1C;AAAA,MACA,IAAI,SAAS;AACZ,eAAO,KAAK,YAAY,IAAI,YAAY,CAAC;AAAA,MAC1C;AAAA,MACA,OAAO;AAAA,MACP,MAAMC,MAAK;AACV,aAAK,OAAOA;AAAA,MACb;AAAA,MACA,MAAM;AACL,eAAO,KAAK;AAAA,MACb;AAAA,MACA,OAAO;AAAA,MACP,WAAW;AAAA,MACX,OAAO,CAAC;AAAA,MACR,QAAQ;AAAA,MACR,WAAW,CAAC;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,IAAI,UAAU;AACb,eAAO,IAAI,YAAY;AAAA,MACxB;AAAA,MACA,IAAI,WAAW;AACd,eAAO,EAAE,MAAM,aAAa;AAAA,MAC7B;AAAA,MACA,IAAI,8BAA8B;AACjC,eAAO,oBAAI,IAAI;AAAA,MAChB;AAAA,MACA,IAAI,oBAAoB;AACvB,eAAO;AAAA,MACR;AAAA,MACA,IAAI,YAAY;AACf,eAAO;AAAA,MACR;AAAA,MACA,IAAI,mBAAmB;AACtB,eAAO;AAAA,MACR;AAAA,MACA,IAAI,mBAAmB;AACtB,eAAO;AAAA,MACR;AAAA,MACA,IAAI,WAAW;AACd,eAAO,CAAC;AAAA,MACT;AAAA,MACA,IAAI,UAAU;AACb,eAAO,CAAC;AAAA,MACT;AAAA,MACA,IAAI,YAAY;AACf,eAAO;AAAA,MACR;AAAA,MACA,IAAI,SAAS;AACZ,eAAO,CAAC;AAAA,MACT;AAAA,MACA,IAAI,iBAAiB;AACpB,eAAO,CAAC;AAAA,MACT;AAAA,MACA,oBAAoB;AACnB,eAAO;AAAA,MACR;AAAA,MACA,kBAAkB;AACjB,eAAO;AAAA,MACR;AAAA,MACA,SAAS;AACR,eAAO;AAAA,MACR;AAAA,MACA,gBAAgB;AACf,eAAO,CAAC;AAAA,MACT;AAAA,MACA,MAAM;AAAA,MAAC;AAAA,MACP,QAAQ;AAAA,MAAC;AAAA,MACT,QAAQ;AACP,cAAM,0BAA0B,eAAe;AAAA,MAChD;AAAA,MACA,mBAAmB;AAClB,eAAO;AAAA,MACR;AAAA,MACA,yBAAyB;AACxB,cAAM,0BAA0B,gCAAgC;AAAA,MACjE;AAAA,MACA,OAAO;AACN,cAAM,0BAA0B,cAAc;AAAA,MAC/C;AAAA,MACA,aAAa;AACZ,cAAM,0BAA0B,oBAAoB;AAAA,MACrD;AAAA,MACA,OAAO;AACN,cAAM,0BAA0B,cAAc;AAAA,MAC/C;AAAA,MACA,QAAQ;AACP,cAAM,0BAA0B,eAAe;AAAA,MAChD;AAAA,MACA,SAAS;AACR,cAAM,0BAA0B,gBAAgB;AAAA,MACjD;AAAA,MACA,uBAAuB;AACtB,cAAM,0BAA0B,8BAA8B;AAAA,MAC/D;AAAA,MACA,cAAc;AACb,cAAM,0BAA0B,qBAAqB;AAAA,MACtD;AAAA,MACA,aAAa;AACZ,cAAM,0BAA0B,oBAAoB;AAAA,MACrD;AAAA,MACA,WAAW;AACV,cAAM,0BAA0B,kBAAkB;AAAA,MACnD;AAAA,MACA,sCAAsC;AACrC,cAAM,0BAA0B,6CAA6C;AAAA,MAC9E;AAAA,MACA,sCAAsC;AACrC,cAAM,0BAA0B,6CAA6C;AAAA,MAC9E;AAAA,MACA,aAAa;AACZ,cAAM,0BAA0B,oBAAoB;AAAA,MACrD;AAAA,MACA,YAAY;AACX,cAAM,0BAA0B,mBAAmB;AAAA,MACpD;AAAA,MACA,SAAS;AACR,cAAM,0BAA0B,gBAAgB;AAAA,MACjD;AAAA,MACA,UAAU;AACT,cAAM,0BAA0B,iBAAiB;AAAA,MAClD;AAAA,MACA,aAAa,EAAE,KAAqB,+BAAe,wBAAwB,EAAE;AAAA,MAC7E,SAAS;AAAA,QACR,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,2BAA2B;AAAA,QAC3B,WAA2B,+BAAe,0BAA0B;AAAA,QACpE,aAA6B,+BAAe,4BAA4B;AAAA,MACzE;AAAA,MACA,eAAe;AAAA,QACd,UAA0B,+BAAe,+BAA+B;AAAA,QACxE,YAA4B,+BAAe,iCAAiC;AAAA,QAC5E,oBAAoC,+BAAe,yCAAyC;AAAA,MAC7F;AAAA,MACA,cAAc,OAAO,OAAO,OAAO;AAAA,QAClC,cAAc;AAAA,QACd,KAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,MACX,IAAI,EAAE,KAAK,6BAAM,GAAN,OAAQ,CAAC;AAAA,MACpB,aAAa;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,eAAe;AAAA,MACf,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,MACrB,QAAQ;AAAA,MACR,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,6BAA6B;AAAA,MAC7B,4BAA4B;AAAA,MAC5B,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,iBAAiB;AAAA,IAClB;AAAA;AAAA;;;AC7NA,IAEM,eACO,kBACE,MAAM,UAAU,UAGzB,cAMJ,OACA,aACA,6BACA,qCACA,qCACA,aACA,mBACA,MACA,MACA,OACA,OACA,QACA,WACA,mBACA,iBACA,UACA,KACA,WACA,QACA,YACA,MACA,aACA,KACA,YACA,UACA,UACA,cACA,UACA,wBACA,iBACAC,SACA,MACA,WACA,eACA,aACA,IACA,KACA,MACA,KACA,MACA,iBACA,qBACA,cACA,SACA,oBACA,gBACA,QACA,eACA,iBACA,sBACA,QACA,OACA,QACA,OACA,kBACA,kBACA,OACA,QACA,SACA,UACA,QACA,YACA,gBACA,YACA,WACAC,SACA,SACA,MACA,UACA,SACA,SACA,SACA,QACA,WACA,QACA,SACA,SACA,QACA,WACA,QACA,YACA,YACA,SACA,cACA,UACA,eACA,WACA,eACA,iBACA,mBACA,oBACA,OACA,kBACA,WACA,4BACA,2BACA,eACA,aACA,cACA,iBACA,UACA,OACA,gBAEI,UA8GC;AAnOP,IAAAC,gBAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAA;AACA;AACA,IAAM,gBAAgB,WAAW,SAAS;AACnC,IAAM,mBAAmB,cAAc;AACvC,KAAM,EAAE,MAAM,UAAU,aAAa;AAAA,MAC1C;AAAA,IACF;AACA,IAAM,eAAe,IAAI,QAAa;AAAA,MACpC,KAAK,cAAc;AAAA,MACnB;AAAA,MACA;AAAA,IACF,CAAC;AACM,KAAM;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAAH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE;AACJ,IAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAAD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAO,kBAAQ;AAAA;AAAA;;;ACnOf;AAAA;AAAA,IAAAG;AACA,eAAW,UAAU;AAAA;AAAA;;;ACDrB;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA;AAAA;AAGA;AAAA;AAAA;;;ACHA,IAAa;AAAb;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAO,IAAM,SAAS,WAAW,QAAQ;AAAA;AAAA;;;ACAzC,IAGa,WA6BA,cAEA,gBACA,mBA+CA,QAKA;AAvFb;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAA;AAGO,IAAM,YAAY,IAAI,MAAM,WAAW,QAAQ,EAAE,IAAI,GAAG,KAAK;AACnE,UAAI,QAAQ,aAAa;AACxB,eAAO,WAAW;AAAA,MACnB;AACA,UAAI,OAAO,WAAW,OAAO,GAAG,MAAM,YAAY;AACjD,eAAO,WAAW,OAAO,GAAG,EAAE,KAAK,WAAW,MAAM;AAAA,MACrD;AACA,aAAO,WAAW,OAAO,GAAG;AAAA,IAC7B,EAAE,CAAC;AAqBI,IAAM,eAA+B,+BAAe,qBAAqB;AAEzE,IAAM,iBAAiC,+BAAe,uBAAuB;AAC7E,IAAM,oBAAoC,+BAAe,0BAA0B;AA+CnF,IAAM,SAAyB,oCAAoB,eAAe;AAKlE,IAAM,WAA2B,oCAAoB,iBAAiB;AAAA;AAAA;;;ACvF7E;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAGA;AACA;AAAA;AAAA;;;ACJA,IASM,eAEJ,aACA,YACA,gBACA,WAEA,UACA,gBACA,kBACA,qBACA,0BACA,YACA,YACA,YACA,kBACA,iBACA,iBACA,YACA,cAEA,YACA,eACA,eACA,oBACA,MACA,MACA,aACA,iBACA,qBACA,iBACA,eACA,mBACA,eACA,YACA,WACA,kBACA,SACA,WACA,iBACA,MACA,MACA,MACA,UACA,MACA,WACA,QACA,YACA,gBACA,gBACA,eACA,eACA,aACA,YACA,gBACA,WACA,YACA,QACA,YACA,gBACA,WACA,SACAC,OACA,MACAC,SACA,iBACAC,SACA,QACA,iBAEWC,YAON;AAtFP,IAAAC,eAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAAA;AASA,IAAM,gBAAgB,QAAQ,iBAAiB,aAAa;AACrD,KAAM;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA;AAAA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA;AAAA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAAL;AAAA,MACA;AAAA,MACA,QAAAC;AAAA,MACA;AAAA,MACA,QAAAC;AAAA,MACA;AAAA,MACA;AAAA,QACE;AACG,IAAMC,aAAY;AAAA;AAAA,MAEvB,WAAW,UAAqB;AAAA,MAChC;AAAA,MACA;AAAA,MACA,QAAAF;AAAA,IACF;AACA,IAAO,iBAAQ;AAAA;AAAA;AAAA;AAAA,MAIb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAAD;AAAA,MACA,QAAAE;AAAA;AAAA;AAAA,MAGA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA;AAAA;AAAA,MAIA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAAD;AAAA,MACA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA,WAAAE;AAAA,IACF;AAAA;AAAA;;;AC1KA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAG;AAAA,IAAAC;AACA,WAAO,UAAU;AAAA;AAAA;;;ACDjB;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC;AAiCA,KAAC,SAAS,QAAQ,SAAS;AAEb,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AACtD,eAAO,CAAC,GAAG,OAAO;AAAA,eACE,OAAO,cAAY,cAAc,OAAO,WAAW,YAAY,UAAU,OAAO,SAAS;AAC7G,eAAO,SAAS,IAAI,QAAQ;AAAA;AAE5B,SAAC,OAAO,SAAS,IAAI,OAAO,SAAS,KAAK,CAAC,GAAG,QAAQ,IAAI,QAAQ;AAAA,IAE1E,GAAE,SAAM,WAAW;AACf;AAMA,UAAIC,UAAS,CAAC;AAOd,UAAI,iBAAiB;AAUrB,eAAS,OAAO,KAAK;AACN,YAAI,OAAO,WAAW,eAAe,UAAU,OAAO,SAAS;AACtE,cAAI;AACA,mBAAO,iBAAkB,aAAa,EAAE,GAAG;AAAA,UAC/C,SAAS,GAAG;AAAA,UAAC;AACP,YAAI;AACV,cAAI;AAAG,WAAC,KAAK,QAAQ,KAAG,KAAK,UAAU,GAAG,iBAAiB,EAAE,IAAI,IAAI,YAAY,GAAG,CAAC;AACrF,iBAAO,MAAM,UAAU,MAAM,KAAK,CAAC;AAAA,QACvC,SAAS,GAAG;AAAA,QAAC;AACE,YAAI,CAAC;AAChB,gBAAM,MAAM,2GAA2G;AAC3H,eAAO,eAAe,GAAG;AAAA,MAC7B;AAZS;AAeT,UAAI,kBAAkB;AACtB,UAAI;AACA,eAAO,CAAC;AACR,0BAAkB;AAAA,MACtB,SAAS,GAAG;AAAA,MAAC;AAGb,uBAAiB;AAUjB,MAAAA,QAAO,oBAAoB,SAASC,SAAQ;AACxC,yBAAiBA;AAAA,MACrB;AAUA,MAAAD,QAAO,cAAc,SAAS,QAAQ,aAAa;AAC/C,iBAAS,UAAU;AACnB,YAAI,OAAO,WAAW;AAClB,gBAAM,MAAM,wBAAuB,OAAO,SAAQ,OAAM,OAAO,WAAY;AAC/E,YAAI,SAAS;AACT,mBAAS;AAAA,iBACJ,SAAS;AACd,mBAAS;AACb,YAAI,OAAO,CAAC;AACZ,aAAK,KAAK,MAAM;AAChB,YAAI,SAAS;AACT,eAAK,KAAK,GAAG;AACjB,aAAK,KAAK,OAAO,SAAS,CAAC;AAC3B,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,cAAc,OAAO,eAAe,GAAG,eAAe,CAAC;AACjE,eAAO,KAAK,KAAK,EAAE;AAAA,MACvB;AAWA,MAAAA,QAAO,UAAU,SAAS,QAAQ,aAAa,UAAU;AACrD,YAAI,OAAO,gBAAgB;AACvB,qBAAW,aACX,cAAc;AAClB,YAAI,OAAO,WAAW;AAClB,qBAAW,QACX,SAAS;AACb,YAAI,OAAO,WAAW;AAClB,mBAAS;AAAA,iBACJ,OAAO,WAAW;AACvB,gBAAM,MAAM,wBAAuB,OAAO,MAAO;AAErD,iBAAS,OAAOE,WAAU;AACtB,UAAAC,UAAS,WAAW;AAChB,gBAAI;AACA,cAAAD,UAAS,MAAMF,QAAO,YAAY,MAAM,CAAC;AAAA,YAC7C,SAAS,KAAK;AACV,cAAAE,UAAS,GAAG;AAAA,YAChB;AAAA,UACJ,CAAC;AAAA,QACL;AARS;AAUT,YAAI,UAAU;AACV,cAAI,OAAO,aAAa;AACpB,kBAAM,MAAM,uBAAqB,OAAO,QAAS;AACrD,iBAAO,QAAQ;AAAA,QACnB;AACI,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AACzC,mBAAO,SAAS,KAAK,KAAK;AACtB,kBAAI,KAAK;AACL,uBAAO,GAAG;AACV;AAAA,cACJ;AACA,sBAAQ,GAAG;AAAA,YACf,CAAC;AAAA,UACL,CAAC;AAAA,MACT;AASA,MAAAF,QAAO,WAAW,SAAS,GAAG,MAAM;AAChC,YAAI,OAAO,SAAS;AAChB,iBAAO;AACX,YAAI,OAAO,SAAS;AAChB,iBAAOA,QAAO,YAAY,IAAI;AAClC,YAAI,OAAO,MAAM,YAAY,OAAO,SAAS;AACzC,gBAAM,MAAM,wBAAuB,OAAO,IAAG,OAAM,OAAO,IAAK;AACnE,eAAO,MAAM,GAAG,IAAI;AAAA,MACxB;AAaA,MAAAA,QAAO,OAAO,SAAS,GAAG,MAAM,UAAU,kBAAkB;AAExD,iBAAS,OAAOE,WAAU;AACtB,cAAI,OAAO,MAAM,YAAY,OAAO,SAAS;AACzC,YAAAF,QAAO,QAAQ,MAAM,SAAS,KAAKI,OAAM;AACrC,oBAAM,GAAGA,OAAMF,WAAU,gBAAgB;AAAA,YAC7C,CAAC;AAAA,mBACI,OAAO,MAAM,YAAY,OAAO,SAAS;AAC9C,kBAAM,GAAG,MAAMA,WAAU,gBAAgB;AAAA;AAEzC,YAAAC,UAASD,UAAS,KAAK,MAAM,MAAM,wBAAuB,OAAO,IAAG,OAAM,OAAO,IAAK,CAAC,CAAC;AAAA,QAChG;AATS;AAWT,YAAI,UAAU;AACV,cAAI,OAAO,aAAa;AACpB,kBAAM,MAAM,uBAAqB,OAAO,QAAS;AACrD,iBAAO,QAAQ;AAAA,QACnB;AACI,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AACzC,mBAAO,SAAS,KAAK,KAAK;AACtB,kBAAI,KAAK;AACL,uBAAO,GAAG;AACV;AAAA,cACJ;AACA,sBAAQ,GAAG;AAAA,YACf,CAAC;AAAA,UACL,CAAC;AAAA,MACT;AASA,eAAS,kBAAkB,OAAO,SAAS;AACvC,YAAI,QAAQ,GACR,QAAQ;AACZ,iBAAS,IAAE,GAAG,IAAE,MAAM,QAAQ,IAAE,GAAG,EAAE,GAAG;AACpC,cAAI,MAAM,WAAW,CAAC,MAAM,QAAQ,WAAW,CAAC;AAC5C,cAAE;AAAA;AAEF,cAAE;AAAA,QACV;AAEA,YAAI,QAAQ;AACR,iBAAO;AACX,eAAO,UAAU;AAAA,MACrB;AAbS;AAuBT,MAAAF,QAAO,cAAc,SAAS,GAAGK,OAAM;AACnC,YAAI,OAAO,MAAM,YAAY,OAAOA,UAAS;AACzC,gBAAM,MAAM,wBAAuB,OAAO,IAAG,OAAM,OAAOA,KAAK;AACnE,YAAIA,MAAK,WAAW;AAChB,iBAAO;AACX,eAAO,kBAAkBL,QAAO,SAAS,GAAGK,MAAK,OAAO,GAAGA,MAAK,SAAO,EAAE,CAAC,GAAGA,KAAI;AAAA,MACrF;AAaA,MAAAL,QAAO,UAAU,SAAS,GAAGK,OAAM,UAAU,kBAAkB;AAE3D,iBAAS,OAAOH,WAAU;AACtB,cAAI,OAAO,MAAM,YAAY,OAAOG,UAAS,UAAU;AACnD,YAAAF,UAASD,UAAS,KAAK,MAAM,MAAM,wBAAuB,OAAO,IAAG,OAAM,OAAOG,KAAK,CAAC,CAAC;AACxF;AAAA,UACJ;AACA,cAAIA,MAAK,WAAW,IAAI;AACpB,YAAAF,UAASD,UAAS,KAAK,MAAM,MAAM,KAAK,CAAC;AACzC;AAAA,UACJ;AACA,UAAAF,QAAO,KAAK,GAAGK,MAAK,OAAO,GAAG,EAAE,GAAG,SAAS,KAAK,MAAM;AACnD,gBAAI;AACA,cAAAH,UAAS,GAAG;AAAA;AAEZ,cAAAA,UAAS,MAAM,kBAAkB,MAAMG,KAAI,CAAC;AAAA,UACpD,GAAG,gBAAgB;AAAA,QACvB;AAfS;AAiBT,YAAI,UAAU;AACV,cAAI,OAAO,aAAa;AACpB,kBAAM,MAAM,uBAAqB,OAAO,QAAS;AACrD,iBAAO,QAAQ;AAAA,QACnB;AACI,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AACzC,mBAAO,SAAS,KAAK,KAAK;AACtB,kBAAI,KAAK;AACL,uBAAO,GAAG;AACV;AAAA,cACJ;AACA,sBAAQ,GAAG;AAAA,YACf,CAAC;AAAA,UACL,CAAC;AAAA,MACT;AASA,MAAAL,QAAO,YAAY,SAASK,OAAM;AAC9B,YAAI,OAAOA,UAAS;AAChB,gBAAM,MAAM,wBAAuB,OAAOA,KAAK;AACnD,eAAO,SAASA,MAAK,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAAA,MAC1C;AASA,MAAAL,QAAO,UAAU,SAASK,OAAM;AAC5B,YAAI,OAAOA,UAAS;AAChB,gBAAM,MAAM,wBAAuB,OAAOA,KAAK;AACnD,YAAIA,MAAK,WAAW;AAChB,gBAAM,MAAM,0BAAwBA,MAAK,SAAO,QAAQ;AAC5D,eAAOA,MAAK,UAAU,GAAG,EAAE;AAAA,MAC/B;AAQA,UAAIF,YAAW,OAAO,YAAY,eAAe,WAAW,OAAO,QAAQ,aAAa,aACjF,OAAO,iBAAiB,aAAa,eAAe,QAAQ,WAC7D;AAQN,eAAS,cAAc,KAAK;AACxB,YAAI,MAAM,CAAC,GACP,IAAI;AACR,aAAK,kBAAkB,WAAW;AAC9B,cAAI,KAAK,IAAI,OAAQ,QAAO;AAC5B,iBAAO,IAAI,WAAW,GAAG;AAAA,QAC7B,GAAG,SAAS,GAAG;AACX,cAAI,KAAK,CAAC;AAAA,QACd,CAAC;AACD,eAAO;AAAA,MACX;AAVS;AAoBT,UAAI,cAAc,mEAAmE,MAAM,EAAE;AAO7F,UAAI,eAAe;AAAA,QAAC;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAG;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAC/D;AAAA,QAAI;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,MAAE;AAM9C,UAAI,qBAAqB,OAAO;AAShC,eAAS,cAAc,GAAG,KAAK;AAC3B,YAAIG,OAAM,GACN,KAAK,CAAC,GACN,IAAI;AACR,YAAI,OAAO,KAAK,MAAM,EAAE;AACpB,gBAAM,MAAM,kBAAgB,GAAG;AACnC,eAAOA,OAAM,KAAK;AACd,eAAK,EAAEA,MAAK,IAAI;AAChB,aAAG,KAAK,YAAa,MAAM,IAAK,EAAI,CAAC;AACrC,gBAAM,KAAK,MAAS;AACpB,cAAIA,QAAO,KAAK;AACZ,eAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B;AAAA,UACJ;AACA,eAAK,EAAEA,MAAK,IAAI;AAChB,gBAAO,MAAM,IAAK;AAClB,aAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B,gBAAM,KAAK,OAAS;AACpB,cAAIA,QAAO,KAAK;AACZ,eAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B;AAAA,UACJ;AACA,eAAK,EAAEA,MAAK,IAAI;AAChB,gBAAO,MAAM,IAAK;AAClB,aAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B,aAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAAA,QAClC;AACA,eAAO,GAAG,KAAK,EAAE;AAAA,MACrB;AA5BS;AAqCT,eAAS,cAAc,GAAG,KAAK;AAC3B,YAAIA,OAAM,GACN,OAAO,EAAE,QACT,OAAO,GACP,KAAK,CAAC,GACN,IAAI,IAAI,IAAI,IAAI,GAAG;AACvB,YAAI,OAAO;AACP,gBAAM,MAAM,kBAAgB,GAAG;AACnC,eAAOA,OAAM,OAAO,KAAK,OAAO,KAAK;AACjC,iBAAO,EAAE,WAAWA,MAAK;AACzB,eAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,iBAAO,EAAE,WAAWA,MAAK;AACzB,eAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,cAAI,MAAM,MAAM,MAAM;AAClB;AACJ,cAAK,MAAM,MAAO;AAClB,gBAAM,KAAK,OAAS;AACpB,aAAG,KAAK,mBAAmB,CAAC,CAAC;AAC7B,cAAI,EAAE,QAAQ,OAAOA,QAAO;AACxB;AACJ,iBAAO,EAAE,WAAWA,MAAK;AACzB,eAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,cAAI,MAAM;AACN;AACJ,eAAM,KAAK,OAAS,MAAO;AAC3B,gBAAM,KAAK,OAAS;AACpB,aAAG,KAAK,mBAAmB,CAAC,CAAC;AAC7B,cAAI,EAAE,QAAQ,OAAOA,QAAO;AACxB;AACJ,iBAAO,EAAE,WAAWA,MAAK;AACzB,eAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,eAAM,KAAK,MAAS,MAAO;AAC3B,eAAK;AACL,aAAG,KAAK,mBAAmB,CAAC,CAAC;AAC7B,YAAE;AAAA,QACN;AACA,YAAI,MAAM,CAAC;AACX,aAAKA,OAAM,GAAGA,OAAI,MAAMA;AACpB,cAAI,KAAK,GAAGA,IAAG,EAAE,WAAW,CAAC,CAAC;AAClC,eAAO;AAAA,MACX;AAxCS;AA+CT,UAAI,OAAO,WAAW;AAClB;AAOA,YAAIC,QAAO,CAAC;AAOZ,QAAAA,MAAK,gBAAgB;AAQrB,QAAAA,MAAK,aAAa,SAAS,KAAK,KAAK;AACjC,cAAI,KAAK;AACT,cAAI,OAAO,QAAQ;AACf,iBAAK,KACL,MAAM,kCAAW;AAAE,qBAAO;AAAA,YAAM,GAA1B;AACV,iBAAO,OAAO,SAAS,KAAK,IAAI,OAAO,MAAM;AACzC,gBAAI,KAAK;AACL,kBAAI,KAAG,GAAI;AAAA,qBACN,KAAK;AACV,kBAAM,MAAI,IAAG,KAAM,GAAI,GACvB,IAAK,KAAG,KAAM,GAAI;AAAA,qBACb,KAAK;AACV,kBAAM,MAAI,KAAI,KAAM,GAAI,GACxB,IAAM,MAAI,IAAG,KAAM,GAAI,GACvB,IAAK,KAAG,KAAM,GAAI;AAAA;AAElB,kBAAM,MAAI,KAAI,IAAM,GAAI,GACxB,IAAM,MAAI,KAAI,KAAM,GAAI,GACxB,IAAM,MAAI,IAAG,KAAM,GAAI,GACvB,IAAK,KAAG,KAAM,GAAI;AACtB,iBAAK;AAAA,UACT;AAAA,QACJ;AAWA,QAAAA,MAAK,aAAa,SAAS,KAAK,KAAK;AACjC,cAAI,GAAG,GAAG,GAAG,GAAG,OAAO,gCAASC,IAAG;AAC/B,YAAAA,KAAIA,GAAE,MAAM,GAAGA,GAAE,QAAQ,IAAI,CAAC;AAC9B,gBAAI,MAAM,MAAMA,GAAE,SAAS,CAAC;AAC5B,gBAAI,OAAO;AACX,gBAAI,OAAO,IAAIA;AACf,kBAAM;AAAA,UACV,GANuB;AAOvB,kBAAQ,IAAI,IAAI,OAAO,MAAM;AACzB,iBAAK,IAAE,SAAU;AACb,kBAAI,CAAC;AAAA,sBACC,IAAE,SAAU;AAClB,eAAE,IAAI,IAAI,OAAO,QAAS,KAAK,CAAC,GAAG,CAAC,CAAC,GACrC,KAAM,IAAE,OAAO,IAAM,IAAE,EAAK;AAAA,sBACtB,IAAE,SAAU;AAClB,gBAAE,IAAE,IAAI,OAAO,SAAS,IAAE,IAAI,OAAO,SAAS,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAC5D,KAAM,IAAE,OAAO,MAAQ,IAAE,OAAO,IAAM,IAAE,EAAK;AAAA,sBACvC,IAAE,SAAU;AAClB,gBAAE,IAAE,IAAI,OAAO,SAAS,IAAE,IAAI,OAAO,SAAS,IAAE,IAAI,OAAO,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GACrF,KAAM,IAAE,MAAO,MAAQ,IAAE,OAAO,MAAQ,IAAE,OAAO,IAAM,IAAE,EAAK;AAAA,gBAC7D,OAAM,WAAW,4BAA0B,CAAC;AAAA,UACrD;AAAA,QACJ;AASA,QAAAD,MAAK,cAAc,SAAS,KAAK,KAAK;AAClC,cAAI,IAAI,KAAK;AACb,iBAAO,MAAM;AACT,iBAAK,KAAK,OAAO,OAAO,KAAK,IAAI,OAAO;AACpC;AACJ,gBAAI,MAAM,SAAU,MAAM,OAAQ;AAC9B,mBAAK,KAAK,IAAI,OAAO,MAAM;AACvB,oBAAI,MAAM,SAAU,MAAM,OAAQ;AAC9B,uBAAK,KAAG,SAAQ,OAAM,KAAG,QAAO,KAAO;AACvC,uBAAK;AAAM;AAAA,gBACf;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,EAAE;AAAA,UACV;AACA,cAAI,OAAO,KAAM,KAAI,EAAE;AAAA,QAC3B;AASA,QAAAA,MAAK,cAAc,SAAS,KAAK,KAAK;AAClC,cAAI,KAAK;AACT,cAAI,OAAO,QAAQ;AACf,iBAAK,KAAK,MAAM,kCAAW;AAAE,qBAAO;AAAA,YAAM,GAA1B;AACpB,iBAAO,OAAO,SAAS,KAAK,IAAI,OAAO,MAAM;AACzC,gBAAI,MAAM;AACN,kBAAI,EAAE;AAAA;AAEN,oBAAM,OACN,KAAK,MAAI,MAAI,KAAM,GACnB,IAAK,KAAG,OAAO,KAAM;AACzB,iBAAK;AAAA,UACT;AAAA,QACJ;AAQA,QAAAA,MAAK,oBAAoB,SAAS,KAAK,KAAK;AACxC,UAAAA,MAAK,YAAY,KAAK,SAAS,IAAI;AAC/B,YAAAA,MAAK,WAAW,IAAI,GAAG;AAAA,UAC3B,CAAC;AAAA,QACL;AAUA,QAAAA,MAAK,oBAAoB,SAAS,KAAK,KAAK;AACxC,UAAAA,MAAK,WAAW,KAAK,SAAS,IAAI;AAC9B,YAAAA,MAAK,YAAY,IAAI,GAAG;AAAA,UAC5B,CAAC;AAAA,QACL;AAOA,QAAAA,MAAK,qBAAqB,SAAS,IAAI;AACnC,iBAAQ,KAAK,MAAQ,IAAK,KAAK,OAAS,IAAK,KAAK,QAAW,IAAI;AAAA,QACrE;AAQA,QAAAA,MAAK,gBAAgB,SAAS,KAAK;AAC/B,cAAI,IAAI,IAAE;AACV,kBAAQ,KAAK,IAAI,OAAO;AACpB,iBAAKA,MAAK,mBAAmB,EAAE;AACnC,iBAAO;AAAA,QACX;AAQA,QAAAA,MAAK,uBAAuB,SAAS,KAAK;AACtC,cAAI,IAAE,GAAG,IAAE;AACX,UAAAA,MAAK,YAAY,KAAK,SAAS,IAAI;AAC/B,cAAE;AAAG,iBAAKA,MAAK,mBAAmB,EAAE;AAAA,UACxC,CAAC;AACD,iBAAO,CAAC,GAAE,CAAC;AAAA,QACf;AAEA,eAAOA;AAAA,MACX,EAAE;AAEF,WAAK,MAAM,KAAK,OAAO,WAAW;AAAE,eAAO,CAAC,oBAAI;AAAA,MAAM;AAOtD,UAAI,kBAAkB;AAOtB,UAAI,8BAA8B;AAOlC,UAAI,sBAAsB;AAO1B,UAAI,qBAAqB;AAOzB,UAAI,SAAS;AAAA,QACT;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,MAC5B;AAOA,UAAI,SAAS;AAAA,QACT;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,MACxC;AAOA,UAAI,SAAS;AAAA,QACT;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,MACJ;AAUA,eAAS,UAAU,IAAID,MAAK,GAAG,GAAG;AAC9B,YAAI,GACA,IAAI,GAAGA,IAAG,GACV,IAAI,GAAGA,OAAM,CAAC;AAElB,aAAK,EAAE,CAAC;AAoBR,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AAEZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AAEZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AAEZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AAEZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AAEb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AACb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AAEb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AACb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AAEb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AACb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AAEb,WAAGA,IAAG,IAAI,IAAI,EAAE,sBAAsB,CAAC;AACvC,WAAGA,OAAM,CAAC,IAAI;AACd,eAAO;AAAA,MACX;AApHS;AA4HT,eAAS,cAAc,MAAM,MAAM;AAC/B,iBAAS,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,EAAE;AAC/B,iBAAQ,QAAQ,IAAM,KAAK,IAAI,IAAI,KACnC,QAAQ,OAAO,KAAK,KAAK;AAC7B,eAAO,EAAE,KAAK,MAAM,KAAW;AAAA,MACnC;AALS;AAaT,eAAS,KAAK,KAAK,GAAG,GAAG;AACrB,YAAI,SAAS,GACT,KAAK,CAAC,GAAG,CAAC,GACV,OAAO,EAAE,QACT,OAAO,EAAE,QACT;AACJ,iBAAS,IAAI,GAAG,IAAI,MAAM;AACtB,eAAK,cAAc,KAAK,MAAM,GAC9B,SAAS,GAAG,MACZ,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACrB,aAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACvB,eAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AACnB,aAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACvB,eAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AAAA,MACvB;AAlBS;AA4BT,eAAS,QAAQ,MAAM,KAAK,GAAG,GAAG;AAC9B,YAAI,OAAO,GACP,KAAK,CAAC,GAAG,CAAC,GACV,OAAO,EAAE,QACT,OAAO,EAAE,QACT;AACJ,iBAAS,IAAI,GAAG,IAAI,MAAM;AACtB,eAAK,cAAc,KAAK,IAAI,GAC5B,OAAO,GAAG,MACV,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACrB,eAAO;AACP,aAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACvB,eAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AACnB,aAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACvB,eAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AAAA,MACvB;AA/BS;AA4CT,eAAS,OAAO,GAAG,MAAM,QAAQ,UAAU,kBAAkB;AACzD,YAAI,QAAQ,OAAO,MAAM,GACrB,OAAO,MAAM,QACb;AAGJ,YAAI,SAAS,KAAK,SAAS,IAAI;AAC3B,gBAAM,MAAM,sCAAoC,MAAM;AACtD,cAAI,UAAU;AACV,YAAAH,UAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AACI,kBAAM;AAAA,QACd;AACA,YAAI,KAAK,WAAW,iBAAiB;AACjC,gBAAK,MAAM,0BAAwB,KAAK,SAAO,SAAO,eAAe;AACrE,cAAI,UAAU;AACV,YAAAA,UAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AACI,kBAAM;AAAA,QACd;AACA,iBAAU,KAAK,WAAY;AAE3B,YAAI,GAAG,GAAG,IAAI,GAAG;AAGjB,YAAI,YAAY;AACZ,cAAI,IAAI,WAAW,MAAM;AACzB,cAAI,IAAI,WAAW,MAAM;AAAA,QAC7B,OAAO;AACH,cAAI,OAAO,MAAM;AACjB,cAAI,OAAO,MAAM;AAAA,QACrB;AAEA,gBAAQ,MAAM,GAAG,GAAG,CAAC;AAOrB,iBAAS,OAAO;AACZ,cAAI;AACA,6BAAiB,IAAI,MAAM;AAC/B,cAAI,IAAI,QAAQ;AACZ,gBAAI,QAAQ,KAAK,IAAI;AACrB,mBAAO,IAAI,UAAS;AAChB,kBAAI,IAAI;AACR,mBAAK,GAAG,GAAG,CAAC;AACZ,mBAAK,MAAM,GAAG,CAAC;AACf,kBAAI,KAAK,IAAI,IAAI,QAAQ;AACrB;AAAA,YACR;AAAA,UACJ,OAAO;AACH,iBAAK,IAAI,GAAG,IAAI,IAAI;AAChB,mBAAK,IAAI,GAAG,IAAK,QAAQ,GAAI;AACzB,0BAAU,OAAO,KAAK,GAAG,GAAG,CAAC;AACrC,gBAAI,MAAM,CAAC;AACX,iBAAK,IAAI,GAAG,IAAI,MAAM;AAClB,kBAAI,MAAO,MAAM,CAAC,KAAK,KAAM,SAAU,CAAC,GACxC,IAAI,MAAO,MAAM,CAAC,KAAK,KAAM,SAAU,CAAC,GACxC,IAAI,MAAO,MAAM,CAAC,KAAK,IAAK,SAAU,CAAC,GACvC,IAAI,MAAM,MAAM,CAAC,IAAI,SAAU,CAAC;AACpC,gBAAI,UAAU;AACV,uBAAS,MAAM,GAAG;AAClB;AAAA,YACJ;AACI,qBAAO;AAAA,UACf;AACA,cAAI;AACA,YAAAA,UAAS,IAAI;AAAA,QACrB;AA9BS;AAiCT,YAAI,OAAO,aAAa,aAAa;AACjC,eAAK;AAAA,QAGT,OAAO;AACH,cAAI;AACJ,iBAAO;AACH,gBAAI,QAAO,MAAM,KAAK,OAAO;AACzB,qBAAO,OAAO,CAAC;AAAA,QAC3B;AAAA,MACJ;AArFS;AAiGT,eAAS,MAAM,GAAG,MAAM,UAAU,kBAAkB;AAChD,YAAI;AACJ,YAAI,OAAO,MAAM,YAAY,OAAO,SAAS,UAAU;AACnD,gBAAM,MAAM,qCAAqC;AACjD,cAAI,UAAU;AACV,YAAAA,UAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AAEI,kBAAM;AAAA,QACd;AAGA,YAAI,OAAO;AACX,YAAI,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,CAAC,MAAM,KAAK;AAClD,gBAAM,MAAM,2BAAyB,KAAK,UAAU,GAAE,CAAC,CAAC;AACxD,cAAI,UAAU;AACV,YAAAA,UAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AAEI,kBAAM;AAAA,QACd;AACA,YAAI,KAAK,OAAO,CAAC,MAAM;AACnB,kBAAQ,OAAO,aAAa,CAAC,GAC7B,SAAS;AAAA,aACR;AACD,kBAAQ,KAAK,OAAO,CAAC;AACrB,cAAK,UAAU,OAAO,UAAU,OAAO,UAAU,OAAQ,KAAK,OAAO,CAAC,MAAM,KAAK;AAC7E,kBAAM,MAAM,4BAA0B,KAAK,UAAU,GAAE,CAAC,CAAC;AACzD,gBAAI,UAAU;AACV,cAAAA,UAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,YACJ;AACI,oBAAM;AAAA,UACd;AACA,mBAAS;AAAA,QACb;AAGA,YAAI,KAAK,OAAO,SAAS,CAAC,IAAI,KAAK;AAC/B,gBAAM,MAAM,qBAAqB;AACjC,cAAI,UAAU;AACV,YAAAA,UAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AACI,kBAAM;AAAA,QACd;AACA,YAAI,KAAK,SAAS,KAAK,UAAU,QAAQ,SAAS,CAAC,GAAG,EAAE,IAAI,IACxD,KAAK,SAAS,KAAK,UAAU,SAAS,GAAG,SAAS,CAAC,GAAG,EAAE,GACxD,SAAS,KAAK,IACd,YAAY,KAAK,UAAU,SAAS,GAAG,SAAS,EAAE;AACtD,aAAK,SAAS,MAAM,OAAS;AAE7B,YAAI,YAAY,cAAc,CAAC,GAC3B,QAAQ,cAAc,WAAW,eAAe;AAQpD,iBAAS,OAAO,OAAO;AACnB,cAAI,MAAM,CAAC;AACX,cAAI,KAAK,IAAI;AACb,cAAI,SAAS;AACT,gBAAI,KAAK,KAAK;AAClB,cAAI,KAAK,GAAG;AACZ,cAAI,SAAS;AACT,gBAAI,KAAK,GAAG;AAChB,cAAI,KAAK,OAAO,SAAS,CAAC;AAC1B,cAAI,KAAK,GAAG;AACZ,cAAI,KAAK,cAAc,OAAO,MAAM,MAAM,CAAC;AAC3C,cAAI,KAAK,cAAc,OAAO,OAAO,SAAS,IAAI,CAAC,CAAC;AACpD,iBAAO,IAAI,KAAK,EAAE;AAAA,QACtB;AAbS;AAgBT,YAAI,OAAO,YAAY;AACnB,iBAAO,OAAO,OAAO,WAAW,OAAO,MAAM,CAAC;AAAA,aAG7C;AACD,iBAAO,WAAW,OAAO,QAAQ,SAASM,MAAK,OAAO;AAClD,gBAAIA;AACA,uBAASA,MAAK,IAAI;AAAA;AAElB,uBAAS,MAAM,OAAO,KAAK,CAAC;AAAA,UACpC,GAAG,gBAAgB;AAAA,QACvB;AAAA,MACJ;AA3FS;AAqGT,MAAAT,QAAO,eAAe;AAUtB,MAAAA,QAAO,eAAe;AAEtB,aAAOA;AAAA,IACX,CAAC;AAAA;AAAA;;;ACl2CD;AAAA;AAAA;AAAAU;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;AACA,IAAI,UAAU,wBAAC,YAAY,SAAS,eAAe;AACjD,SAAO,CAACC,UAAS,SAAS;AACxB,QAAI,QAAQ;AACZ,WAAO,SAAS,CAAC;AACjB,mBAAe,SAAS,GAAG;AACzB,UAAI,KAAK,OAAO;AACd,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AACA,cAAQ;AACR,UAAI;AACJ,UAAI,UAAU;AACd,UAAI;AACJ,UAAI,WAAW,CAAC,GAAG;AACjB,kBAAU,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;AAC5B,QAAAA,SAAQ,IAAI,aAAa;AAAA,MAC3B,OAAO;AACL,kBAAU,MAAM,WAAW,UAAU,QAAQ;AAAA,MAC/C;AACA,UAAI,SAAS;AACX,YAAI;AACF,gBAAM,MAAM,QAAQA,UAAS,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,QACpD,SAAS,KAAK;AACZ,cAAI,eAAe,SAAS,SAAS;AACnC,YAAAA,SAAQ,QAAQ;AAChB,kBAAM,MAAM,QAAQ,KAAKA,QAAO;AAChC,sBAAU;AAAA,UACZ,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAIA,SAAQ,cAAc,SAAS,YAAY;AAC7C,gBAAM,MAAM,WAAWA,QAAO;AAAA,QAChC;AAAA,MACF;AACA,UAAI,QAAQA,SAAQ,cAAc,SAAS,UAAU;AACnD,QAAAA,SAAQ,MAAM;AAAA,MAChB;AACA,aAAOA;AAAA,IACT;AAnCe;AAAA,EAoCjB;AACF,GAzCc;;;ACDd;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;AACA,IAAI,mBAAmB,OAAO;;;ACD9B;AAAA;AAAA;AAAAC;AAEA,IAAI,YAAY,8BAAO,SAAS,UAA0B,uBAAO,OAAO,IAAI,MAAM;AAChF,QAAM,EAAE,MAAM,OAAO,MAAM,MAAM,IAAI;AACrC,QAAM,UAAU,mBAAmB,cAAc,QAAQ,IAAI,UAAU,QAAQ;AAC/E,QAAM,cAAc,QAAQ,IAAI,cAAc;AAC9C,MAAI,aAAa,WAAW,qBAAqB,KAAK,aAAa,WAAW,mCAAmC,GAAG;AAClH,WAAO,cAAc,SAAS,EAAE,KAAK,IAAI,CAAC;AAAA,EAC5C;AACA,SAAO,CAAC;AACV,GARgB;AAShB,eAAe,cAAc,SAAS,SAAS;AAC7C,QAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,MAAI,UAAU;AACZ,WAAO,0BAA0B,UAAU,OAAO;AAAA,EACpD;AACA,SAAO,CAAC;AACV;AANe;AAOf,SAAS,0BAA0B,UAAU,SAAS;AACpD,QAAM,OAAuB,uBAAO,OAAO,IAAI;AAC/C,WAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,UAAM,uBAAuB,QAAQ,OAAO,IAAI,SAAS,IAAI;AAC7D,QAAI,CAAC,sBAAsB;AACzB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,6BAAuB,MAAM,KAAK,KAAK;AAAA,IACzC;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,KAAK;AACf,WAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAM,uBAAuB,IAAI,SAAS,GAAG;AAC7C,UAAI,sBAAsB;AACxB,kCAA0B,MAAM,KAAK,KAAK;AAC1C,eAAO,KAAK,GAAG;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AApBS;AAqBT,IAAI,yBAAyB,wBAAC,MAAM,KAAK,UAAU;AACjD,MAAI,KAAK,GAAG,MAAM,QAAQ;AACxB,QAAI,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG;AAC5B;AACA,WAAK,GAAG,EAAE,KAAK,KAAK;AAAA,IACtB,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,KAAK;AAAA,IAC/B;AAAA,EACF,OAAO;AACL,QAAI,CAAC,IAAI,SAAS,IAAI,GAAG;AACvB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK;AAAA,IACpB;AAAA,EACF;AACF,GAf6B;AAgB7B,IAAI,4BAA4B,wBAAC,MAAM,KAAK,UAAU;AACpD,MAAI,aAAa;AACjB,QAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,OAAK,QAAQ,CAAC,MAAM,UAAU;AAC5B,QAAI,UAAU,KAAK,SAAS,GAAG;AAC7B,iBAAW,IAAI,IAAI;AAAA,IACrB,OAAO;AACL,UAAI,CAAC,WAAW,IAAI,KAAK,OAAO,WAAW,IAAI,MAAM,YAAY,MAAM,QAAQ,WAAW,IAAI,CAAC,KAAK,WAAW,IAAI,aAAa,MAAM;AACpI,mBAAW,IAAI,IAAoB,uBAAO,OAAO,IAAI;AAAA,MACvD;AACA,mBAAa,WAAW,IAAI;AAAA,IAC9B;AAAA,EACF,CAAC;AACH,GAbgC;;;ACvDhC;AAAA;AAAA;AAAAC;AACA,IAAI,YAAY,wBAAC,SAAS;AACxB,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,MAAI,MAAM,CAAC,MAAM,IAAI;AACnB,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT,GANgB;AAOhB,IAAI,mBAAmB,wBAAC,cAAc;AACpC,QAAM,EAAE,QAAQ,KAAK,IAAI,sBAAsB,SAAS;AACxD,QAAM,QAAQ,UAAU,IAAI;AAC5B,SAAO,kBAAkB,OAAO,MAAM;AACxC,GAJuB;AAKvB,IAAI,wBAAwB,wBAAC,SAAS;AACpC,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,QAAQ,cAAc,CAAC,OAAO,UAAU;AAClD,UAAM,OAAO,IAAI,KAAK;AACtB,WAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AACzB,WAAO;AAAA,EACT,CAAC;AACD,SAAO,EAAE,QAAQ,KAAK;AACxB,GAR4B;AAS5B,IAAI,oBAAoB,wBAAC,OAAO,WAAW;AACzC,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,UAAI,MAAM,CAAC,EAAE,SAAS,IAAI,GAAG;AAC3B,cAAM,CAAC,IAAI,MAAM,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAXwB;AAYxB,IAAI,eAAe,CAAC;AACpB,IAAI,aAAa,wBAAC,OAAO,SAAS;AAChC,MAAI,UAAU,KAAK;AACjB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM,MAAM,6BAA6B;AACvD,MAAI,OAAO;AACT,UAAM,WAAW,GAAG,KAAK,IAAI,IAAI;AACjC,QAAI,CAAC,aAAa,QAAQ,GAAG;AAC3B,UAAI,MAAM,CAAC,GAAG;AACZ,qBAAa,QAAQ,IAAI,QAAQ,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,CAAC,UAAU,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAAA,MACpL,OAAO;AACL,qBAAa,QAAQ,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,MACjD;AAAA,IACF;AACA,WAAO,aAAa,QAAQ;AAAA,EAC9B;AACA,SAAO;AACT,GAjBiB;AAkBjB,IAAI,YAAY,wBAAC,KAAK,YAAY;AAChC,MAAI;AACF,WAAO,QAAQ,GAAG;AAAA,EACpB,QAAQ;AACN,WAAO,IAAI,QAAQ,yBAAyB,CAAC,UAAU;AACrD,UAAI;AACF,eAAO,QAAQ,KAAK;AAAA,MACtB,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF,GAZgB;AAahB,IAAI,eAAe,wBAAC,QAAQ,UAAU,KAAK,SAAS,GAAjC;AACnB,IAAI,UAAU,wBAAC,YAAY;AACzB,QAAM,MAAM,QAAQ;AACpB,QAAM,QAAQ,IAAI;AAAA,IAChB;AAAA,IACA,IAAI,WAAW,CAAC,MAAM,KAAK,KAAK;AAAA,EAClC;AACA,MAAI,IAAI;AACR,SAAO,IAAI,IAAI,QAAQ,KAAK;AAC1B,UAAM,WAAW,IAAI,WAAW,CAAC;AACjC,QAAI,aAAa,IAAI;AACnB,YAAM,aAAa,IAAI,QAAQ,KAAK,CAAC;AACrC,YAAM,OAAO,IAAI,MAAM,OAAO,eAAe,KAAK,SAAS,UAAU;AACrE,aAAO,aAAa,KAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,QAAQ,OAAO,IAAI,IAAI;AAAA,IACjF,WAAW,aAAa,IAAI;AAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,MAAM,OAAO,CAAC;AAC3B,GAlBc;AAuBd,IAAI,kBAAkB,wBAAC,YAAY;AACjC,QAAM,SAAS,QAAQ,OAAO;AAC9B,SAAO,OAAO,SAAS,KAAK,OAAO,GAAG,EAAE,MAAM,MAAM,OAAO,MAAM,GAAG,EAAE,IAAI;AAC5E,GAHsB;AAItB,IAAI,YAAY,wBAAC,MAAM,QAAQ,SAAS;AACtC,MAAI,KAAK,QAAQ;AACf,UAAM,UAAU,KAAK,GAAG,IAAI;AAAA,EAC9B;AACA,SAAO,GAAG,OAAO,CAAC,MAAM,MAAM,KAAK,GAAG,GAAG,IAAI,GAAG,QAAQ,MAAM,KAAK,GAAG,MAAM,GAAG,EAAE,MAAM,MAAM,KAAK,GAAG,GAAG,MAAM,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,GAAG,EAAE;AACjJ,GALgB;AAMhB,IAAI,yBAAyB,wBAAC,SAAS;AACrC,MAAI,KAAK,WAAW,KAAK,SAAS,CAAC,MAAM,MAAM,CAAC,KAAK,SAAS,GAAG,GAAG;AAClE,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,GAAG;AAC/B,QAAM,UAAU,CAAC;AACjB,MAAI,WAAW;AACf,WAAS,QAAQ,CAAC,YAAY;AAC5B,QAAI,YAAY,MAAM,CAAC,KAAK,KAAK,OAAO,GAAG;AACzC,kBAAY,MAAM;AAAA,IACpB,WAAW,KAAK,KAAK,OAAO,GAAG;AAC7B,UAAI,KAAK,KAAK,OAAO,GAAG;AACtB,YAAI,QAAQ,WAAW,KAAK,aAAa,IAAI;AAC3C,kBAAQ,KAAK,GAAG;AAAA,QAClB,OAAO;AACL,kBAAQ,KAAK,QAAQ;AAAA,QACvB;AACA,cAAM,kBAAkB,QAAQ,QAAQ,KAAK,EAAE;AAC/C,oBAAY,MAAM;AAClB,gBAAQ,KAAK,QAAQ;AAAA,MACvB,OAAO;AACL,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AACvD,GA1B6B;AA2B7B,IAAI,aAAa,wBAAC,UAAU;AAC1B,MAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,YAAQ,MAAM,QAAQ,OAAO,GAAG;AAAA,EAClC;AACA,SAAO,MAAM,QAAQ,GAAG,MAAM,KAAK,UAAU,OAAO,mBAAmB,IAAI;AAC7E,GARiB;AASjB,IAAI,iBAAiB,wBAAC,KAAK,KAAK,aAAa;AAC3C,MAAI;AACJ,MAAI,CAAC,YAAY,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACzC,QAAI,YAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AACxC,QAAI,cAAc,IAAI;AACpB,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AAAA,IACtC;AACA,WAAO,cAAc,IAAI;AACvB,YAAM,kBAAkB,IAAI,WAAW,YAAY,IAAI,SAAS,CAAC;AACjE,UAAI,oBAAoB,IAAI;AAC1B,cAAM,aAAa,YAAY,IAAI,SAAS;AAC5C,cAAM,WAAW,IAAI,QAAQ,KAAK,UAAU;AAC5C,eAAO,WAAW,IAAI,MAAM,YAAY,aAAa,KAAK,SAAS,QAAQ,CAAC;AAAA,MAC9E,WAAW,mBAAmB,MAAM,MAAM,eAAe,GAAG;AAC1D,eAAO;AAAA,MACT;AACA,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,YAAY,CAAC;AAAA,IAClD;AACA,cAAU,OAAO,KAAK,GAAG;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,UAAU,CAAC;AACjB,cAAY,OAAO,KAAK,GAAG;AAC3B,MAAI,WAAW,IAAI,QAAQ,KAAK,CAAC;AACjC,SAAO,aAAa,IAAI;AACtB,UAAM,eAAe,IAAI,QAAQ,KAAK,WAAW,CAAC;AAClD,QAAI,aAAa,IAAI,QAAQ,KAAK,QAAQ;AAC1C,QAAI,aAAa,gBAAgB,iBAAiB,IAAI;AACpD,mBAAa;AAAA,IACf;AACA,QAAI,OAAO,IAAI;AAAA,MACb,WAAW;AAAA,MACX,eAAe,KAAK,iBAAiB,KAAK,SAAS,eAAe;AAAA,IACpE;AACA,QAAI,SAAS;AACX,aAAO,WAAW,IAAI;AAAA,IACxB;AACA,eAAW;AACX,QAAI,SAAS,IAAI;AACf;AAAA,IACF;AACA,QAAI;AACJ,QAAI,eAAe,IAAI;AACrB,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ,IAAI,MAAM,aAAa,GAAG,iBAAiB,KAAK,SAAS,YAAY;AAC7E,UAAI,SAAS;AACX,gBAAQ,WAAW,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,UAAU;AACZ,UAAI,EAAE,QAAQ,IAAI,KAAK,MAAM,QAAQ,QAAQ,IAAI,CAAC,IAAI;AACpD,gBAAQ,IAAI,IAAI,CAAC;AAAA,MACnB;AACA;AACA,cAAQ,IAAI,EAAE,KAAK,KAAK;AAAA,IAC1B,OAAO;AACL,cAAQ,IAAI,MAAM;AAAA,IACpB;AAAA,EACF;AACA,SAAO,MAAM,QAAQ,GAAG,IAAI;AAC9B,GA/DqB;AAgErB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB,wBAAC,KAAK,QAAQ;AACjC,SAAO,eAAe,KAAK,KAAK,IAAI;AACtC,GAFqB;AAGrB,IAAI,sBAAsB;;;AHxM1B,IAAI,wBAAwB,wBAAC,QAAQ,UAAU,KAAK,mBAAmB,GAA3C;AAC5B,IAAI,cAAc,MAAM;AAAA,EALxB,OAKwB;AAAA;AAAA;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA,YAAY,CAAC;AAAA,EACb,YAAY,SAAS,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,GAAG;AACnD,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA,EACA,MAAM,KAAK;AACT,WAAO,MAAM,KAAK,iBAAiB,GAAG,IAAI,KAAK,qBAAqB;AAAA,EACtE;AAAA,EACA,iBAAiB,KAAK;AACpB,UAAM,WAAW,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG;AAC7D,UAAM,QAAQ,KAAK,eAAe,QAAQ;AAC1C,WAAO,QAAQ,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI,QAAQ;AAAA,EAC3E;AAAA,EACA,uBAAuB;AACrB,UAAM,UAAU,CAAC;AACjB,UAAM,OAAO,OAAO,KAAK,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,CAAC;AACjE,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,KAAK,eAAe,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC;AAC/E,UAAI,SAAS,OAAO,UAAU,UAAU;AACtC,gBAAQ,GAAG,IAAI,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI;AAAA,MACnE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,UAAU;AACvB,WAAO,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,EAAE,QAAQ,IAAI;AAAA,EACjE;AAAA,EACA,MAAM,KAAK;AACT,WAAO,cAAc,KAAK,KAAK,GAAG;AAAA,EACpC;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,eAAe,KAAK,KAAK,GAAG;AAAA,EACrC;AAAA,EACA,OAAO,MAAM;AACX,QAAI,MAAM;AACR,aAAO,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAAA,IACvC;AACA,UAAM,aAAa,CAAC;AACpB,SAAK,IAAI,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,iBAAW,GAAG,IAAI;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,MAAM,UAAU,SAAS;AACvB,WAAO,KAAK,UAAU,eAAe,MAAM,UAAU,MAAM,OAAO;AAAA,EACpE;AAAA,EACA,cAAc,wBAAC,QAAQ;AACrB,UAAM,EAAE,WAAW,KAAAC,KAAI,IAAI;AAC3B,UAAM,aAAa,UAAU,GAAG;AAChC,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,UAAM,eAAe,OAAO,KAAK,SAAS,EAAE,CAAC;AAC7C,QAAI,cAAc;AAChB,aAAO,UAAU,YAAY,EAAE,KAAK,CAAC,SAAS;AAC5C,YAAI,iBAAiB,QAAQ;AAC3B,iBAAO,KAAK,UAAU,IAAI;AAAA,QAC5B;AACA,eAAO,IAAI,SAAS,IAAI,EAAE,GAAG,EAAE;AAAA,MACjC,CAAC;AAAA,IACH;AACA,WAAO,UAAU,GAAG,IAAIA,KAAI,GAAG,EAAE;AAAA,EACnC,GAhBc;AAAA,EAiBd,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,YAAY,aAAa;AAAA,EACvC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,WAAW;AACT,WAAO,KAAK,YAAY,UAAU;AAAA,EACpC;AAAA,EACA,iBAAiB,QAAQ,MAAM;AAC7B,SAAK,eAAe,MAAM,IAAI;AAAA,EAChC;AAAA,EACA,MAAM,QAAQ;AACZ,WAAO,KAAK,eAAe,MAAM;AAAA,EACnC;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,KAAK,gBAAgB,IAAI;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK;AAAA,EACxD;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK,EAAE,KAAK,UAAU,EAAE;AAAA,EAC3E;AACF;;;AIhHA;AAAA;AAAA;AAAAC;AACA,IAAI,2BAA2B;AAAA,EAC7B,WAAW;AAAA,EACX,cAAc;AAAA,EACd,QAAQ;AACV;AACA,IAAI,MAAM,wBAAC,OAAO,cAAc;AAC9B,QAAM,gBAAgB,IAAI,OAAO,KAAK;AACtC,gBAAc,YAAY;AAC1B,gBAAc,YAAY;AAC1B,SAAO;AACT,GALU;AAgFV,IAAI,kBAAkB,8BAAO,KAAK,OAAO,mBAAmBC,UAAS,WAAW;AAC9E,MAAI,OAAO,QAAQ,YAAY,EAAE,eAAe,SAAS;AACvD,QAAI,EAAE,eAAe,UAAU;AAC7B,YAAM,IAAI,SAAS;AAAA,IACrB;AACA,QAAI,eAAe,SAAS;AAC1B,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AACA,QAAM,YAAY,IAAI;AACtB,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO,QAAQ,QAAQ,GAAG;AAAA,EAC5B;AACA,MAAI,QAAQ;AACV,WAAO,CAAC,KAAK;AAAA,EACf,OAAO;AACL,aAAS,CAAC,GAAG;AAAA,EACf;AACA,QAAM,SAAS,QAAQ,IAAI,UAAU,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,QAAQ,SAAAA,SAAQ,CAAC,CAAC,CAAC,EAAE;AAAA,IAC9E,CAAC,QAAQ,QAAQ;AAAA,MACf,IAAI,OAAO,OAAO,EAAE,IAAI,CAAC,SAAS,gBAAgB,MAAM,OAAO,OAAOA,UAAS,MAAM,CAAC;AAAA,IACxF,EAAE,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,EACxB;AACA,MAAI,mBAAmB;AACrB,WAAO,IAAI,MAAM,QAAQ,SAAS;AAAA,EACpC,OAAO;AACL,WAAO;AAAA,EACT;AACF,GA5BsB;;;ALnFtB,IAAI,aAAa;AACjB,IAAI,wBAAwB,wBAAC,aAAa,YAAY;AACpD,SAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL;AACF,GAL4B;AAM5B,IAAI,UAAU,MAAM;AAAA,EAVpB,OAUoB;AAAA;AAAA;AAAA,EAClB;AAAA,EACA;AAAA,EACA,MAAM,CAAC;AAAA,EACP;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,KAAK,SAAS;AACxB,SAAK,cAAc;AACnB,QAAI,SAAS;AACX,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,MAAM,QAAQ;AACnB,WAAK,mBAAmB,QAAQ;AAChC,WAAK,QAAQ,QAAQ;AACrB,WAAK,eAAe,QAAQ;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,SAAK,SAAS,IAAI,YAAY,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;AAC7E,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,KAAK,iBAAiB,iBAAiB,KAAK,eAAe;AAC7D,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,gCAAgC;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,sCAAsC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,SAAS,IAAI,SAAS,MAAM;AAAA,MACtC,SAAS,KAAK,qBAAqB,IAAI,QAAQ;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI,MAAM;AACZ,QAAI,KAAK,QAAQ,MAAM;AACrB,aAAO,IAAI,SAAS,KAAK,MAAM,IAAI;AACnC,iBAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAChD,YAAI,MAAM,gBAAgB;AACxB;AAAA,QACF;AACA,YAAI,MAAM,cAAc;AACtB,gBAAM,UAAU,KAAK,KAAK,QAAQ,aAAa;AAC/C,eAAK,QAAQ,OAAO,YAAY;AAChC,qBAAW,UAAU,SAAS;AAC5B,iBAAK,QAAQ,OAAO,cAAc,MAAM;AAAA,UAC1C;AAAA,QACF,OAAO;AACL,eAAK,QAAQ,IAAI,GAAG,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,SAAS,2BAAI,SAAS;AACpB,SAAK,cAAc,CAAC,YAAY,KAAK,KAAK,OAAO;AACjD,WAAO,KAAK,UAAU,GAAG,IAAI;AAAA,EAC/B,GAHS;AAAA,EAIT,YAAY,wBAAC,WAAW,KAAK,UAAU,QAA3B;AAAA,EACZ,YAAY,6BAAM,KAAK,SAAX;AAAA,EACZ,cAAc,wBAAC,aAAa;AAC1B,SAAK,YAAY;AAAA,EACnB,GAFc;AAAA,EAGd,SAAS,wBAAC,MAAM,OAAO,YAAY;AACjC,QAAI,KAAK,WAAW;AAClB,WAAK,OAAO,IAAI,SAAS,KAAK,KAAK,MAAM,KAAK,IAAI;AAAA,IACpD;AACA,UAAM,UAAU,KAAK,OAAO,KAAK,KAAK,UAAU,KAAK,qBAAqB,IAAI,QAAQ;AACtF,QAAI,UAAU,QAAQ;AACpB,cAAQ,OAAO,IAAI;AAAA,IACrB,WAAW,SAAS,QAAQ;AAC1B,cAAQ,OAAO,MAAM,KAAK;AAAA,IAC5B,OAAO;AACL,cAAQ,IAAI,MAAM,KAAK;AAAA,IACzB;AAAA,EACF,GAZS;AAAA,EAaT,SAAS,wBAAC,WAAW;AACnB,SAAK,UAAU;AAAA,EACjB,GAFS;AAAA,EAGT,MAAM,wBAAC,KAAK,UAAU;AACpB,SAAK,SAAyB,oBAAI,IAAI;AACtC,SAAK,KAAK,IAAI,KAAK,KAAK;AAAA,EAC1B,GAHM;AAAA,EAIN,MAAM,wBAAC,QAAQ;AACb,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,GAAG,IAAI;AAAA,EAC1C,GAFM;AAAA,EAGN,IAAI,MAAM;AACR,QAAI,CAAC,KAAK,MAAM;AACd,aAAO,CAAC;AAAA,IACV;AACA,WAAO,OAAO,YAAY,KAAK,IAAI;AAAA,EACrC;AAAA,EACA,aAAa,MAAM,KAAK,SAAS;AAC/B,UAAM,kBAAkB,KAAK,OAAO,IAAI,QAAQ,KAAK,KAAK,OAAO,IAAI,KAAK,oBAAoB,IAAI,QAAQ;AAC1G,QAAI,OAAO,QAAQ,YAAY,aAAa,KAAK;AAC/C,YAAM,aAAa,IAAI,mBAAmB,UAAU,IAAI,UAAU,IAAI,QAAQ,IAAI,OAAO;AACzF,iBAAW,CAAC,KAAK,KAAK,KAAK,YAAY;AACrC,YAAI,IAAI,YAAY,MAAM,cAAc;AACtC,0BAAgB,OAAO,KAAK,KAAK;AAAA,QACnC,OAAO;AACL,0BAAgB,IAAI,KAAK,KAAK;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS;AACX,iBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,OAAO,GAAG;AAC5C,YAAI,OAAO,MAAM,UAAU;AACzB,0BAAgB,IAAI,GAAG,CAAC;AAAA,QAC1B,OAAO;AACL,0BAAgB,OAAO,CAAC;AACxB,qBAAW,MAAM,GAAG;AAClB,4BAAgB,OAAO,GAAG,EAAE;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,KAAK,UAAU,KAAK;AACnE,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,SAAS,gBAAgB,CAAC;AAAA,EAChE;AAAA,EACA,cAAc,2BAAI,SAAS,KAAK,aAAa,GAAG,IAAI,GAAtC;AAAA,EACd,OAAO,wBAAC,MAAM,KAAK,YAAY,KAAK,aAAa,MAAM,KAAK,OAAO,GAA5D;AAAA,EACP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,WAAO,CAAC,KAAK,oBAAoB,CAAC,KAAK,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,YAAY,IAAI,SAAS,IAAI,IAAI,KAAK;AAAA,MAChH;AAAA,MACA;AAAA,MACA,sBAAsB,YAAY,OAAO;AAAA,IAC3C;AAAA,EACF,GANO;AAAA,EAOP,OAAO,wBAAC,QAAQ,KAAK,YAAY;AAC/B,WAAO,KAAK;AAAA,MACV,KAAK,UAAU,MAAM;AAAA,MACrB;AAAA,MACA,sBAAsB,oBAAoB,OAAO;AAAA,IACnD;AAAA,EACF,GANO;AAAA,EAOP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,UAAM,MAAM,wBAAC,UAAU,KAAK,aAAa,OAAO,KAAK,sBAAsB,4BAA4B,OAAO,CAAC,GAAnG;AACZ,WAAO,OAAO,SAAS,WAAW,gBAAgB,MAAM,yBAAyB,WAAW,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,IAAI,IAAI;AAAA,EAC7H,GAHO;AAAA,EAIP,WAAW,wBAAC,UAAU,WAAW;AAC/B,SAAK,OAAO,YAAY,OAAO,QAAQ,CAAC;AACxC,WAAO,KAAK,YAAY,MAAM,UAAU,GAAG;AAAA,EAC7C,GAHW;AAAA,EAIX,WAAW,6BAAM;AACf,SAAK,qBAAqB,MAAM,IAAI,SAAS;AAC7C,WAAO,KAAK,iBAAiB,IAAI;AAAA,EACnC,GAHW;AAIb;;;AM7KA;AAAA;AAAA;AAAAC;AACA,IAAI,kBAAkB;AACtB,IAAI,4BAA4B;AAChC,IAAI,UAAU,CAAC,OAAO,QAAQ,OAAO,UAAU,WAAW,OAAO;AACjE,IAAI,mCAAmC;AACvC,IAAI,uBAAuB,cAAc,MAAM;AAAA,EAL/C,OAK+C;AAAA;AAAA;AAC/C;;;ACNA;AAAA;AAAA;AAAAC;AACA,IAAI,mBAAmB;;;ATKvB,IAAI,kBAAkB,wBAAC,MAAM;AAC3B,SAAO,EAAE,KAAK,iBAAiB,GAAG;AACpC,GAFsB;AAGtB,IAAI,eAAe,wBAAC,KAAK,MAAM;AAC7B,MAAI,iBAAiB,KAAK;AACxB,UAAM,MAAM,IAAI,YAAY;AAC5B,WAAO,EAAE,YAAY,IAAI,MAAM,GAAG;AAAA,EACpC;AACA,UAAQ,MAAM,GAAG;AACjB,SAAO,EAAE,KAAK,yBAAyB,GAAG;AAC5C,GAPmB;AAQnB,IAAI,OAAO,MAAM;AAAA,EAjBjB,OAiBiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,aAAa,CAAC,GAAG,SAAS,yBAAyB;AACzD,eAAW,QAAQ,CAAC,WAAW;AAC7B,WAAK,MAAM,IAAI,CAAC,UAAU,SAAS;AACjC,YAAI,OAAO,UAAU,UAAU;AAC7B,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,UAAU,QAAQ,KAAK,OAAO,KAAK;AAAA,QAC1C;AACA,aAAK,QAAQ,CAAC,YAAY;AACxB,eAAK,UAAU,QAAQ,KAAK,OAAO,OAAO;AAAA,QAC5C,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,SAAK,KAAK,CAAC,QAAQ,SAAS,aAAa;AACvC,iBAAW,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG;AAC7B,aAAK,QAAQ;AACb,mBAAW,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG;AAC/B,mBAAS,IAAI,CAAC,YAAY;AACxB,iBAAK,UAAU,EAAE,YAAY,GAAG,KAAK,OAAO,OAAO;AAAA,UACrD,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,SAAK,MAAM,CAAC,SAAS,aAAa;AAChC,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ;AACb,iBAAS,QAAQ,IAAI;AAAA,MACvB;AACA,eAAS,QAAQ,CAAC,YAAY;AAC5B,aAAK,UAAU,iBAAiB,KAAK,OAAO,OAAO;AAAA,MACrD,CAAC;AACD,aAAO;AAAA,IACT;AACA,UAAM,EAAE,QAAQ,GAAG,qBAAqB,IAAI;AAC5C,WAAO,OAAO,MAAM,oBAAoB;AACxC,SAAK,UAAU,UAAU,OAAO,QAAQ,WAAW,UAAU;AAAA,EAC/D;AAAA,EACA,SAAS;AACP,UAAM,QAAQ,IAAI,KAAK;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,UAAM,eAAe,KAAK;AAC1B,UAAM,mBAAmB,KAAK;AAC9B,UAAM,SAAS,KAAK;AACpB,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,MAAM,MAAMC,MAAK;AACf,UAAM,SAAS,KAAK,SAAS,IAAI;AACjC,IAAAA,KAAI,OAAO,IAAI,CAAC,MAAM;AACpB,UAAI;AACJ,UAAIA,KAAI,iBAAiB,cAAc;AACrC,kBAAU,EAAE;AAAA,MACd,OAAO;AACL,kBAAU,8BAAO,GAAG,UAAU,MAAM,QAAQ,CAAC,GAAGA,KAAI,YAAY,EAAE,GAAG,MAAM,EAAE,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAtF;AACV,gBAAQ,gBAAgB,IAAI,EAAE;AAAA,MAChC;AACA,aAAO,UAAU,EAAE,QAAQ,EAAE,MAAM,OAAO;AAAA,IAC5C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS,MAAM;AACb,UAAM,SAAS,KAAK,OAAO;AAC3B,WAAO,YAAY,UAAU,KAAK,WAAW,IAAI;AACjD,WAAO;AAAA,EACT;AAAA,EACA,UAAU,wBAAC,YAAY;AACrB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT,GAHU;AAAA,EAIV,WAAW,wBAAC,YAAY;AACtB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT,GAHW;AAAA,EAIX,MAAM,MAAM,oBAAoB,SAAS;AACvC,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS;AACX,UAAI,OAAO,YAAY,YAAY;AACjC,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB,QAAQ;AACxB,YAAI,QAAQ,mBAAmB,OAAO;AACpC,2BAAiB,wBAAC,YAAY,SAAb;AAAA,QACnB,OAAO;AACL,2BAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,gBAAgB,CAAC,MAAM;AACxC,YAAM,WAAW,cAAc,CAAC;AAChC,aAAO,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAAA,IACvD,IAAI,CAAC,MAAM;AACT,UAAI,mBAAmB;AACvB,UAAI;AACF,2BAAmB,EAAE;AAAA,MACvB,QAAQ;AAAA,MACR;AACA,aAAO,CAAC,EAAE,KAAK,gBAAgB;AAAA,IACjC;AACA,wBAAoB,MAAM;AACxB,YAAM,aAAa,UAAU,KAAK,WAAW,IAAI;AACjD,YAAM,mBAAmB,eAAe,MAAM,IAAI,WAAW;AAC7D,aAAO,CAAC,YAAY;AAClB,cAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAI,WAAW,IAAI,SAAS,MAAM,gBAAgB,KAAK;AACvD,eAAO,IAAI,QAAQ,KAAK,OAAO;AAAA,MACjC;AAAA,IACF,GAAG;AACH,UAAM,UAAU,8BAAO,GAAG,SAAS;AACjC,YAAM,MAAM,MAAM,mBAAmB,eAAe,EAAE,IAAI,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;AAChF,UAAI,KAAK;AACP,eAAO;AAAA,MACT;AACA,YAAM,KAAK;AAAA,IACb,GANgB;AAOhB,SAAK,UAAU,iBAAiB,UAAU,MAAM,GAAG,GAAG,OAAO;AAC7D,WAAO;AAAA,EACT;AAAA,EACA,UAAU,QAAQ,MAAM,SAAS;AAC/B,aAAS,OAAO,YAAY;AAC5B,WAAO,UAAU,KAAK,WAAW,IAAI;AACrC,UAAM,IAAI,EAAE,UAAU,KAAK,WAAW,MAAM,QAAQ,QAAQ;AAC5D,SAAK,OAAO,IAAI,QAAQ,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1C,SAAK,OAAO,KAAK,CAAC;AAAA,EACpB;AAAA,EACA,aAAa,KAAK,GAAG;AACnB,QAAI,eAAe,OAAO;AACxB,aAAO,KAAK,aAAa,KAAK,CAAC;AAAA,IACjC;AACA,UAAM;AAAA,EACR;AAAA,EACA,UAAU,SAAS,cAAcC,MAAK,QAAQ;AAC5C,QAAI,WAAW,QAAQ;AACrB,cAAQ,YAAY,IAAI,SAAS,MAAM,MAAM,KAAK,UAAU,SAAS,cAAcA,MAAK,KAAK,CAAC,GAAG;AAAA,IACnG;AACA,UAAM,OAAO,KAAK,QAAQ,SAAS,EAAE,KAAAA,KAAI,CAAC;AAC1C,UAAM,cAAc,KAAK,OAAO,MAAM,QAAQ,IAAI;AAClD,UAAM,IAAI,IAAI,QAAQ,SAAS;AAAA,MAC7B;AAAA,MACA;AAAA,MACA,KAAAA;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK;AAAA,IACxB,CAAC;AACD,QAAI,YAAY,CAAC,EAAE,WAAW,GAAG;AAC/B,UAAI;AACJ,UAAI;AACF,cAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY;AAC3C,YAAE,MAAM,MAAM,KAAK,iBAAiB,CAAC;AAAA,QACvC,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AACA,aAAO,eAAe,UAAU,IAAI;AAAA,QAClC,CAAC,aAAa,aAAa,EAAE,YAAY,EAAE,MAAM,KAAK,iBAAiB,CAAC;AAAA,MAC1E,EAAE,MAAM,CAAC,QAAQ,KAAK,aAAa,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,iBAAiB,CAAC;AAAA,IAC9E;AACA,UAAM,WAAW,QAAQ,YAAY,CAAC,GAAG,KAAK,cAAc,KAAK,gBAAgB;AACjF,YAAQ,YAAY;AAClB,UAAI;AACF,cAAMC,WAAU,MAAM,SAAS,CAAC;AAChC,YAAI,CAACA,SAAQ,WAAW;AACtB,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,eAAOA,SAAQ;AAAA,MACjB,SAAS,KAAK;AACZ,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,QAAQ,wBAAC,YAAY,SAAS;AAC5B,WAAO,KAAK,UAAU,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,MAAM;AAAA,EACjE,GAFQ;AAAA,EAGR,UAAU,wBAAC,OAAO,aAAa,KAAK,iBAAiB;AACnD,QAAI,iBAAiB,SAAS;AAC5B,aAAO,KAAK,MAAM,cAAc,IAAI,QAAQ,OAAO,WAAW,IAAI,OAAO,KAAK,YAAY;AAAA,IAC5F;AACA,YAAQ,MAAM,SAAS;AACvB,WAAO,KAAK;AAAA,MACV,IAAI;AAAA,QACF,eAAe,KAAK,KAAK,IAAI,QAAQ,mBAAmB,UAAU,KAAK,KAAK,CAAC;AAAA,QAC7E;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAbU;AAAA,EAcV,OAAO,6BAAM;AACX,qBAAiB,SAAS,CAAC,UAAU;AACnC,YAAM,YAAY,KAAK,UAAU,MAAM,SAAS,OAAO,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAAA,IACtF,CAAC;AAAA,EACH,GAJO;AAKT;;;AU1OA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;AACA,IAAI,oBAAoB;AACxB,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,aAAa,OAAO;AACxB,IAAI,kBAAkB,IAAI,IAAI,aAAa;AAC3C,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO,EAAE,WAAW,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,EAC3C;AACA,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,6BAA6B,MAAM,2BAA2B;AACtE,WAAO;AAAA,EACT,WAAW,MAAM,6BAA6B,MAAM,2BAA2B;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,MAAM,mBAAmB;AAC3B,WAAO;AAAA,EACT,WAAW,MAAM,mBAAmB;AAClC,WAAO;AAAA,EACT;AACA,SAAO,EAAE,WAAW,EAAE,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE,SAAS,EAAE;AAC/D;AAlBS;AAmBT,IAAI,OAAO,MAAM;AAAA,EAzBjB,OAyBiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA,YAA4B,uBAAO,OAAO,IAAI;AAAA,EAC9C,OAAO,QAAQ,OAAO,UAAUC,UAAS,oBAAoB;AAC3D,QAAI,OAAO,WAAW,GAAG;AACvB,UAAI,KAAK,WAAW,QAAQ;AAC1B,cAAM;AAAA,MACR;AACA,UAAI,oBAAoB;AACtB;AAAA,MACF;AACA,WAAK,SAAS;AACd;AAAA,IACF;AACA,UAAM,CAAC,OAAO,GAAG,UAAU,IAAI;AAC/B,UAAM,UAAU,UAAU,MAAM,WAAW,WAAW,IAAI,CAAC,IAAI,IAAI,yBAAyB,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,UAAU,OAAO,CAAC,IAAI,IAAI,yBAAyB,IAAI,MAAM,MAAM,6BAA6B;AAC9N,QAAI;AACJ,QAAI,SAAS;AACX,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAI,YAAY,QAAQ,CAAC,KAAK;AAC9B,UAAI,QAAQ,QAAQ,CAAC,GAAG;AACtB,oBAAY,UAAU,QAAQ,0BAA0B,KAAK;AAC7D,YAAI,YAAY,KAAK,SAAS,GAAG;AAC/B,gBAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO,KAAK,UAAU,SAAS;AAC/B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,MAAM,6BAA6B,MAAM;AAAA,QAClD,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,SAAS,IAAI,IAAI,KAAK;AAC5C,YAAI,SAAS,IAAI;AACf,eAAK,YAAYA,SAAQ;AAAA,QAC3B;AAAA,MACF;AACA,UAAI,CAAC,sBAAsB,SAAS,IAAI;AACtC,iBAAS,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;AAAA,MACtC;AAAA,IACF,OAAO;AACL,aAAO,KAAK,UAAU,KAAK;AAC3B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,EAAE,SAAS,KAAK,MAAM,6BAA6B,MAAM;AAAA,QAClE,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,KAAK,IAAI,IAAI,KAAK;AAAA,MAC1C;AAAA,IACF;AACA,SAAK,OAAO,YAAY,OAAO,UAAUA,UAAS,kBAAkB;AAAA,EACtE;AAAA,EACA,iBAAiB;AACf,UAAM,YAAY,OAAO,KAAK,KAAK,SAAS,EAAE,KAAK,UAAU;AAC7D,UAAM,UAAU,UAAU,IAAI,CAAC,MAAM;AACnC,YAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,cAAQ,OAAO,EAAE,cAAc,WAAW,IAAI,CAAC,KAAK,EAAE,SAAS,KAAK,gBAAgB,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,EAAE,eAAe;AAAA,IAChI,CAAC;AACD,QAAI,OAAO,KAAK,WAAW,UAAU;AACnC,cAAQ,QAAQ,IAAI,KAAK,MAAM,EAAE;AAAA,IACnC;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO,QAAQ,CAAC;AAAA,IAClB;AACA,WAAO,QAAQ,QAAQ,KAAK,GAAG,IAAI;AAAA,EACrC;AACF;;;ACvGA;AAAA;AAAA;AAAAC;AAEA,IAAI,OAAO,MAAM;AAAA,EAFjB,OAEiB;AAAA;AAAA;AAAA,EACf,WAAW,EAAE,UAAU,EAAE;AAAA,EACzB,QAAQ,IAAI,KAAK;AAAA,EACjB,OAAO,MAAM,OAAO,oBAAoB;AACtC,UAAM,aAAa,CAAC;AACpB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,OAAO;AAClB,UAAI,WAAW;AACf,aAAO,KAAK,QAAQ,cAAc,CAAC,MAAM;AACvC,cAAM,OAAO,MAAM,CAAC;AACpB,eAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACpB;AACA,mBAAW;AACX,eAAO;AAAA,MACT,CAAC;AACD,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,KAAK,MAAM,0BAA0B,KAAK,CAAC;AAC1D,aAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,eAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI;AAClC,iBAAO,CAAC,IAAI,OAAO,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,OAAO,YAAY,KAAK,UAAU,kBAAkB;AAC9E,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,QAAI,SAAS,KAAK,MAAM,eAAe;AACvC,QAAI,WAAW,IAAI;AACjB,aAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAAA,IACtB;AACA,QAAI,eAAe;AACnB,UAAM,sBAAsB,CAAC;AAC7B,UAAM,sBAAsB,CAAC;AAC7B,aAAS,OAAO,QAAQ,yBAAyB,CAAC,GAAG,cAAc,eAAe;AAChF,UAAI,iBAAiB,QAAQ;AAC3B,4BAAoB,EAAE,YAAY,IAAI,OAAO,YAAY;AACzD,eAAO;AAAA,MACT;AACA,UAAI,eAAe,QAAQ;AACzB,4BAAoB,OAAO,UAAU,CAAC,IAAI,EAAE;AAC5C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AACD,WAAO,CAAC,IAAI,OAAO,IAAI,MAAM,EAAE,GAAG,qBAAqB,mBAAmB;AAAA,EAC5E;AACF;;;AF9CA,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC;AAChE,IAAI,sBAAsC,uBAAO,OAAO,IAAI;AAC5D,SAAS,oBAAoB,MAAM;AACjC,SAAO,oBAAoB,IAAI,MAAM,IAAI;AAAA,IACvC,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,MAC3B;AAAA,MACA,CAAC,GAAG,aAAa,WAAW,KAAK,QAAQ,KAAK;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AAPS;AAQT,SAAS,2BAA2B;AAClC,wBAAsC,uBAAO,OAAO,IAAI;AAC1D;AAFS;AAGT,SAAS,mCAAmC,QAAQ;AAClD,QAAM,OAAO,IAAI,KAAK;AACtB,QAAM,cAAc,CAAC;AACrB,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;AAAA,EACT;AACA,QAAM,2BAA2B,OAAO;AAAA,IACtC,CAAC,UAAU,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,GAAG,GAAG,KAAK;AAAA,EAChD,EAAE;AAAA,IACA,CAAC,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW,KAAK,MAAM,YAAY,IAAI,YAAY,KAAK,MAAM,SAAS,MAAM;AAAA,EACpG;AACA,QAAM,YAA4B,uBAAO,OAAO,IAAI;AACpD,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,yBAAyB,QAAQ,IAAI,KAAK,KAAK;AAC3E,UAAM,CAAC,oBAAoB,MAAM,QAAQ,IAAI,yBAAyB,CAAC;AACvE,QAAI,oBAAoB;AACtB,gBAAU,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC,CAAC,GAAG,UAAU;AAAA,IAChG,OAAO;AACL;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,mBAAa,KAAK,OAAO,MAAM,GAAG,kBAAkB;AAAA,IACtD,SAAS,GAAG;AACV,YAAM,MAAM,aAAa,IAAI,qBAAqB,IAAI,IAAI;AAAA,IAC5D;AACA,QAAI,oBAAoB;AACtB;AAAA,IACF;AACA,gBAAY,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,GAAG,UAAU,MAAM;AACjD,YAAM,gBAAgC,uBAAO,OAAO,IAAI;AACxD,oBAAc;AACd,aAAO,cAAc,GAAG,cAAc;AACpC,cAAM,CAAC,KAAK,KAAK,IAAI,WAAW,UAAU;AAC1C,sBAAc,GAAG,IAAI;AAAA,MACvB;AACA,aAAO,CAAC,GAAG,aAAa;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,QAAM,CAAC,QAAQ,qBAAqB,mBAAmB,IAAI,KAAK,YAAY;AAC5E,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,aAAS,IAAI,GAAG,OAAO,YAAY,CAAC,EAAE,QAAQ,IAAI,MAAM,KAAK;AAC3D,YAAM,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;AACjC,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,YAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,eAAS,IAAI,GAAG,OAAO,KAAK,QAAQ,IAAI,MAAM,KAAK;AACjD,YAAI,KAAK,CAAC,CAAC,IAAI,oBAAoB,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,CAAC;AACpB,aAAW,KAAK,qBAAqB;AACnC,eAAW,CAAC,IAAI,YAAY,oBAAoB,CAAC,CAAC;AAAA,EACpD;AACA,SAAO,CAAC,QAAQ,YAAY,SAAS;AACvC;AAxDS;AAyDT,SAAS,eAAe,YAAY,MAAM;AACxC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,aAAW,KAAK,OAAO,KAAK,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG;AAC3E,QAAI,oBAAoB,CAAC,EAAE,KAAK,IAAI,GAAG;AACrC,aAAO,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAVS;AAWT,IAAI,eAAe,MAAM;AAAA,EA3FzB,OA2FyB;AAAA;AAAA;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAC5E,SAAK,UAAU,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAAA,EAC1E;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,cAAc,CAAC,QAAQ;AAC1B,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,QAAI,CAAC,WAAW,MAAM,GAAG;AACvB;AACA,OAAC,YAAY,MAAM,EAAE,QAAQ,CAAC,eAAe;AAC3C,mBAAW,MAAM,IAAoB,uBAAO,OAAO,IAAI;AACvD,eAAO,KAAK,WAAW,eAAe,CAAC,EAAE,QAAQ,CAAC,MAAM;AACtD,qBAAW,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,WAAW,eAAe,EAAE,CAAC,CAAC;AAAA,QAC5D,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,MAAM,MAAM,KAAK,CAAC,GAAG;AAC9C,QAAI,MAAM,KAAK,IAAI,GAAG;AACpB,YAAM,KAAK,oBAAoB,IAAI;AACnC,UAAI,WAAW,iBAAiB;AAC9B,eAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,qBAAW,CAAC,EAAE,IAAI,MAAM,eAAe,WAAW,CAAC,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,QACvH,CAAC;AAAA,MACH,OAAO;AACL,mBAAW,MAAM,EAAE,IAAI,MAAM,eAAe,WAAW,MAAM,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,MACjI;AACA,aAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM;AACxC,eAAG,KAAK,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC3D,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,OAAO,CAAC,CAAC,EAAE;AAAA,YACrB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC9D;AAAA,QACF;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,UAAM,QAAQ,uBAAuB,IAAI,KAAK,CAAC,IAAI;AACnD,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,QAAQ,MAAM,CAAC;AACrB,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,CAAC,EAAE,KAAK,MAAM;AAAA,YACnB,GAAG,eAAe,WAAW,CAAC,GAAG,KAAK,KAAK,eAAe,WAAW,eAAe,GAAG,KAAK,KAAK,CAAC;AAAA,UACpG;AACA,iBAAO,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,aAAa,MAAM,IAAI,CAAC,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,6BAAyB;AACzB,UAAM,WAAW,KAAK,kBAAkB;AACxC,SAAK,QAAQ,CAAC,SAAS,UAAU;AAC/B,YAAM,UAAU,SAAS,OAAO,KAAK,SAAS,eAAe;AAC7D,YAAM,cAAc,QAAQ,CAAC,EAAE,KAAK;AACpC,UAAI,aAAa;AACf,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,MAAM,MAAM,QAAQ,CAAC,CAAC;AACpC,UAAI,CAAC,OAAO;AACV,eAAO,CAAC,CAAC,GAAG,UAAU;AAAA,MACxB;AACA,YAAM,QAAQ,MAAM,QAAQ,IAAI,CAAC;AACjC,aAAO,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,KAAK;AAAA,IAClC;AACA,WAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,EAChC;AAAA,EACA,oBAAoB;AAClB,UAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAO,KAAK,KAAK,OAAO,EAAE,OAAO,OAAO,KAAK,KAAK,WAAW,CAAC,EAAE,QAAQ,CAAC,WAAW;AAClF,eAAS,MAAM,MAAM,KAAK,cAAc,MAAM;AAAA,IAChD,CAAC;AACD,SAAK,cAAc,KAAK,UAAU;AAClC,WAAO;AAAA,EACT;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,SAAS,CAAC;AAChB,QAAI,cAAc,WAAW;AAC7B,KAAC,KAAK,aAAa,KAAK,OAAO,EAAE,QAAQ,CAAC,MAAM;AAC9C,YAAM,WAAW,EAAE,MAAM,IAAI,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;AAC9F,UAAI,SAAS,WAAW,GAAG;AACzB,wBAAgB;AAChB,eAAO,KAAK,GAAG,QAAQ;AAAA,MACzB,WAAW,WAAW,iBAAiB;AACrC,eAAO;AAAA,UACL,GAAG,OAAO,KAAK,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;AAAA,QACnF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,mCAAmC,MAAM;AAAA,IAClD;AAAA,EACF;AACF;;;AG1MA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;AAEA,IAAI,cAAc,MAAM;AAAA,EAFxB,OAEwB;AAAA;AAAA;AAAA,EACtB,OAAO;AAAA,EACP,WAAW,CAAC;AAAA,EACZ,UAAU,CAAC;AAAA,EACX,YAAY,MAAM;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,SAAK,QAAQ,KAAK,CAAC,QAAQ,MAAM,OAAO,CAAC;AAAA,EAC3C;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI;AACR,QAAI;AACJ,WAAO,IAAI,KAAK,KAAK;AACnB,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI;AACF,iBAAS,KAAK,GAAG,OAAO,OAAO,QAAQ,KAAK,MAAM,MAAM;AACtD,iBAAO,IAAI,GAAG,OAAO,EAAE,CAAC;AAAA,QAC1B;AACA,cAAM,OAAO,MAAM,QAAQ,IAAI;AAAA,MACjC,SAAS,GAAG;AACV,YAAI,aAAa,sBAAsB;AACrC;AAAA,QACF;AACA,cAAM;AAAA,MACR;AACA,WAAK,QAAQ,OAAO,MAAM,KAAK,MAAM;AACrC,WAAK,WAAW,CAAC,MAAM;AACvB,WAAK,UAAU;AACf;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,SAAK,OAAO,iBAAiB,KAAK,aAAa,IAAI;AACnD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,WAAW,KAAK,SAAS,WAAW,GAAG;AAC9C,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,WAAO,KAAK,SAAS,CAAC;AAAA,EACxB;AACF;;;ACtDA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;AAGA,IAAI,cAA8B,uBAAO,OAAO,IAAI;AACpD,IAAIC,QAAO,MAAM;AAAA,EAJjB,OAIiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY,QAAQ,SAAS,UAAU;AACrC,SAAK,YAAY,YAA4B,uBAAO,OAAO,IAAI;AAC/D,SAAK,WAAW,CAAC;AACjB,QAAI,UAAU,SAAS;AACrB,YAAM,IAAoB,uBAAO,OAAO,IAAI;AAC5C,QAAE,MAAM,IAAI,EAAE,SAAS,cAAc,CAAC,GAAG,OAAO,EAAE;AAClD,WAAK,WAAW,CAAC,CAAC;AAAA,IACpB;AACA,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,OAAO,QAAQ,MAAM,SAAS;AAC5B,SAAK,SAAS,EAAE,KAAK;AACrB,QAAI,UAAU;AACd,UAAM,QAAQ,iBAAiB,IAAI;AACnC,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,QAAQ,MAAM,IAAI,CAAC;AACzB,YAAM,UAAU,WAAW,GAAG,KAAK;AACnC,YAAM,MAAM,MAAM,QAAQ,OAAO,IAAI,QAAQ,CAAC,IAAI;AAClD,UAAI,OAAO,QAAQ,WAAW;AAC5B,kBAAU,QAAQ,UAAU,GAAG;AAC/B,YAAI,SAAS;AACX,uBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC9B;AACA;AAAA,MACF;AACA,cAAQ,UAAU,GAAG,IAAI,IAAIA,MAAK;AAClC,UAAI,SAAS;AACX,gBAAQ,UAAU,KAAK,OAAO;AAC9B,qBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC9B;AACA,gBAAU,QAAQ,UAAU,GAAG;AAAA,IACjC;AACA,YAAQ,SAAS,KAAK;AAAA,MACpB,CAAC,MAAM,GAAG;AAAA,QACR;AAAA,QACA,cAAc,aAAa,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AAAA,QACjE,OAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM,QAAQ,YAAY,QAAQ;AAChD,UAAM,cAAc,CAAC;AACrB,aAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,YAAM,aAAa,EAAE,MAAM,KAAK,EAAE,eAAe;AACjD,YAAM,eAAe,CAAC;AACtB,UAAI,eAAe,QAAQ;AACzB,mBAAW,SAAyB,uBAAO,OAAO,IAAI;AACtD,oBAAY,KAAK,UAAU;AAC3B,YAAI,eAAe,eAAe,UAAU,WAAW,aAAa;AAClE,mBAAS,KAAK,GAAG,OAAO,WAAW,aAAa,QAAQ,KAAK,MAAM,MAAM;AACvE,kBAAM,MAAM,WAAW,aAAa,EAAE;AACtC,kBAAM,YAAY,aAAa,WAAW,KAAK;AAC/C,uBAAW,OAAO,GAAG,IAAI,SAAS,GAAG,KAAK,CAAC,YAAY,OAAO,GAAG,IAAI,WAAW,GAAG,KAAK,SAAS,GAAG;AACpG,yBAAa,WAAW,KAAK,IAAI;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,MAAM;AACnB,UAAM,cAAc,CAAC;AACrB,SAAK,UAAU;AACf,UAAM,UAAU;AAChB,QAAI,WAAW,CAAC,OAAO;AACvB,UAAM,QAAQ,UAAU,IAAI;AAC5B,UAAM,gBAAgB,CAAC;AACvB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,SAAS,MAAM,MAAM;AAC3B,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,OAAO,SAAS,QAAQ,IAAI,MAAM,KAAK;AACrD,cAAM,OAAO,SAAS,CAAC;AACvB,cAAM,WAAW,KAAK,UAAU,IAAI;AACpC,YAAI,UAAU;AACZ,mBAAS,UAAU,KAAK;AACxB,cAAI,QAAQ;AACV,gBAAI,SAAS,UAAU,GAAG,GAAG;AAC3B,0BAAY;AAAA,gBACV,GAAG,KAAK,gBAAgB,SAAS,UAAU,GAAG,GAAG,QAAQ,KAAK,OAAO;AAAA,cACvE;AAAA,YACF;AACA,wBAAY,KAAK,GAAG,KAAK,gBAAgB,UAAU,QAAQ,KAAK,OAAO,CAAC;AAAA,UAC1E,OAAO;AACL,sBAAU,KAAK,QAAQ;AAAA,UACzB;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,OAAO,KAAK,UAAU,QAAQ,IAAI,MAAM,KAAK;AAC3D,gBAAM,UAAU,KAAK,UAAU,CAAC;AAChC,gBAAM,SAAS,KAAK,YAAY,cAAc,CAAC,IAAI,EAAE,GAAG,KAAK,QAAQ;AACrE,cAAI,YAAY,KAAK;AACnB,kBAAM,UAAU,KAAK,UAAU,GAAG;AAClC,gBAAI,SAAS;AACX,0BAAY,KAAK,GAAG,KAAK,gBAAgB,SAAS,QAAQ,KAAK,OAAO,CAAC;AACvE,sBAAQ,UAAU;AAClB,wBAAU,KAAK,OAAO;AAAA,YACxB;AACA;AAAA,UACF;AACA,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AACA,gBAAM,CAAC,KAAK,MAAM,OAAO,IAAI;AAC7B,gBAAM,QAAQ,KAAK,UAAU,GAAG;AAChC,gBAAM,iBAAiB,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAC9C,cAAI,mBAAmB,QAAQ;AAC7B,kBAAM,IAAI,QAAQ,KAAK,cAAc;AACrC,gBAAI,GAAG;AACL,qBAAO,IAAI,IAAI,EAAE,CAAC;AAClB,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,KAAK,SAAS,MAAM,CAAC;AAC7E,kBAAI,OAAO,KAAK,MAAM,SAAS,EAAE,QAAQ;AACvC,sBAAM,UAAU;AAChB,sBAAM,iBAAiB,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,UAAU;AACnD,sBAAM,iBAAiB,cAAc,cAAc,MAAM,CAAC;AAC1D,+BAAe,KAAK,KAAK;AAAA,cAC3B;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,YAAY,QAAQ,QAAQ,KAAK,IAAI,GAAG;AAC1C,mBAAO,IAAI,IAAI;AACf,gBAAI,QAAQ;AACV,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,QAAQ,KAAK,OAAO,CAAC;AAC7E,kBAAI,MAAM,UAAU,GAAG,GAAG;AACxB,4BAAY;AAAA,kBACV,GAAG,KAAK,gBAAgB,MAAM,UAAU,GAAG,GAAG,QAAQ,QAAQ,KAAK,OAAO;AAAA,gBAC5E;AAAA,cACF;AAAA,YACF,OAAO;AACL,oBAAM,UAAU;AAChB,wBAAU,KAAK,KAAK;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,iBAAW,UAAU,OAAO,cAAc,MAAM,KAAK,CAAC,CAAC;AAAA,IACzD;AACA,QAAI,YAAY,SAAS,GAAG;AAC1B,kBAAY,KAAK,CAAC,GAAG,MAAM;AACzB,eAAO,EAAE,QAAQ,EAAE;AAAA,MACrB,CAAC;AAAA,IACH;AACA,WAAO,CAAC,YAAY,IAAI,CAAC,EAAE,SAAS,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC;AAAA,EACrE;AACF;;;AD3JA,IAAI,aAAa,MAAM;AAAA,EAHvB,OAGuB;AAAA;AAAA;AAAA,EACrB,OAAO;AAAA,EACP;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,IAAIC,MAAK;AAAA,EACxB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,UAAU,uBAAuB,IAAI;AAC3C,QAAI,SAAS;AACX,eAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,aAAK,MAAM,OAAO,QAAQ,QAAQ,CAAC,GAAG,OAAO;AAAA,MAC/C;AACA;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,MAAM,OAAO;AAAA,EACzC;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,WAAO,KAAK,MAAM,OAAO,QAAQ,IAAI;AAAA,EACvC;AACF;;;AlBjBA,IAAIC,QAAO,cAAc,KAAS;AAAA,EALlC,OAKkC;AAAA;AAAA;AAAA,EAChC,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,OAAO;AACb,SAAK,SAAS,QAAQ,UAAU,IAAI,YAAY;AAAA,MAC9C,SAAS,CAAC,IAAI,aAAa,GAAG,IAAI,WAAW,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;AoBZA;AAAA;AAAA;AAAAC;AACA,IAAI,OAAO,wBAAC,YAAY;AACtB,QAAM,WAAW;AAAA,IACf,QAAQ;AAAA,IACR,cAAc,CAAC,OAAO,QAAQ,OAAO,QAAQ,UAAU,OAAO;AAAA,IAC9D,cAAc,CAAC;AAAA,IACf,eAAe,CAAC;AAAA,EAClB;AACA,QAAM,OAAO;AAAA,IACX,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,mBAAmB,CAAC,eAAe;AACvC,QAAI,OAAO,eAAe,UAAU;AAClC,UAAI,eAAe,KAAK;AACtB,eAAO,MAAM;AAAA,MACf,OAAO;AACL,eAAO,CAAC,WAAW,eAAe,SAAS,SAAS;AAAA,MACtD;AAAA,IACF,WAAW,OAAO,eAAe,YAAY;AAC3C,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC,WAAW,WAAW,SAAS,MAAM,IAAI,SAAS;AAAA,IAC5D;AAAA,EACF,GAAG,KAAK,MAAM;AACd,QAAM,oBAAoB,CAAC,qBAAqB;AAC9C,QAAI,OAAO,qBAAqB,YAAY;AAC1C,aAAO;AAAA,IACT,WAAW,MAAM,QAAQ,gBAAgB,GAAG;AAC1C,aAAO,MAAM;AAAA,IACf,OAAO;AACL,aAAO,MAAM,CAAC;AAAA,IAChB;AAAA,EACF,GAAG,KAAK,YAAY;AACpB,SAAO,sCAAe,MAAM,GAAG,MAAM;AACnC,aAAS,IAAI,KAAK,OAAO;AACvB,QAAE,IAAI,QAAQ,IAAI,KAAK,KAAK;AAAA,IAC9B;AAFS;AAGT,UAAM,cAAc,gBAAgB,EAAE,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC;AACnE,QAAI,aAAa;AACf,UAAI,+BAA+B,WAAW;AAAA,IAChD;AACA,QAAI,KAAK,WAAW,KAAK;AACvB,YAAM,eAAe,EAAE,IAAI,OAAO,MAAM;AACxC,UAAI,cAAc;AAChB,YAAI,QAAQ,YAAY;AAAA,MAC1B,OAAO;AACL,YAAI,QAAQ,QAAQ;AAAA,MACtB;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,UAAI,oCAAoC,MAAM;AAAA,IAChD;AACA,QAAI,KAAK,eAAe,QAAQ;AAC9B,UAAI,iCAAiC,KAAK,cAAc,KAAK,GAAG,CAAC;AAAA,IACnE;AACA,QAAI,EAAE,IAAI,WAAW,WAAW;AAC9B,UAAI,KAAK,UAAU,MAAM;AACvB,YAAI,0BAA0B,KAAK,OAAO,SAAS,CAAC;AAAA,MACtD;AACA,YAAM,eAAe,iBAAiB,EAAE,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC;AACrE,UAAI,aAAa,QAAQ;AACvB,YAAI,gCAAgC,aAAa,KAAK,GAAG,CAAC;AAAA,MAC5D;AACA,UAAI,UAAU,KAAK;AACnB,UAAI,CAAC,SAAS,QAAQ;AACpB,cAAM,iBAAiB,EAAE,IAAI,OAAO,gCAAgC;AACpE,YAAI,gBAAgB;AAClB,oBAAU,eAAe,MAAM,SAAS;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,SAAS,QAAQ;AACnB,YAAI,gCAAgC,QAAQ,KAAK,GAAG,CAAC;AACrD,UAAE,IAAI,QAAQ,OAAO,QAAQ,gCAAgC;AAAA,MAC/D;AACA,QAAE,IAAI,QAAQ,OAAO,gBAAgB;AACrC,QAAE,IAAI,QAAQ,OAAO,cAAc;AACnC,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS,EAAE,IAAI;AAAA,QACf,QAAQ;AAAA,QACR,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AACA,UAAM,KAAK;AAAA,EACb,GAlDO;AAmDT,GApFW;;;ACDX;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;AACA,IAAI,kBAAkB,wBAAC,QAAQ;AAC7B,SAAO,aAAa,IAAI,QAAQ,QAAQ,CAAC,OAAO,EAAE,GAAG,KAAK,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;AAChF,GAFsB;AAGtB,IAAI,kBAAkB,wBAAC,QAAQ,aAAa,GAAG,EAAE,QAAQ,UAAU,CAAC,OAAO,EAAE,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,GAApF;AACtB,IAAI,eAAe,wBAAC,QAAQ;AAC1B,MAAI,SAAS;AACb,QAAM,QAAQ,IAAI,WAAW,GAAG;AAChC,WAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAU,OAAO,aAAa,MAAM,CAAC,CAAC;AAAA,EACxC;AACA,SAAO,KAAK,MAAM;AACpB,GAPmB;AAQnB,IAAI,eAAe,wBAAC,QAAQ;AAC1B,QAAM,SAAS,KAAK,GAAG;AACvB,QAAM,QAAQ,IAAI,WAAW,IAAI,YAAY,OAAO,MAAM,CAAC;AAC3D,QAAM,OAAO,OAAO,SAAS;AAC7B,WAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK,MAAM,KAAK,KAAK;AAC1D,UAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAC9B,UAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,EAChC;AACA,SAAO;AACT,GATmB;;;ACbnB;AAAA;AAAA;AAAAC;AACA,IAAI,iBAAkC,kBAAC,oBAAoB;AACzD,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,kBAAgB,OAAO,IAAI;AAC3B,SAAO;AACT,GAAG,kBAAkB,CAAC,CAAC;;;AChBvB;AAAA;AAAA;AAAAC;;;ACAA;AAAA;AAAA;AAAAC;AAkBA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AACR;AACA,IAAI,gBAAgB,6BAAM;AACxB,QAAM,SAAS;AACf,QAAM,qBAAqB,OAAO,cAAc,eAAe;AAC/D,MAAI,oBAAoB;AACtB,eAAW,CAAC,YAAY,SAAS,KAAK,OAAO,QAAQ,eAAe,GAAG;AACrE,UAAI,qBAAqB,SAAS,GAAG;AACnC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,gBAAgB,UAAU;AAC3C,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,WAAW,QAAQ;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,SAAS,SAAS,SAAS,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,SAAO;AACT,GApBoB;AAqBpB,IAAI,uBAAuB,wBAACC,cAAa;AACvC,QAAM,YAAY;AAClB,SAAO,UAAU,WAAWA,SAAQ;AACtC,GAH2B;;;AC7C3B;AAAA;AAAA;AAAAC;AACA,IAAI,6BAA6B,cAAc,MAAM;AAAA,EADrD,OACqD;AAAA;AAAA;AAAA,EACnD,YAAY,KAAK;AACf,UAAM,GAAG,GAAG,kCAAkC;AAC9C,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,kBAAkB,cAAc,MAAM;AAAA,EAP1C,OAO0C;AAAA;AAAA;AAAA,EACxC,YAAY,OAAO;AACjB,UAAM,sBAAsB,KAAK,EAAE;AACnC,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,oBAAoB,cAAc,MAAM;AAAA,EAb5C,OAa4C;AAAA;AAAA;AAAA,EAC1C,YAAY,OAAO;AACjB,UAAM,UAAU,KAAK,mCAAmC;AACxD,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,kBAAkB,cAAc,MAAM;AAAA,EAnB1C,OAmB0C;AAAA;AAAA;AAAA,EACxC,YAAY,OAAO;AACjB,UAAM,UAAU,KAAK,WAAW;AAChC,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,mBAAmB,cAAc,MAAM;AAAA,EAzB3C,OAyB2C;AAAA;AAAA;AAAA,EACzC,YAAY,kBAAkB,KAAK;AACjC,UAAM,+CAA+C,gBAAgB,YAAY,GAAG,IAAI;AACxF,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,mBAAmB,cAAc,MAAM;AAAA,EA/B3C,OA+B2C;AAAA;AAAA;AAAA,EACzC,YAAY,QAAQ;AAClB,UAAM,0BAA0B,KAAK,UAAU,MAAM,CAAC,EAAE;AACxD,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,uBAAuB,cAAc,MAAM;AAAA,EArC/C,OAqC+C;AAAA;AAAA;AAAA,EAC7C,YAAY,QAAQ;AAClB,UAAM,iCAAiC,KAAK,UAAU,MAAM,CAAC,EAAE;AAC/D,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,8BAA8B,cAAc,MAAM;AAAA,EA3CtD,OA2CsD;AAAA;AAAA;AAAA,EACpD,YAAY,OAAO;AACjB,UAAM,SAAS,KAAK,wBAAwB;AAC5C,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,iBAAkC,kBAAC,oBAAoB;AACzD,kBAAgB,SAAS,IAAI;AAC7B,kBAAgB,SAAS,IAAI;AAC7B,kBAAgB,MAAM,IAAI;AAC1B,kBAAgB,QAAQ,IAAI;AAC5B,kBAAgB,WAAW,IAAI;AAC/B,kBAAgB,YAAY,IAAI;AAChC,kBAAgB,SAAS,IAAI;AAC7B,kBAAgB,WAAW,IAAI;AAC/B,SAAO;AACT,GAAG,kBAAkB,CAAC,CAAC;;;AC3DvB;AAAA;AAAA;AAAAC;AACA,IAAI,cAAc,IAAI,YAAY;AAClC,IAAI,cAAc,IAAI,YAAY;;;AHGlC,eAAe,QAAQ,YAAY,KAAK,MAAM;AAC5C,QAAM,YAAY,gBAAgB,GAAG;AACrC,QAAM,YAAY,MAAM,iBAAiB,YAAY,SAAS;AAC9D,SAAO,MAAM,OAAO,OAAO,KAAK,WAAW,WAAW,IAAI;AAC5D;AAJe;AAKf,eAAe,UAAU,WAAW,KAAK,WAAW,MAAM;AACxD,QAAM,YAAY,gBAAgB,GAAG;AACrC,QAAM,YAAY,MAAM,gBAAgB,WAAW,SAAS;AAC5D,SAAO,MAAM,OAAO,OAAO,OAAO,WAAW,WAAW,WAAW,IAAI;AACzE;AAJe;AAKf,SAAS,YAAY,KAAK;AACxB,SAAO,aAAa,IAAI,QAAQ,oBAAoB,EAAE,EAAE,QAAQ,OAAO,EAAE,CAAC;AAC5E;AAFS;AAGT,eAAe,iBAAiB,KAAK,KAAK;AACxC,MAAI,CAAC,OAAO,UAAU,CAAC,OAAO,OAAO,WAAW;AAC9C,UAAM,IAAI,MAAM,0EAA0E;AAAA,EAC5F;AACA,MAAI,YAAY,GAAG,GAAG;AACpB,QAAI,IAAI,SAAS,aAAa,IAAI,SAAS,UAAU;AACnD,YAAM,IAAI;AAAA,QACR,0CAA0C,IAAI,IAAI;AAAA,MACpD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC,eAAe,IAAI;AACnC,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,MAAM,OAAO,OAAO,UAAU,OAAO,KAAK,KAAK,OAAO,MAAM;AAAA,EACrE;AACA,MAAI,IAAI,SAAS,SAAS,GAAG;AAC3B,WAAO,MAAM,OAAO,OAAO,UAAU,SAAS,YAAY,GAAG,GAAG,KAAK,OAAO,MAAM;AAAA,EACpF;AACA,SAAO,MAAM,OAAO,OAAO,UAAU,OAAO,YAAY,OAAO,GAAG,GAAG,KAAK,OAAO,MAAM;AACzF;AApBe;AAqBf,eAAe,gBAAgB,KAAK,KAAK;AACvC,MAAI,CAAC,OAAO,UAAU,CAAC,OAAO,OAAO,WAAW;AAC9C,UAAM,IAAI,MAAM,0EAA0E;AAAA,EAC5F;AACA,MAAI,YAAY,GAAG,GAAG;AACpB,QAAI,IAAI,SAAS,YAAY,IAAI,SAAS,UAAU;AAClD,aAAO;AAAA,IACT;AACA,UAAM,MAAM,oBAAoB,GAAG;AAAA,EACrC;AACA,MAAI,OAAO,QAAQ,YAAY,IAAI,SAAS,SAAS,GAAG;AACtD,UAAM,aAAa,MAAM,OAAO,OAAO,UAAU,SAAS,YAAY,GAAG,GAAG,KAAK,MAAM;AAAA,MACrF,eAAe;AAAA,IACjB,CAAC;AACD,UAAM,MAAM,oBAAoB,UAAU;AAAA,EAC5C;AACA,QAAM,SAAS,CAAC,eAAe,MAAM;AACrC,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,MAAM,OAAO,OAAO,UAAU,OAAO,KAAK,KAAK,OAAO,MAAM;AAAA,EACrE;AACA,MAAI,IAAI,SAAS,QAAQ,GAAG;AAC1B,WAAO,MAAM,OAAO,OAAO,UAAU,QAAQ,YAAY,GAAG,GAAG,KAAK,OAAO,MAAM;AAAA,EACnF;AACA,SAAO,MAAM,OAAO,OAAO,UAAU,OAAO,YAAY,OAAO,GAAG,GAAG,KAAK,OAAO,MAAM;AACzF;AAxBe;AAyBf,eAAe,oBAAoB,YAAY;AAC7C,MAAI,WAAW,SAAS,WAAW;AACjC,UAAM,IAAI,MAAM,wBAAwB,WAAW,IAAI,EAAE;AAAA,EAC3D;AACA,MAAI,CAAC,WAAW,aAAa;AAC3B,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AACA,QAAM,MAAM,MAAM,OAAO,OAAO,UAAU,OAAO,UAAU;AAC3D,QAAM,EAAE,IAAI,IAAI;AAChB,QAAM,EAAE,KAAK,GAAG,EAAE,IAAI;AACtB,QAAM,EAAE,KAAK,GAAG,EAAE,IAAI;AACtB,SAAO,EAAE,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,SAAS,CAAC,eAAe,MAAM,EAAE;AACvE;AAZe;AAaf,SAAS,gBAAgB,MAAM;AAC7B,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,YAAY;AAAA,MACd;AAAA,IACF;AACE,YAAM,IAAI,2BAA2B,IAAI;AAAA,EAC7C;AACF;AApGS;AAqGT,SAAS,YAAY,KAAK;AACxB,QAAM,UAAU,cAAc;AAC9B,MAAI,YAAY,UAAU,CAAC,CAAC,OAAO,WAAW;AAC5C,WAAO,eAAe,OAAO,UAAU;AAAA,EACzC;AACA,SAAO,eAAe;AACxB;AANS;;;AHpKT,IAAI,gBAAgB,wBAAC,SAAS,gBAAgB,YAAY,OAAO,KAAK,UAAU,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,MAAM,EAAE,GAA3F;AACpB,IAAI,sBAAsB,wBAAC,QAAQ,gBAAgB,GAAG,EAAE,QAAQ,MAAM,EAAE,GAA9C;AAC1B,IAAI,gBAAgB,wBAAC,SAAS,KAAK,MAAM,YAAY,OAAO,gBAAgB,IAAI,CAAC,CAAC,GAA9D;AACpB,SAAS,cAAc,KAAK;AAC1B,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,UAAM,aAAa;AACnB,WAAO,SAAS,cAAc,OAAO,OAAO,cAAc,EAAE,SAAS,WAAW,GAAG,MAAM,EAAE,SAAS,eAAe,WAAW,QAAQ;AAAA,EACxI;AACA,SAAO;AACT;AANS;AAOT,IAAI,OAAO,8BAAO,SAAS,YAAY,MAAM,YAAY;AACvD,QAAM,iBAAiB,cAAc,OAAO;AAC5C,MAAI;AACJ,MAAI,OAAO,eAAe,YAAY,SAAS,YAAY;AACzD,UAAM,WAAW;AACjB,oBAAgB,cAAc,EAAE,KAAK,KAAK,OAAO,KAAK,WAAW,IAAI,CAAC;AAAA,EACxE,OAAO;AACL,oBAAgB,cAAc,EAAE,KAAK,KAAK,MAAM,CAAC;AAAA,EACnD;AACA,QAAM,eAAe,GAAG,aAAa,IAAI,cAAc;AACvD,QAAM,gBAAgB,MAAM,QAAQ,YAAY,KAAK,YAAY,OAAO,YAAY,CAAC;AACrF,QAAM,YAAY,oBAAoB,aAAa;AACnD,SAAO,GAAG,YAAY,IAAI,SAAS;AACrC,GAbW;AAcX,IAAI,SAAS,8BAAO,OAAO,WAAW,MAAM,YAAY;AACtD,QAAM,aAAa,MAAM,MAAM,GAAG;AAClC,MAAI,WAAW,WAAW,GAAG;AAC3B,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACA,QAAM,EAAE,QAAQ,QAAQ,IAAI,OAAO,KAAK;AACxC,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,UAAM,IAAI,iBAAiB,MAAM;AAAA,EACnC;AACA,QAAM,MAAM,KAAK,IAAI,IAAI,MAAM;AAC/B,MAAI,QAAQ,OAAO,QAAQ,MAAM,KAAK;AACpC,UAAM,IAAI,kBAAkB,KAAK;AAAA,EACnC;AACA,MAAI,QAAQ,OAAO,QAAQ,OAAO,KAAK;AACrC,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACA,MAAI,QAAQ,OAAO,MAAM,QAAQ,KAAK;AACpC,UAAM,IAAI,iBAAiB,KAAK,QAAQ,GAAG;AAAA,EAC7C;AACA,QAAM,gBAAgB,MAAM,UAAU,GAAG,MAAM,YAAY,GAAG,CAAC;AAC/D,QAAM,WAAW,MAAM;AAAA,IACrB;AAAA,IACA;AAAA,IACA,gBAAgB,WAAW,CAAC,CAAC;AAAA,IAC7B,YAAY,OAAO,aAAa;AAAA,EAClC;AACA,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,4BAA4B,KAAK;AAAA,EAC7C;AACA,SAAO;AACT,GA9Ba;AA+Bb,IAAI,iBAAiB,8BAAO,OAAO,SAAS,SAAS;AACnD,QAAM,SAAS,aAAa,KAAK;AACjC,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,UAAM,IAAI,iBAAiB,MAAM;AAAA,EACnC;AACA,MAAI,CAAC,OAAO,KAAK;AACf,UAAM,IAAI,qBAAqB,MAAM;AAAA,EACvC;AACA,MAAI,QAAQ,UAAU;AACpB,UAAM,WAAW,MAAM,MAAM,QAAQ,UAAU,IAAI;AACnD,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,MAAM,6BAA6B,QAAQ,QAAQ,EAAE;AAAA,IACjE;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,QAAI,CAAC,KAAK,MAAM;AACd,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AACA,QAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC7B,YAAM,IAAI,MAAM,qDAAqD;AAAA,IACvE;AACA,QAAI,QAAQ,MAAM;AAChB,cAAQ,KAAK,KAAK,GAAG,KAAK,IAAI;AAAA,IAChC,OAAO;AACL,cAAQ,OAAO,KAAK;AAAA,IACtB;AAAA,EACF,WAAW,CAAC,QAAQ,MAAM;AACxB,UAAM,IAAI,MAAM,yEAAyE;AAAA,EAC3F;AACA,QAAM,cAAc,QAAQ,KAAK,KAAK,CAAC,QAAQ,IAAI,QAAQ,OAAO,GAAG;AACrE,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACA,SAAO,MAAM,OAAO,OAAO,aAAa,YAAY,OAAO,OAAO,GAAG;AACvE,GAjCqB;AAkCrB,IAAI,SAAS,wBAAC,UAAU;AACtB,MAAI;AACF,UAAM,CAAC,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG;AAC9B,UAAM,SAAS,cAAc,CAAC;AAC9B,UAAM,UAAU,cAAc,CAAC;AAC/B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,QAAQ;AACN,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACF,GAZa;AAab,IAAI,eAAe,wBAAC,UAAU;AAC5B,MAAI;AACF,UAAM,CAAC,CAAC,IAAI,MAAM,MAAM,GAAG;AAC3B,WAAO,cAAc,CAAC;AAAA,EACxB,QAAQ;AACN,UAAM,IAAI,gBAAgB,KAAK;AAAA,EACjC;AACF,GAPmB;;;ADlHnB,IAAI,MAAM,EAAE,MAAM,QAAQ,QAAQ,eAAe;;;AJ8FjD,IAAIC,UAAS,IAAI;AACjB,IAAIC,UAAS,IAAI;AACjB,IAAIC,QAAO,IAAI;;;AFhGf,sBAAmB;AAInB,IAAM,cAAc;AAAA,EAClB,OAAO,wBAAC,UAAkB,MAAM,SAAS,GAAG,GAArC;AAAA,EACP,UAAU,wBAAC,aAAqB,SAAS,SAAS,GAAxC;AACZ;AAEA,IAAM,iBAAiB;AAAA,EACrB,MAAM,wBAAC,SAAiB,KAAK,SAAS,GAAhC;AAAA,EACN,OAAO,wBAAC,UAAkB,MAAM,SAAS,GAAG,GAArC;AAAA,EACP,UAAU,wBAAC,aAAqB,SAAS,UAAU,GAAzC;AAAA,EACV,MAAM,wBAAC,SAAiB,CAAC,WAAW,UAAU,EAAE,SAAS,IAAI,GAAvD;AACR;AAEA,IAAM,OAAO,IAAIC,MAAwB;AAGzC,SAAS,cAAc,MAAW;AAChC,MAAI,CAAC,KAAK,SAAS,CAAC,YAAY,MAAM,KAAK,KAAK,GAAG;AACjD,UAAM,IAAI,MAAM,mBAAmB;AAAA,EACrC;AACA,MAAI,CAAC,KAAK,YAAY,CAAC,YAAY,SAAS,KAAK,QAAQ,GAAG;AAC1D,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AACA,SAAO;AACT;AARS;AAUT,SAAS,iBAAiB,MAAW;AACnC,MAAI,CAAC,KAAK,QAAQ,CAAC,eAAe,KAAK,KAAK,IAAI,GAAG;AACjD,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AACA,MAAI,CAAC,KAAK,SAAS,CAAC,eAAe,MAAM,KAAK,KAAK,GAAG;AACpD,UAAM,IAAI,MAAM,mBAAmB;AAAA,EACrC;AACA,MAAI,CAAC,KAAK,YAAY,CAAC,eAAe,SAAS,KAAK,QAAQ,GAAG;AAC7D,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACrD;AACA,MAAI,KAAK,QAAQ,CAAC,eAAe,KAAK,KAAK,IAAI,GAAG;AAChD,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACpC;AACA,SAAO,EAAE,GAAG,MAAM,MAAM,KAAK,QAAQ,WAAW;AAClD;AAdS;AAiBT,KAAK,KAAK,aAAa,OAAO,MAAM;AAClC,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,MAAM,OAAO,UAAU,KAAK,IAAI,iBAAiB,IAAI;AAG7D,UAAM,eAAe,MAAM,EAAE,IAAI,GAAG;AAAA,MAClC;AAAA,IACF,EAAE,KAAK,KAAK,EAAE,MAAM;AAEpB,QAAI,cAAc;AAChB,aAAO,EAAE,KAAK,EAAE,OAAO,wBAAwB,GAAG,GAAG;AAAA,IACvD;AAGA,UAAM,eAAe,MAAM,gBAAAC,QAAO,KAAK,UAAU,EAAE;AACnD,UAAM,SAAS,OAAO,WAAW;AACjC,UAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AAGnC,UAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,KAGtB,EAAE,KAAK,QAAQ,OAAO,MAAM,cAAc,MAAM,KAAK,KAAK,IAAI,EAAE,IAAI;AAGrE,UAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,KAGtB,EAAE,KAAK,OAAO,WAAW,GAAG,QAAQ,GAAG,GAAG,EAAE,IAAI;AAGjD,UAAM,QAAQ,MAAMC,MAAK;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI,IAAK,KAAK,KAAK;AAAA;AAAA,IAClD,GAAG,EAAE,IAAI,UAAU;AAEnB,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,MACT;AAAA,MACA,MAAM,EAAE,IAAI,QAAQ,OAAO,MAAM,KAAK;AAAA,IACxC,CAAC;AAAA,EACH,SAASC,QAAY;AACnB,YAAQ,MAAM,uBAAuBA,MAAK;AAC1C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAOA,OAAM,WAAW;AAAA,IAC1B,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,KAAK,KAAK,UAAU,OAAO,MAAM;AAC/B,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,OAAO,SAAS,IAAI,cAAc,IAAI;AAG9C,UAAMC,QAAO,MAAM,EAAE,IAAI,GAAG;AAAA,MAC1B;AAAA,IACF,EAAE,KAAK,KAAK,EAAE,MAAM;AAEpB,QAAI,CAACA,SAAQ,CAACA,MAAK,WAAW;AAC5B,aAAO,EAAE,KAAK,EAAE,OAAO,4BAA4B,GAAG,GAAG;AAAA,IAC3D;AAGA,UAAM,kBAAkB,MAAM,gBAAAH,QAAO,QAAQ,UAAUG,MAAK,aAAa;AACzE,QAAI,CAAC,iBAAiB;AACpB,aAAO,EAAE,KAAK,EAAE,OAAO,4BAA4B,GAAG,GAAG;AAAA,IAC3D;AAGA,UAAM,QAAQ,MAAMF,MAAK;AAAA,MACvB,QAAQE,MAAK;AAAA,MACb,OAAOA,MAAK;AAAA,MACZ,MAAMA,MAAK;AAAA,MACX,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI,IAAK,KAAK,KAAK;AAAA;AAAA,IAClD,GAAG,EAAE,IAAI,UAAU;AAEnB,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,MACT;AAAA,MACA,MAAM;AAAA,QACJ,IAAIA,MAAK;AAAA,QACT,OAAOA,MAAK;AAAA,QACZ,MAAMA,MAAK;AAAA,QACX,MAAMA,MAAK;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,SAASD,QAAY;AACnB,YAAQ,MAAM,gBAAgBA,MAAK;AACnC,WAAO,EAAE,KAAK;AAAA,MACZ,OAAOA,OAAM,WAAW;AAAA,IAC1B,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,KAAK,IAAI,WAAW,OAAO,MAAM;AAC/B,MAAI;AACF,UAAM,aAAa,EAAE,IAAI,OAAO,eAAe;AAC/C,QAAI,CAAC,cAAc,CAAC,WAAW,WAAW,SAAS,GAAG;AACpD,aAAO,EAAE,KAAK,EAAE,OAAO,wBAAwB,GAAG,GAAG;AAAA,IACvD;AAEA,UAAM,QAAQ,WAAW,UAAU,CAAC;AACpC,UAAM,UAAU,MAAME,QAAO,OAAO,EAAE,IAAI,UAAU;AAGpD,UAAMD,QAAO,MAAM,EAAE,IAAI,GAAG;AAAA,MAC1B;AAAA,IACF,EAAE,KAAK,QAAQ,MAAM,EAAE,MAAM;AAE7B,QAAI,CAACA,SAAQ,CAACA,MAAK,WAAW;AAC5B,aAAO,EAAE,KAAK,EAAE,OAAO,wCAAwC,GAAG,GAAG;AAAA,IACvE;AAEA,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,MAAM;AAAA,QACJ,IAAIA,MAAK;AAAA,QACT,OAAOA,MAAK;AAAA,QACZ,MAAMA,MAAK;AAAA,QACX,MAAMA,MAAK;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,SAASD,QAAY;AACnB,YAAQ,MAAM,6BAA6BA,MAAK;AAChD,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,CAAC;;;ActLD;AAAA;AAAA;AAAAG;AAIA,IAAM,OAAO,IAAIC,MAAwB;AAGzC,IAAM,iBAAiB,8BAAO,GAAQ,SAAc;AAClD,MAAI;AACF,UAAM,aAAa,EAAE,IAAI,OAAO,eAAe;AAC/C,QAAI,CAAC,cAAc,CAAC,WAAW,WAAW,SAAS,GAAG;AACpD,aAAO,EAAE,KAAK,EAAE,OAAO,wBAAwB,GAAG,GAAG;AAAA,IACvD;AAEA,UAAM,QAAQ,WAAW,UAAU,CAAC;AACpC,UAAM,UAAU,MAAMC,QAAO,OAAO,EAAE,IAAI,UAAU;AACpD,MAAE,IAAI,QAAQ,OAAO;AACrB,UAAM,KAAK;AAAA,EACb,SAASC,QAAO;AACd,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,GAduB;AAiBvB,KAAK,IAAI,YAAY,gBAAgB,OAAO,MAAM;AAChD,MAAI;AACF,UAAM,cAAc,EAAE,IAAI,MAAM;AAEhC,UAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAMvC,EAAE,KAAK,YAAY,MAAM,EAAE,MAAM;AAElC,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,KAAK,EAAE,OAAO,uBAAuB,GAAG,GAAG;AAAA,IACtD;AAEA,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,MAAM;AAAA,QACJ,IAAI,SAAS;AAAA,QACb,OAAO,SAAS;AAAA,QAChB,MAAM,SAAS;AAAA,QACf,MAAM,SAAS;AAAA,QACf,KAAK,SAAS;AAAA,QACd,SAAS,SAAS,WAAW;AAAA,QAC7B,YAAY,SAAS;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH,SAASA,QAAY;AACnB,YAAQ,MAAM,sBAAsBA,MAAK;AACzC,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,CAAC;AAGD,KAAK,IAAI,YAAY,gBAAgB,OAAO,MAAM;AAChD,MAAI;AACF,UAAM,cAAc,EAAE,IAAI,MAAM;AAChC,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,MAAM,IAAI,IAAI;AAEtB,QAAI,CAAC,QAAQ,KAAK,KAAK,EAAE,WAAW,GAAG;AACrC,aAAO,EAAE,KAAK,EAAE,OAAO,0BAA0B,GAAG,GAAG;AAAA,IACzD;AAEA,UAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AAEnC,UAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAItB,EAAE,KAAK,KAAK,KAAK,GAAG,OAAO,MAAM,KAAK,YAAY,MAAM,EAAE,IAAI;AAE/D,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAASA,QAAY;AACnB,YAAQ,MAAM,yBAAyBA,MAAK;AAC5C,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,CAAC;AAGD,KAAK,IAAI,iBAAiB,gBAAgB,OAAO,MAAM;AACrD,MAAI;AACF,UAAM,cAAc,EAAE,IAAI,MAAM;AAEhC,UAAM,cAAc,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAI1C,EAAE,KAAK,YAAY,MAAM,EAAE,MAAM;AAElC,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,aAAa,eAAe;AAAA,IAC9B,CAAC;AAAA,EACH,SAASA,QAAY;AACnB,YAAQ,MAAM,2BAA2BA,MAAK;AAC9C,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,CAAC;AAGD,KAAK,IAAI,iBAAiB,gBAAgB,OAAO,MAAM;AACrD,MAAI;AACF,UAAM,cAAc,EAAE,IAAI,MAAM;AAChC,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,WAAW,qBAAqB,eAAe,IAAI;AAE3D,QAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,gBAAgB;AACzD,aAAO,EAAE,KAAK,EAAE,OAAO,0BAA0B,GAAG,GAAG;AAAA,IACzD;AAEA,UAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AAGnC,UAAM,WAAW,MAAM,EAAE,IAAI,GAAG;AAAA,MAC9B;AAAA,IACF,EAAE,KAAK,YAAY,MAAM,EAAE,MAAM;AAEjC,QAAI,UAAU;AAEZ,YAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,OAItB,EAAE,KAAK,WAAW,qBAAqB,gBAAgB,KAAK,YAAY,MAAM,EAAE,IAAI;AAAA,IACvF,OAAO;AAEL,YAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGtB,EAAE,KAAK,OAAO,WAAW,GAAG,YAAY,QAAQ,WAAW,qBAAqB,gBAAgB,KAAK,GAAG,EAAE,IAAI;AAAA,IACjH;AAEA,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAASA,QAAY;AACnB,YAAQ,MAAM,8BAA8BA,MAAK;AACjD,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,CAAC;;;ACrJD;AAAA;AAAA;AAAAC;AAIA,IAAM,YAAY,IAAIC,MAAwB;AAG9C,IAAMC,kBAAiB,8BAAO,GAAQ,SAAc;AAClD,MAAI;AACF,UAAM,aAAa,EAAE,IAAI,OAAO,eAAe;AAC/C,QAAI,CAAC,cAAc,CAAC,WAAW,WAAW,SAAS,GAAG;AACpD,aAAO,EAAE,KAAK,EAAE,OAAO,wBAAwB,GAAG,GAAG;AAAA,IACvD;AAEA,UAAM,QAAQ,WAAW,UAAU,CAAC;AACpC,UAAM,UAAU,MAAMC,QAAO,OAAO,EAAE,IAAI,UAAU;AACpD,MAAE,IAAI,QAAQ,OAAO;AACrB,UAAM,KAAK;AAAA,EACb,SAASC,QAAO;AACd,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,GAduB;AAiBvB,UAAU,IAAI,KAAKF,iBAAgB,OAAO,MAAM;AAC9C,MAAI;AACF,UAAM,cAAc,EAAE,IAAI,MAAM;AAEhC,QAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUZ,QAAI,YAAY,SAAS,WAAW;AAClC,cAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYV;AAEA,UAAMG,aAAY,YAAY,SAAS,YACnC,MAAM,EAAE,IAAI,GAAG,QAAQ,KAAK,EAAE,KAAK,YAAY,MAAM,EAAE,IAAI,IAC3D,MAAM,EAAE,IAAI,GAAG,QAAQ,KAAK,EAAE,IAAI;AAEtC,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,WAAWA,WAAU,WAAW,CAAC;AAAA,IACnC,CAAC;AAAA,EACH,SAASD,QAAY;AACnB,YAAQ,MAAM,wBAAwBA,MAAK;AAC3C,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,CAAC;AAGD,UAAU,KAAK,KAAKF,iBAAgB,OAAO,MAAM;AAC/C,MAAI;AACF,UAAM,cAAc,EAAE,IAAI,MAAM;AAEhC,QAAI,YAAY,SAAS,WAAW;AAClC,aAAO,EAAE,KAAK,EAAE,OAAO,4CAA4C,GAAG,GAAG;AAAA,IAC3E;AAEA,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,OAAAI,QAAO,aAAa,QAAQ,gBAAgB,cAAc,aAAa,IAAI;AAEnF,QAAI,CAACA,UAAS,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB;AACxD,aAAO,EAAE,KAAK,EAAE,OAAO,0BAA0B,GAAG,GAAG;AAAA,IACzD;AAEA,UAAM,aAAa,OAAO,WAAW;AACrC,UAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AAEnC,UAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAItB,EAAE;AAAA,MACD;AAAA,MAAY,YAAY;AAAA,MAAQA;AAAA,MAAO;AAAA,MACvC;AAAA,MAAQ;AAAA,MAAgB,gBAAgB;AAAA,MAAI,gBAAgB;AAAA,MAC5D;AAAA,MAAK;AAAA,IACP,EAAE,IAAI;AAEN,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,SAASF,QAAY;AACnB,YAAQ,MAAM,0BAA0BA,MAAK;AAC7C,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,CAAC;AAGD,UAAU,IAAI,QAAQF,iBAAgB,OAAO,MAAM;AACjD,MAAI;AACF,UAAM,aAAa,EAAE,IAAI,MAAM,IAAI;AAEnC,UAAM,WAAW,MAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,KAKvC,EAAE,KAAK,UAAU,EAAE,MAAM;AAE1B,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,KAAK,EAAE,OAAO,2BAA2B,GAAG,GAAG;AAAA,IAC1D;AAEA,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,SAASE,QAAY;AACnB,YAAQ,MAAM,uBAAuBA,MAAK;AAC1C,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,CAAC;AAGD,UAAU,IAAI,eAAeF,iBAAgB,OAAO,MAAM;AACxD,MAAI;AACF,UAAM,cAAc,EAAE,IAAI,MAAM;AAChC,UAAM,aAAa,EAAE,IAAI,MAAM,IAAI;AACnC,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,OAAO,IAAI;AAEnB,QAAI,CAAC,CAAC,SAAS,UAAU,UAAU,WAAW,EAAE,SAAS,MAAM,GAAG;AAChE,aAAO,EAAE,KAAK,EAAE,OAAO,qBAAqB,GAAG,GAAG;AAAA,IACpD;AAGA,UAAM,WAAW,MAAM,EAAE,IAAI,GAAG;AAAA,MAC9B;AAAA,IACF,EAAE,KAAK,UAAU,EAAE,MAAM;AAEzB,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,KAAK,EAAE,OAAO,2BAA2B,GAAG,GAAG;AAAA,IAC1D;AAEA,QAAI,SAAS,eAAe,YAAY,UAAU,YAAY,SAAS,SAAS;AAC9E,aAAO,EAAE,KAAK,EAAE,OAAO,uBAAuB,GAAG,GAAG;AAAA,IACtD;AAEA,UAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AAEnC,UAAM,EAAE,IAAI,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAItB,EAAE,KAAK,QAAQ,KAAK,UAAU,EAAE,IAAI;AAErC,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAASE,QAAY;AACnB,YAAQ,MAAM,iCAAiCA,MAAK;AACpD,WAAO,EAAE,KAAK,EAAE,OAAO,oBAAoB,GAAG,GAAG;AAAA,EACnD;AACF,CAAC;;;AtChKD,IAAM,MAAM,IAAIG,MAAwB;AAGxC,IAAI,IAAI,KAAK,OAAO,GAAG,SAAS;AAC9B,QAAM,cAAc,EAAE,IAAI,aAAa,MAAM,GAAG;AAChD,SAAO,KAAK;AAAA,IACV,QAAQ;AAAA,IACR,cAAc,CAAC,OAAO,QAAQ,OAAO,UAAU,SAAS;AAAA,IACxD,cAAc,CAAC,gBAAgB,eAAe;AAAA,IAC9C,aAAa;AAAA,EACf,CAAC,EAAE,GAAG,IAAI;AACZ,CAAC;AAGD,IAAI,IAAI,KAAK,CAAC,MAAM;AAClB,SAAO,EAAE,KAAK;AAAA,IACZ,SAAS;AAAA,IACT,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IAClC,SAAS;AAAA,EACX,CAAC;AACH,CAAC;AAGD,IAAI,MAAM,SAAS,IAAU;AAC7B,IAAI,MAAM,SAAS,IAAU;AAC7B,IAAI,MAAM,cAAc,SAAc;AAGtC,IAAI,QAAQ,CAAC,KAAK,MAAM;AACtB,UAAQ,MAAM,cAAc,GAAG;AAC/B,SAAO,EAAE,KAAK;AAAA,IACZ,OAAO;AAAA,IACP,SAAS,IAAI;AAAA,EACf,GAAG,GAAG;AACR,CAAC;AAED,IAAO,cAAQ;;;AuClDf;AAAA;AAAA;AAAAC;AAEA,IAAM,YAAwB,8BAAO,SAASC,MAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAASA,IAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACjBf;AAAA;AAAA;AAAAC;AASA,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAASC,MAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAASA,IAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAMC,SAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAKA,QAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;AzCzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;A0CVnB;AAAA;AAAA;AAAAC;AAwBA,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACAC,MACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAASA,MAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACAA,MACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAASA,MAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;A3C3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACAC,MACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAASA,MAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAASA,MAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAYA,MAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAASA,MAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACAA,MACA,QACI;AACJ,WAAK,MAAMA;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["init_performance", "init_performance", "PerformanceMark", "init_performance", "init_performance", "init_performance", "init_performance", "clear", "count", "<PERSON><PERSON><PERSON><PERSON>", "createTask", "debug", "dir", "dirxml", "error", "group", "groupCollapsed", "groupEnd", "info", "log", "profile", "profileEnd", "table", "time", "timeEnd", "timeLog", "timeStamp", "trace", "warn", "init_console", "init_performance", "init_console", "init_performance", "hrtime", "init_performance", "dir", "env", "count", "init_performance", "init_performance", "init_performance", "init_performance", "cwd", "hrtime", "assert", "init_process", "init_performance", "init_process", "init_performance", "init_performance", "init_performance", "init_performance", "sign", "subtle", "verify", "webcrypto", "init_crypto", "init_performance", "init_performance", "init_crypto", "init_performance", "bcrypt", "random", "callback", "nextTick", "salt", "hash", "off", "utfx", "b", "err", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "context", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "raw", "init_performance", "context", "init_performance", "init_performance", "app", "env", "context", "init_performance", "init_performance", "init_performance", "context", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "Node", "Node", "<PERSON><PERSON>", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "init_performance", "platform", "init_performance", "init_performance", "verify", "decode", "sign", "<PERSON><PERSON>", "bcrypt", "sign", "error", "user", "verify", "init_performance", "<PERSON><PERSON>", "verify", "error", "init_performance", "<PERSON><PERSON>", "authMiddleware", "verify", "error", "campaigns", "title", "<PERSON><PERSON>", "init_performance", "env", "init_performance", "env", "error", "init_performance", "env", "env"]}
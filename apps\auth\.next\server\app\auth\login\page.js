(()=>{var e={};e.id=859,e.ids=[859],e.modules={317:(e,t,a)=>{Promise.resolve().then(a.bind(a,9834))},685:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(8828);function s({children:e}){return(0,r.jsx)("html",{lang:"id",children:(0,r.jsx)("body",{children:(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900 p-4",children:e})})})}a(1365),a(2843)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2449:()=>{},2701:(e,t,a)=>{Promise.resolve().then(a.bind(a,7349))},2721:()=>{},2843:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3782:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,385,23)),Promise.resolve().then(a.t.bind(a,3737,23)),Promise.resolve().then(a.t.bind(a,6081,23)),Promise.resolve().then(a.t.bind(a,1904,23)),Promise.resolve().then(a.t.bind(a,5856,23)),Promise.resolve().then(a.t.bind(a,5492,23)),Promise.resolve().then(a.t.bind(a,9082,23)),Promise.resolve().then(a.t.bind(a,5812,23))},3873:e=>{"use strict";e.exports=require("path")},4030:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,9355,23)),Promise.resolve().then(a.t.bind(a,4439,23)),Promise.resolve().then(a.t.bind(a,7851,23)),Promise.resolve().then(a.t.bind(a,4730,23)),Promise.resolve().then(a.t.bind(a,9774,23)),Promise.resolve().then(a.t.bind(a,3170,23)),Promise.resolve().then(a.t.bind(a,968,23)),Promise.resolve().then(a.t.bind(a,8298,23))},4455:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>d.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>p,tree:()=>l});var r=a(4332),s=a(8819),o=a(7851),d=a.n(o),n=a(7540),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);a.d(t,i);let l={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,7349)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,2341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=["C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\auth\\login\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},6907:(e,t,a)=>{"use strict";a.d(t,{$n:()=>c,Zp:()=>h,Wu:()=>y,BT:()=>v,wL:()=>w,aR:()=>b,ZB:()=>g,pd:()=>p,JU:()=>x});var r=a(3486),s=a(159),o=a(3072),d=a(6353),n=a(4627),i=a(5855);function l(...e){return(0,i.QP)((0,n.$)(e))}let m=(0,d.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef(({className:e,variant:t,size:a,asChild:s=!1,...d},n)=>{let i=s?o.DX:"button";return(0,r.jsx)(i,{className:l(m({variant:t,size:a,className:e})),ref:n,...d})});c.displayName="Button";let p=s.forwardRef(({className:e,type:t,...a},s)=>(0,r.jsx)("input",{type:t,className:l("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));p.displayName="Input";var f=a(6370);let u=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),x=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(f.b,{ref:a,className:l(u(),e),...t}));x.displayName=f.b.displayName;let h=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:l("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));h.displayName="Card";let b=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:l("flex flex-col space-y-1.5 p-6",e),...t}));b.displayName="CardHeader";let g=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("h3",{ref:a,className:l("text-2xl font-semibold leading-none tracking-tight",e),...t}));g.displayName="CardTitle";let v=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("p",{ref:a,className:l("text-sm text-muted-foreground",e),...t}));v.displayName="CardDescription";let y=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:l("p-6 pt-0",e),...t}));y.displayName="CardContent";let w=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:l("flex items-center p-6 pt-0",e),...t}));w.displayName="CardFooter";var N=a(2616),j=a(3967),P=a(9391),k=a(2283);N.bL,N.l9,N.YJ,N.ZL,N.Pb,N.z6,s.forwardRef(({className:e,inset:t,children:a,...s},o)=>(0,r.jsxs)(N.ZP,{ref:o,className:l("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...s,children:[a,(0,r.jsx)(j.A,{className:"ml-auto h-4 w-4"})]})).displayName=N.ZP.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(N.G5,{ref:a,className:l("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=N.G5.displayName,s.forwardRef(({className:e,sideOffset:t=4,...a},s)=>(0,r.jsx)(N.ZL,{children:(0,r.jsx)(N.UC,{ref:s,sideOffset:t,className:l("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})})).displayName=N.UC.displayName,s.forwardRef(({className:e,inset:t,...a},s)=>(0,r.jsx)(N.q7,{ref:s,className:l("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...a})).displayName=N.q7.displayName,s.forwardRef(({className:e,children:t,checked:a,...s},o)=>(0,r.jsxs)(N.H_,{ref:o,className:l("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:a,...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(N.VF,{children:(0,r.jsx)(P.A,{className:"h-4 w-4"})})}),t]})).displayName=N.H_.displayName,s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(N.hN,{ref:s,className:l("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(N.VF,{children:(0,r.jsx)(k.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=N.hN.displayName,s.forwardRef(({className:e,inset:t,...a},s)=>(0,r.jsx)(N.JU,{ref:s,className:l("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...a})).displayName=N.JU.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(N.wv,{ref:a,className:l("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=N.wv.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:l("w-full caption-bottom text-sm",e),...t})})).displayName="Table",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("thead",{ref:a,className:l("[&_tr]:border-b",e),...t})).displayName="TableHeader",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tbody",{ref:a,className:l("[&_tr:last-child]:border-0",e),...t})).displayName="TableBody",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tfoot",{ref:a,className:l("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tr",{ref:a,className:l("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t})).displayName="TableRow",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("th",{ref:a,className:l("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t})).displayName="TableHead",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("td",{ref:a,className:l("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t})).displayName="TableCell",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("caption",{ref:a,className:l("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},7349:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(3952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\apps\\\\auth\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\auth\\login\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9699:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(1253);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},9834:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(3486),s=a(9989),o=a.n(s),d=a(860),n=a(5626),i=a(5519),l=a(1507),m=a(6907);let c=l.Ik({email:l.Yj().email({message:"Alamat email tidak valid."}),password:l.Yj().min(1,{message:"Password tidak boleh kosong."})});function p(){let e=(0,d.useRouter)(),{register:t,handleSubmit:a,formState:{errors:s,isSubmitting:l}}=(0,n.mN)({resolver:(0,i.u)(c)}),p=async t=>{try{let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t.email,password:t.password})});if(!a.ok){let e=await a.json();throw Error(e.error||"Login gagal. Periksa kembali email dan password Anda.")}e.push("https://dashboard.monetizr.com")}catch(e){console.error(e)}};return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900 p-4",children:(0,r.jsx)(m.Zp,{className:"w-full max-w-md",children:(0,r.jsxs)("form",{onSubmit:a(p),children:[(0,r.jsxs)(m.aR,{className:"space-y-1 text-center",children:[(0,r.jsx)(m.ZB,{className:"text-2xl font-bold",children:"Login ke PromotePro"}),(0,r.jsx)(m.BT,{children:"Masukkan email dan password Anda untuk mengakses dashboard."})]}),(0,r.jsxs)(m.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.JU,{htmlFor:"email",children:"Email"}),(0,r.jsx)(m.pd,{id:"email",type:"email",placeholder:"<EMAIL>",...t("email")}),s.email&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:s.email.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.JU,{htmlFor:"password",children:"Password"}),(0,r.jsx)(m.pd,{id:"password",type:"password",...t("password")}),s.password&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:s.password.message})]}),(0,r.jsx)(m.$n,{type:"submit",className:"w-full",disabled:l,children:l?"Memproses...":"Login"})]}),(0,r.jsxs)(m.wL,{className:"flex flex-col gap-2 text-sm text-center",children:[(0,r.jsx)(o(),{href:"/auth/forgot-password",children:"Lupa Password?"}),(0,r.jsxs)("p",{children:["Belum punya akun?"," ",(0,r.jsx)(o(),{href:"/auth/register",children:"Daftar Sekarang"})]})]})]})})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[191,787,253,130],()=>a(4455));module.exports=r})();
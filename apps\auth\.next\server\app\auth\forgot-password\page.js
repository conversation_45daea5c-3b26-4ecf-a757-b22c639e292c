(()=>{var e={};e.id=413,e.ids=[413],e.modules={685:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(8828);function s({children:e}){return(0,a.jsx)("html",{lang:"id",children:(0,a.jsx)("body",{children:(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900 p-4",children:e})})})}r(1365),r(2843)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2449:()=>{},2721:()=>{},2843:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3782:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,385,23)),Promise.resolve().then(r.t.bind(r,3737,23)),Promise.resolve().then(r.t.bind(r,6081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,5856,23)),Promise.resolve().then(r.t.bind(r,5492,23)),Promise.resolve().then(r.t.bind(r,9082,23)),Promise.resolve().then(r.t.bind(r,5812,23))},3873:e=>{"use strict";e.exports=require("path")},4030:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9355,23)),Promise.resolve().then(r.t.bind(r,4439,23)),Promise.resolve().then(r.t.bind(r,7851,23)),Promise.resolve().then(r.t.bind(r,4730,23)),Promise.resolve().then(r.t.bind(r,9774,23)),Promise.resolve().then(r.t.bind(r,3170,23)),Promise.resolve().then(r.t.bind(r,968,23)),Promise.resolve().then(r.t.bind(r,8298,23))},6643:(e,t,r)=>{Promise.resolve().then(r.bind(r,8973))},6907:(e,t,r)=>{"use strict";r.d(t,{$n:()=>c,Zp:()=>h,Wu:()=>y,BT:()=>v,wL:()=>N,aR:()=>b,ZB:()=>g,pd:()=>f,JU:()=>x});var a=r(3486),s=r(159),o=r(3072),n=r(6353),i=r(4627),d=r(5855);function l(...e){return(0,d.QP)((0,i.$)(e))}let m=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...n},i)=>{let d=s?o.DX:"button";return(0,a.jsx)(d,{className:l(m({variant:t,size:r,className:e})),ref:i,...n})});c.displayName="Button";let f=s.forwardRef(({className:e,type:t,...r},s)=>(0,a.jsx)("input",{type:t,className:l("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...r}));f.displayName="Input";var p=r(6370);let u=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),x=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(p.b,{ref:r,className:l(u(),e),...t}));x.displayName=p.b.displayName;let h=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:l("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));h.displayName="Card";let b=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:l("flex flex-col space-y-1.5 p-6",e),...t}));b.displayName="CardHeader";let g=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("h3",{ref:r,className:l("text-2xl font-semibold leading-none tracking-tight",e),...t}));g.displayName="CardTitle";let v=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("p",{ref:r,className:l("text-sm text-muted-foreground",e),...t}));v.displayName="CardDescription";let y=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:l("p-6 pt-0",e),...t}));y.displayName="CardContent";let N=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:l("flex items-center p-6 pt-0",e),...t}));N.displayName="CardFooter";var w=r(2616),j=r(3967),P=r(9391),k=r(2283);w.bL,w.l9,w.YJ,w.ZL,w.Pb,w.z6,s.forwardRef(({className:e,inset:t,children:r,...s},o)=>(0,a.jsxs)(w.ZP,{ref:o,className:l("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...s,children:[r,(0,a.jsx)(j.A,{className:"ml-auto h-4 w-4"})]})).displayName=w.ZP.displayName,s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(w.G5,{ref:r,className:l("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=w.G5.displayName,s.forwardRef(({className:e,sideOffset:t=4,...r},s)=>(0,a.jsx)(w.ZL,{children:(0,a.jsx)(w.UC,{ref:s,sideOffset:t,className:l("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})})).displayName=w.UC.displayName,s.forwardRef(({className:e,inset:t,...r},s)=>(0,a.jsx)(w.q7,{ref:s,className:l("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...r})).displayName=w.q7.displayName,s.forwardRef(({className:e,children:t,checked:r,...s},o)=>(0,a.jsxs)(w.H_,{ref:o,className:l("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:r,...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(w.VF,{children:(0,a.jsx)(P.A,{className:"h-4 w-4"})})}),t]})).displayName=w.H_.displayName,s.forwardRef(({className:e,children:t,...r},s)=>(0,a.jsxs)(w.hN,{ref:s,className:l("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(w.VF,{children:(0,a.jsx)(k.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=w.hN.displayName,s.forwardRef(({className:e,inset:t,...r},s)=>(0,a.jsx)(w.JU,{ref:s,className:l("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...r})).displayName=w.JU.displayName,s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(w.wv,{ref:r,className:l("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=w.wv.displayName,s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:r,className:l("w-full caption-bottom text-sm",e),...t})})).displayName="Table",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("thead",{ref:r,className:l("[&_tr]:border-b",e),...t})).displayName="TableHeader",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tbody",{ref:r,className:l("[&_tr:last-child]:border-0",e),...t})).displayName="TableBody",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tfoot",{ref:r,className:l("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tr",{ref:r,className:l("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t})).displayName="TableRow",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("th",{ref:r,className:l("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t})).displayName="TableHead",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("td",{ref:r,className:l("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t})).displayName="TableCell",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("caption",{ref:r,className:l("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},6971:(e,t,r)=>{Promise.resolve().then(r.bind(r,9639))},6991:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>f,tree:()=>l});var a=r(4332),s=r(8819),o=r(7851),n=r.n(o),i=r(7540),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["auth",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9639)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\auth\\forgot-password\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,2341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=["C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\auth\\forgot-password\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},f=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/forgot-password/page",pathname:"/auth/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8973:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(3486),s=r(9989),o=r.n(s),n=r(5626),i=r(5519),d=r(1507),l=r(6907);let m=d.Ik({email:d.Yj().email({message:"Alamat email tidak valid."})});function c(){let{register:e,handleSubmit:t,formState:{errors:r,isSubmitting:s}}=(0,n.mN)({resolver:(0,i.u)(m)}),d=async e=>{try{console.log("Mengirim link reset untuk:",e.email),alert("Jika email Anda terdaftar, Anda akan menerima link reset.")}catch(e){console.error(e),alert("Terjadi kesalahan. Coba lagi nanti.")}};return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900 p-4",children:(0,a.jsx)(l.Zp,{className:"w-full max-w-md",children:(0,a.jsxs)("form",{onSubmit:t(d),children:[(0,a.jsxs)(l.aR,{className:"space-y-1 text-center",children:[(0,a.jsx)(l.ZB,{className:"text-2xl font-bold",children:"Lupa Password?"}),(0,a.jsx)(l.BT,{children:"Masukkan email Anda, kami akan mengirimkan instruksi untuk reset."})]}),(0,a.jsxs)(l.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.JU,{htmlFor:"email",children:"Email"}),(0,a.jsx)(l.pd,{id:"email",type:"email",placeholder:"<EMAIL>",...e("email")}),r.email&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:r.email.message})]}),(0,a.jsx)(l.$n,{type:"submit",className:"w-full",disabled:s,children:s?"Mengirim...":"Kirim Link Reset"})]}),(0,a.jsx)(l.wL,{className:"text-sm text-center",children:(0,a.jsx)(o(),{href:"/auth/login",className:"underline",children:"Kembali ke Login"})})]})})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9639:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(3952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\apps\\\\auth\\\\src\\\\app\\\\auth\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\auth\\forgot-password\\page.tsx","default")},9699:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(1253);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[191,787,253,130],()=>r(6991));module.exports=a})();
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[607],{444:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function u(e){let t=new URLSearchParams;for(let[n,u]of Object.entries(e))if(Array.isArray(u))for(let e of u)t.append(n,r(e));else t.set(n,r(u));return t}function o(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return u}})},1907:(e,t,n)=>{var r=n(7620),u="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useState,i=r.useEffect,a=r.useLayoutEffect,l=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!u(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),u=r[0].inst,c=r[1];return a(function(){u.value=n,u.getSnapshot=t,s(u)&&c({inst:u})},[e,n,t]),i(function(){return s(u)&&c({inst:u}),e(function(){s(u)&&c({inst:u})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},2942:(e,t,n)=>{var r=n(2418);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}})},3100:(e,t,n)=>{e.exports=n(1907)},4509:(e,t,n)=>{e.exports=n(9172)},4637:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let r=n(8490),u=n(1075);function o(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,u.hasBasePath)(n.pathname)}catch(e){return!1}}},5908:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return a},urlObjectKeys:function(){return i}});let r=n(5999)._(n(444)),u=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:n}=e,o=e.protocol||"",i=e.pathname||"",a=e.hash||"",l=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:n&&(s=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(s+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||u.test(o))&&!1!==s?(s="//"+(s||""),i&&"/"!==i[0]&&(i="/"+i)):s||(s=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+o+s+(i=i.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return o(e)}},6355:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},7261:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let r=n(5999),u=n(4568),o=r._(n(7620)),i=n(5908),a=n(9330),l=n(7533),s=n(7849),c=n(8490),f=n(7720);n(1611);let d=n(3781),p=n(4637),h=n(529);function y(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function g(e){let t,n,r,[i,g]=(0,o.useOptimistic)(d.IDLE_LINK_STATUS),v=(0,o.useRef)(null),{href:m,as:E,children:P,prefetch:S=null,passHref:O,replace:j,shallow:_,scroll:T,onClick:w,onMouseEnter:x,onTouchStart:C,legacyBehavior:A=!1,onNavigate:N,ref:L,unstable_dynamicOnHover:R,...I}=e;t=P,A&&("string"==typeof t||"number"==typeof t)&&(t=(0,u.jsx)("a",{children:t}));let M=o.default.useContext(a.AppRouterContext),U=!1!==S,k=null===S?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:D,as:F}=o.default.useMemo(()=>{let e=y(m);return{href:e,as:E?y(E):e}},[m,E]);A&&(n=o.default.Children.only(t));let K=A?n&&"object"==typeof n&&n.ref:L,z=o.default.useCallback(e=>(null!==M&&(v.current=(0,d.mountLinkInstance)(e,D,M,k,U,g)),()=>{v.current&&((0,d.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,d.unmountPrefetchableInstance)(e)}),[U,D,M,k,g]),V={ref:(0,s.useMergedRef)(z,K),onClick(e){A||"function"!=typeof w||w(e),A&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),M&&(e.defaultPrevented||function(e,t,n,r,u,i,a){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){u&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),o.default.startTransition(()=>{if(a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,u?"replace":"push",null==i||i,r.current)})}}(e,D,F,v,j,T,N))},onMouseEnter(e){A||"function"!=typeof x||x(e),A&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),M&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===R)},onTouchStart:function(e){A||"function"!=typeof C||C(e),A&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),M&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===R)}};return(0,c.isAbsoluteUrl)(F)?V.href=F:A&&!O&&("a"!==n.type||"href"in n.props)||(V.href=(0,f.addBasePath)(F)),r=A?o.default.cloneElement(n,V):(0,u.jsx)("a",{...I,...V,children:t}),(0,u.jsx)(b.Provider,{value:i,children:r})}n(6355);let b=(0,o.createContext)(d.IDLE_LINK_STATUS),v=()=>(0,o.useContext)(b);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7849:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return u}});let r=n(7620);function u(e,t){let n=(0,r.useRef)(null),u=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=u.current;t&&(u.current=null,t())}else e&&(n.current=o(e,r)),t&&(u.current=o(t,r))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8490:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return b},NormalizeError:function(){return y},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return i},getURL:function(){return a},isAbsoluteUrl:function(){return o},isResSent:function(){return s},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return m}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,u=Array(r),o=0;o<r;o++)u[o]=arguments[o];return n||(n=!0,t=e(...u)),t}}let u=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>u.test(e);function i(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){let{href:e}=window.location,t=i();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&s(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function m(e){return JSON.stringify({message:e.message,stack:e.stack})}},9172:(e,t,n)=>{var r=n(7620),u=n(3100),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=u.useSyncExternalStore,a=r.useRef,l=r.useEffect,s=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,u){var f=a(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=i(e,(f=s(function(){function e(e){if(!l){if(l=!0,i=e,e=r(e),void 0!==u&&d.hasValue){var t=d.value;if(u(t,e))return a=t}return a=e}if(t=a,o(i,e))return t;var n=r(e);return void 0!==u&&u(t,n)?(i=e,t):(i=e,a=n)}var i,a,l=!1,s=void 0===n?null:n;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,n,r,u]))[0],f[1]);return l(function(){d.hasValue=!0,d.value=p},[p]),c(p),p}},9906:(e,t,n)=>{n.d(t,{vt:()=>d});let r=e=>{let t,n=new Set,r=(e,r)=>{let u="function"==typeof e?e(t):e;if(!Object.is(u,t)){let e=t;t=(null!=r?r:"object"!=typeof u||null===u)?u:Object.assign({},t,u),n.forEach(n=>n(t,e))}},u=()=>t,o={setState:r,getState:u,getInitialState:()=>i,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},i=t=e(r,u,o);return o},u=e=>e?r(e):r;var o=n(7620),i=n(4509);let{useDebugValue:a}=o,{useSyncExternalStoreWithSelector:l}=i,s=!1,c=e=>e,f=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?u(e):e,n=(e,n)=>(function(e,t=c,n){n&&!s&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),s=!0);let r=l(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return a(r),r})(t,e,n);return Object.assign(n,t),n},d=e=>e?f(e):f}}]);
(()=>{var e={};e.id=397,e.ids=[397],e.modules={408:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3252:(e,r,t)=>{"use strict";t.d(r,{$C:()=>u,mI:()=>o,QQ:()=>p,bw:()=>n});var s=t(424),a=t.n(s),i=t(5208);async function n(){try{let e=await (0,i.UL)(),r=e.get("auth_token")?.value;if(!r)return null;return a().verify(r,process.env.JWT_SECRET||"development-secret-key")}catch(e){return console.error("Token verification failed:",e),null}}function u(){return crypto.randomUUID()}function o(){return new Date().toISOString()}class d{constructor(){this.data=new Map,this.data.set("users",[]),this.data.set("bank_accounts",[]),this.data.set("campaigns",[]),this.data.set("promotions",[]),this.data.set("transactions",[]),this.data.set("wallets",[])}prepare(e){return{bind:(...r)=>({first:async()=>e.includes("SELECT")&&e.includes("users")&&e.includes("email")&&(this.data.get("users")||[]).find(e=>e.email===r[0])||null,all:async()=>e.includes("SELECT")&&e.includes("users")?{results:this.data.get("users")||[]}:{results:[]},run:async()=>{if(e.includes("INSERT INTO users")){let e=this.data.get("users")||[],t={id:r[0],email:r[1],name:r[2],password_hash:r[3],role:r[4],bio:r[5]||null,created_at:o(),updated_at:o(),is_active:!0};return e.push(t),this.data.set("users",e),{success:!0,meta:{changes:1}}}return{success:!0,meta:{changes:0}}}})}}async exec(e){return{success:!0}}}let c=new d,p={async create(e){let r=u(),t=o(),s={...e,id:r,created_at:t,updated_at:t};return await c.prepare(`
      INSERT INTO users (id, email, name, password_hash, role, bio, created_at, updated_at, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(s.id,s.email,s.name,s.password_hash,s.role,s.bio,s.created_at,s.updated_at,s.is_active).run(),s},findByEmail:async e=>await c.prepare(`
      SELECT * FROM users WHERE email = ? AND is_active = TRUE
    `).bind(e).first(),findById:async e=>await c.prepare(`
      SELECT * FROM users WHERE id = ? AND is_active = TRUE
    `).bind(e).first(),async update(e,r){let t=Object.keys(r).map(e=>`${e} = ?`).join(", "),s=Object.values(r);return s.push(o()),s.push(e),(await c.prepare(`
      UPDATE users SET ${t}, updated_at = ? WHERE id = ?
    `).bind(...s).run()).success}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},7032:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9864:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>d,PUT:()=>c});var a=t(8106),i=t(8819),n=t(2050),u=t(4235),o=t(3252);async function d(e){let r=await (0,o.bw)();if(!r)return u.NextResponse.json({error:"Unauthorized"},{status:401});try{let e=await o.QQ.findById(r.userId);if(!e)return u.NextResponse.json({error:"User not found"},{status:404});return u.NextResponse.json({id:e.id,name:e.name,email:e.email,bio:e.bio,role:e.role})}catch(e){return console.error("Profile fetch error:",e),u.NextResponse.json({error:"Failed to fetch profile"},{status:500})}}async function c(e){let r=await (0,o.bw)();if(!r)return u.NextResponse.json({error:"Unauthorized"},{status:401});try{let{name:t,bio:s}=await e.json();if(!t||0===t.trim().length)return u.NextResponse.json({error:"Name is required"},{status:400});if(!await o.QQ.update(r.userId,{name:t.trim(),bio:s?.trim()||null}))return u.NextResponse.json({error:"Failed to update profile"},{status:500});let a=await o.QQ.findById(r.userId);return u.NextResponse.json({success:!0,user:{id:a.id,name:a.name,email:a.email,bio:a.bio,role:a.role}})}catch(e){return console.error("Profile update error:",e),u.NextResponse.json({error:"Failed to update profile"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/user/profile/route",pathname:"/api/user/profile",filename:"route",bundlePath:"app/api/user/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\api\\user\\profile\\route.ts",nextConfigOutput:"export",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:f}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,744,208,424],()=>t(9864));module.exports=s})();
{"name": "printable-characters", "version": "1.0.33", "lockfileVersion": 1, "requires": true, "dependencies": {"abbrev": {"version": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz", "integrity": "sha1-kbR5JYinc4wl813W9jdSovh3YTU=", "dev": true}, "acorn": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/acorn/-/acorn-5.1.2.tgz", "integrity": "sha512-o96FZLJBPY1lvTuJylGA9Bk3t/GKPPJG8H0ydQQl01crzwJgspa4AEIq/pVTXigmK0PHVQhiAtn8WMBLL9D2WA==", "dev": true}, "acorn-jsx": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-3.0.1.tgz", "integrity": "sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s=", "dev": true, "requires": {"acorn": "3.3.0"}, "dependencies": {"acorn": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-3.3.0.tgz", "integrity": "sha1-ReN/s56No/JbruP/U2niu18iAXo=", "dev": true}}}, "ajv": {"version": "5.2.3", "resolved": "https://registry.npmjs.org/ajv/-/ajv-5.2.3.tgz", "integrity": "sha1-wG9Zh3jETGsWGrr+NGa4GtGBTtI=", "dev": true, "requires": {"co": "4.6.0", "fast-deep-equal": "1.0.0", "json-schema-traverse": "0.3.1", "json-stable-stringify": "1.0.1"}}, "ajv-keywords": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.1.0.tgz", "integrity": "sha1-opbhf3v658HOT34N5T0pyzIWLfA=", "dev": true}, "align-text": {"version": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz", "integrity": "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=", "dev": true, "requires": {"kind-of": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "longest": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz", "repeat-string": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz"}}, "amdefine": {"version": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz", "integrity": "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=", "dev": true}, "ansi-escapes": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.0.0.tgz", "integrity": "sha512-O/klc27mWNUigtv0F8NJWbLF00OcegQalkqKURWdosW08YZKi4m6CnSUSvIZG1otNJbTWhN01Hhz389DW7mvDQ==", "dev": true}, "ansi-regex": {"version": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.0.0.tgz", "integrity": "sha1-xQYbbg74qBd15Q9dZhUb9r83EQc=", "dev": true}, "ansi-styles": {"version": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true}, "anymatch": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-1.3.2.tgz", "integrity": "sha512-0XNayC8lTHQ2OI8aljNCN3sSx6hsr/1+rlcDAotXJR7C1oZZHCNsfpbKwMjRA3Uqb5tF1Rae2oloTr4xpq+WjA==", "dev": true, "optional": true, "requires": {"micromatch": "2.3.11", "normalize-path": "2.1.1"}}, "argparse": {"version": "https://registry.npmjs.org/argparse/-/argparse-1.0.9.tgz", "integrity": "sha1-c9g7wmP4bpf4zE9rrhsOkKfSLIY=", "dev": true, "requires": {"sprintf-js": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"}}, "arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "dev": true, "optional": true, "requires": {"arr-flatten": "1.1.0"}}, "arr-flatten": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==", "dev": true, "optional": true}, "array-union": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz", "integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "dev": true, "requires": {"array-uniq": "1.0.3"}}, "array-uniq": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz", "integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=", "dev": true}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM=", "dev": true, "optional": true}, "arrify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz", "integrity": "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=", "dev": true}, "asn1": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.3.tgz", "integrity": "sha1-2sh4dxPJlmhJ/IGAd36+nB3fO4Y=", "dev": true}, "assert-plus": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz", "integrity": "sha1-104bh+ev/A24qttwIfP+SBAasjQ=", "dev": true}, "async": {"version": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=", "dev": true}, "async-each": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/async-each/-/async-each-1.0.1.tgz", "integrity": "sha1-GdOGodntxufByF04iu28xW0zYC0=", "dev": true, "optional": true}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "dev": true}, "aws-sign2": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.6.0.tgz", "integrity": "sha1-FDQt0428yU0OW4fXY81jYSwOeU8=", "dev": true}, "aws4": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.6.0.tgz", "integrity": "sha1-g+9cqGCysy5KDe7e6MdxudtXRx4=", "dev": true}, "babel-cli": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-cli/-/babel-cli-6.26.0.tgz", "integrity": "sha1-UCq1SHTX24itALiHoGODzgPQAvE=", "dev": true, "requires": {"babel-core": "6.26.0", "babel-polyfill": "6.26.0", "babel-register": "6.26.0", "babel-runtime": "6.26.0", "chokidar": "1.7.0", "commander": "https://registry.npmjs.org/commander/-/commander-2.11.0.tgz", "convert-source-map": "1.5.0", "fs-readdir-recursive": "1.0.0", "glob": "7.1.2", "lodash": "4.17.4", "output-file-sync": "1.1.2", "path-is-absolute": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "slash": "1.0.0", "source-map": "0.5.7", "v8flags": "2.1.1"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "babel-code-frame": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-code-frame/-/babel-code-frame-6.26.0.tgz", "integrity": "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=", "dev": true, "requires": {"chalk": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "esutils": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "js-tokens": "3.0.2"}}, "babel-core": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-core/-/babel-core-6.26.0.tgz", "integrity": "sha1-rzL3izGm/O8RnIew/Y2XU/A6C7g=", "dev": true, "requires": {"babel-code-frame": "6.26.0", "babel-generator": "6.26.0", "babel-helpers": "6.24.1", "babel-messages": "6.23.0", "babel-register": "6.26.0", "babel-runtime": "6.26.0", "babel-template": "6.26.0", "babel-traverse": "6.26.0", "babel-types": "6.26.0", "babylon": "6.18.0", "convert-source-map": "1.5.0", "debug": "2.6.9", "json5": "0.5.1", "lodash": "4.17.4", "minimatch": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "path-is-absolute": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "private": "0.1.7", "slash": "1.0.0", "source-map": "0.5.7"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "babel-generator": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-generator/-/babel-generator-6.26.0.tgz", "integrity": "sha1-rBriAHC3n248odMmlhMFN3TyDcU=", "dev": true, "requires": {"babel-messages": "6.23.0", "babel-runtime": "6.26.0", "babel-types": "6.26.0", "detect-indent": "4.0.0", "jsesc": "1.3.0", "lodash": "4.17.4", "source-map": "0.5.7", "trim-right": "1.0.1"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "babel-helpers": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-helpers/-/babel-helpers-6.24.1.tgz", "integrity": "sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=", "dev": true, "requires": {"babel-runtime": "6.26.0", "babel-template": "6.26.0"}}, "babel-messages": {"version": "6.23.0", "resolved": "https://registry.npmjs.org/babel-messages/-/babel-messages-6.23.0.tgz", "integrity": "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=", "dev": true, "requires": {"babel-runtime": "6.26.0"}}, "babel-plugin-transform-es2015-block-scoped-functions": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoped-functions/-/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz", "integrity": "sha1-u8UbSflk1wy42OC5ToICRs46YUE=", "dev": true, "requires": {"babel-runtime": "6.26.0"}}, "babel-plugin-transform-es2015-destructuring": {"version": "6.23.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.23.0.tgz", "integrity": "sha1-mXux8auWf2gtKwh2/jWNYOdlxW0=", "dev": true, "requires": {"babel-runtime": "6.26.0"}}, "babel-polyfill": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-polyfill/-/babel-polyfill-6.26.0.tgz", "integrity": "sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM=", "dev": true, "requires": {"babel-runtime": "6.26.0", "core-js": "2.5.1", "regenerator-runtime": "0.10.5"}, "dependencies": {"regenerator-runtime": {"version": "0.10.5", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.5.tgz", "integrity": "sha1-M2w+/BIgrc7dosn6tntaeVWjNlg=", "dev": true}}}, "babel-register": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-register/-/babel-register-6.26.0.tgz", "integrity": "sha1-btAhFz4vy0htestFxgCahW9kcHE=", "dev": true, "requires": {"babel-core": "6.26.0", "babel-runtime": "6.26.0", "core-js": "2.5.1", "home-or-tmp": "2.0.0", "lodash": "4.17.4", "mkdirp": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "source-map-support": "0.4.18"}}, "babel-runtime": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz", "integrity": "sha1-llxwWGaOgrVde/4E/yM3vItWR/4=", "dev": true, "requires": {"core-js": "2.5.1", "regenerator-runtime": "0.11.0"}}, "babel-template": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-template/-/babel-template-6.26.0.tgz", "integrity": "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=", "dev": true, "requires": {"babel-runtime": "6.26.0", "babel-traverse": "6.26.0", "babel-types": "6.26.0", "babylon": "6.18.0", "lodash": "4.17.4"}}, "babel-traverse": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-traverse/-/babel-traverse-6.26.0.tgz", "integrity": "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=", "dev": true, "requires": {"babel-code-frame": "6.26.0", "babel-messages": "6.23.0", "babel-runtime": "6.26.0", "babel-types": "6.26.0", "babylon": "6.18.0", "debug": "2.6.9", "globals": "9.18.0", "invariant": "2.2.2", "lodash": "4.17.4"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}}}, "babel-types": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-types/-/babel-types-6.26.0.tgz", "integrity": "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=", "dev": true, "requires": {"babel-runtime": "6.26.0", "esutils": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "lodash": "4.17.4", "to-fast-properties": "1.0.3"}}, "babylon": {"version": "6.18.0", "resolved": "https://registry.npmjs.org/babylon/-/babylon-6.18.0.tgz", "integrity": "sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ==", "dev": true}, "balanced-match": {"version": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true}, "bcrypt-pbkdf": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz", "integrity": "sha1-Y7xdy2EzG5K8Bf1SiVPDNGKgb40=", "dev": true, "optional": true, "requires": {"tweetnacl": "0.14.5"}}, "binary-extensions": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.10.0.tgz", "integrity": "sha1-muuabF6IY4qtFx4Wf1kAq+JINdA=", "dev": true, "optional": true}, "boom": {"version": "2.10.1", "resolved": "https://registry.npmjs.org/boom/-/boom-2.10.1.tgz", "integrity": "sha1-OciRjO/1eZ+D+UkqhI9iWt0Mdm8=", "dev": true, "requires": {"hoek": "2.16.3"}}, "brace-expansion": {"version": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.8.tgz", "integrity": "sha1-wHshHHyVLsH479Uad+8NHTmQopI=", "dev": true, "requires": {"balanced-match": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "concat-map": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"}}, "braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "dev": true, "optional": true, "requires": {"expand-range": "1.8.2", "preserve": "0.2.0", "repeat-element": "1.1.2"}}, "browser-stdout": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/browser-stdout/-/browser-stdout-1.3.0.tgz", "integrity": "sha1-81HTKWnTL6XXpVZxVCY9korjvR8=", "dev": true}, "caller-path": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/caller-path/-/caller-path-0.1.0.tgz", "integrity": "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=", "dev": true, "requires": {"callsites": "0.2.0"}}, "callsites": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-0.2.0.tgz", "integrity": "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=", "dev": true}, "camelcase": {"version": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "integrity": "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=", "dev": true, "optional": true}, "caseless": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.11.0.tgz", "integrity": "sha1-cVuW6phBWTzDMGeSP17GDr2k99c=", "dev": true}, "center-align": {"version": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz", "integrity": "sha1-qg0yYptu6XIgBBHL1EYckHvCt60=", "dev": true, "optional": true, "requires": {"align-text": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz", "lazy-cache": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz"}}, "chalk": {"version": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "dev": true, "requires": {"ansi-styles": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "escape-string-regexp": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "has-ansi": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "strip-ansi": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "supports-color": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz"}}, "chokidar": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-1.7.0.tgz", "integrity": "sha1-eY5ol3gVHIB2tLNg5e3SjNortGg=", "dev": true, "optional": true, "requires": {"anymatch": "1.3.2", "async-each": "1.0.1", "fsevents": "1.1.2", "glob-parent": "2.0.0", "inherits": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "is-binary-path": "1.0.1", "is-glob": "2.0.1", "path-is-absolute": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "readdirp": "2.1.0"}}, "circular-json": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/circular-json/-/circular-json-0.3.3.tgz", "integrity": "sha512-UZK3NBx2Mca+b5LsG7bY183pHWt5Y1xts4P3Pz7ENTwGVnJOUWbRb3ocjvX7hx9tq/yTAdclXm9sZ38gNuem4A==", "dev": true}, "cli-cursor": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "dev": true, "requires": {"restore-cursor": "2.0.0"}}, "cli-width": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/cli-width/-/cli-width-2.2.0.tgz", "integrity": "sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=", "dev": true}, "cliui": {"version": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz", "integrity": "sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=", "dev": true, "optional": true, "requires": {"center-align": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz", "right-align": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz", "wordwrap": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz"}, "dependencies": {"wordwrap": {"version": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "integrity": "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=", "dev": true, "optional": true}}}, "co": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "dev": true}, "color-convert": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.0.tgz", "integrity": "sha1-Gsz5fdc5uYO/mU1W/sj5WFNkG3o=", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "combined-stream": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.5.tgz", "integrity": "sha1-k4NwpXtKUd6ix3wV1cX9+JUWQAk=", "dev": true, "requires": {"delayed-stream": "1.0.0"}}, "commander": {"version": "https://registry.npmjs.org/commander/-/commander-2.11.0.tgz", "integrity": "sha1-FXFS/R56bI2YpbcVzzdt+SgARWM=", "dev": true}, "concat-map": {"version": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "concat-stream": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.0.tgz", "integrity": "sha1-CqxmL9Ur54lk1VMvaUeE5wEQrPc=", "dev": true, "requires": {"inherits": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "readable-stream": "2.3.3", "typedarray": "0.0.6"}}, "convert-source-map": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.5.0.tgz", "integrity": "sha1-ms1whRxtXf3ZPZKC5e35SgP/RrU=", "dev": true}, "core-js": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/core-js/-/core-js-2.5.1.tgz", "integrity": "sha1-rmh03GaTd4m4B1T/VCjfZoGcpQs=", "dev": true}, "core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true}, "coveralls": {"version": "2.13.3", "resolved": "https://registry.npmjs.org/coveralls/-/coveralls-2.13.3.tgz", "integrity": "sha512-iiAmn+l1XqRwNLXhW8Rs5qHZRFMYp9ZIPjEOVRpC/c4so6Y/f4/lFi0FfR5B9cCqgyhkJ5cZmbvcVRfP8MHchw==", "dev": true, "requires": {"js-yaml": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.6.1.tgz", "lcov-parse": "0.0.10", "log-driver": "1.2.5", "minimist": "1.2.0", "request": "2.79.0"}}, "cross-spawn": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz", "integrity": "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=", "dev": true, "requires": {"lru-cache": "4.1.1", "shebang-command": "1.2.0", "which": "https://registry.npmjs.org/which/-/which-1.2.14.tgz"}}, "cryptiles": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-2.0.5.tgz", "integrity": "sha1-O9/s3GCBR8HGcgL6KR59ylnqo7g=", "dev": true, "requires": {"boom": "2.10.1"}}, "dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "dev": true, "requires": {"assert-plus": "1.0.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true}}}, "debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "dev": true, "requires": {"ms": "2.0.0"}}, "decamelize": {"version": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true, "optional": true}, "deep-is": {"version": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz", "integrity": "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=", "dev": true}, "del": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/del/-/del-2.2.2.tgz", "integrity": "sha1-wSyYHQZ4RshLyvhiz/kw2Qf/0ag=", "dev": true, "requires": {"globby": "5.0.0", "is-path-cwd": "1.0.0", "is-path-in-cwd": "1.0.0", "object-assign": "4.1.1", "pify": "2.3.0", "pinkie-promise": "2.0.1", "rimraf": "2.6.2"}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "dev": true}, "detect-indent": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/detect-indent/-/detect-indent-4.0.0.tgz", "integrity": "sha1-920GQ1LN9Docts5hnE7jqUdd4gg=", "dev": true, "requires": {"repeating": "2.0.1"}}, "diff": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/diff/-/diff-3.2.0.tgz", "integrity": "sha1-yc45Okt8vQsFinJck98pkCeGj/k=", "dev": true}, "doctrine": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-2.0.0.tgz", "integrity": "sha1-xz2NKQnSIpHhoAejlYBNqLZl/mM=", "dev": true, "requires": {"esutils": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "isarray": "1.0.0"}}, "ecc-jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz", "integrity": "sha1-D8c6ntXw1Tw4GTOYUj735UN3dQU=", "dev": true, "optional": true, "requires": {"jsbn": "0.1.1"}}, "escape-string-regexp": {"version": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true}, "escodegen": {"version": "https://registry.npmjs.org/escodegen/-/escodegen-1.8.1.tgz", "integrity": "sha1-WltTr0aTEQvrsIZ6o0MN07cKEBg=", "dev": true, "requires": {"esprima": "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz", "estraverse": "https://registry.npmjs.org/estraverse/-/estraverse-1.9.3.tgz", "esutils": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "optionator": "https://registry.npmjs.org/optionator/-/optionator-0.8.2.tgz", "source-map": "https://registry.npmjs.org/source-map/-/source-map-0.2.0.tgz"}, "dependencies": {"estraverse": {"version": "https://registry.npmjs.org/estraverse/-/estraverse-1.9.3.tgz", "integrity": "sha1-r2fy3JIlgkFZUJJgkaQAXSnJu0Q=", "dev": true}}}, "eslint": {"version": "4.8.0", "resolved": "https://registry.npmjs.org/eslint/-/eslint-4.8.0.tgz", "integrity": "sha1-Ip7w41Tg5h2DfHqA/fuoJeGZgV4=", "dev": true, "requires": {"ajv": "5.2.3", "babel-code-frame": "6.26.0", "chalk": "2.1.0", "concat-stream": "1.6.0", "cross-spawn": "5.1.0", "debug": "3.1.0", "doctrine": "2.0.0", "eslint-scope": "3.7.1", "espree": "3.5.1", "esquery": "1.0.0", "estraverse": "4.2.0", "esutils": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "file-entry-cache": "2.0.0", "functional-red-black-tree": "1.0.1", "glob": "7.1.2", "globals": "9.18.0", "ignore": "3.3.5", "imurmurhash": "0.1.4", "inquirer": "3.3.0", "is-resolvable": "1.0.0", "js-yaml": "3.10.0", "json-stable-stringify": "1.0.1", "levn": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "lodash": "4.17.4", "minimatch": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "mkdirp": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "natural-compare": "1.4.0", "optionator": "https://registry.npmjs.org/optionator/-/optionator-0.8.2.tgz", "path-is-inside": "1.0.2", "pluralize": "7.0.0", "progress": "2.0.0", "require-uncached": "1.0.3", "semver": "5.4.1", "strip-ansi": "4.0.0", "strip-json-comments": "2.0.1", "table": "4.0.2", "text-table": "0.2.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "ansi-styles": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.0.tgz", "integrity": "sha512-NnSOmMEYtVR2JVMIGTzynRkkaxtiq1xnFBcdQD/DnNCYPoEPsVJhM98BDyaoNOQIi7p4okdi3E27eN7GQbsUug==", "dev": true, "requires": {"color-convert": "1.9.0"}}, "chalk": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.1.0.tgz", "integrity": "sha512-LUHGS/dge4ujbXMJrnihYMcL4AoOweGnw9Tp3kQuqy1Kx5c1qKjqvMJZ6nVJPMWJtKCTN72ZogH3oeSO9g9rXQ==", "dev": true, "requires": {"ansi-styles": "3.2.0", "escape-string-regexp": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "supports-color": "4.4.0"}}, "esprima": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.0.tgz", "integrity": "sha512-oftTcaMu/EGrEIu904mWteKIv8vMuOgGYo7EhVJJN00R/EED9DCua/xxHRdYnKtcECzVg7xOWhflvJMnqcFZjw==", "dev": true}, "js-yaml": {"version": "3.10.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.10.0.tgz", "integrity": "sha512-O2v52ffjLa9VeM43J4XocZE//WT9N0IiwDa3KSHH7Tu8CtH+1qM8SIZvnsTh6v+4yFy5KUY3BHUVwjpfAWsjIA==", "dev": true, "requires": {"argparse": "https://registry.npmjs.org/argparse/-/argparse-1.0.9.tgz", "esprima": "4.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "3.0.0"}}, "supports-color": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-4.4.0.tgz", "integrity": "sha512-rKC3+DyXWgK0ZLKwmRsrkyHVZAjNkfzeehuFWdGGcqGDTZFH73+RH6S/RDAAxl9GusSjZSUWYLmT9N5pzXFOXQ==", "dev": true, "requires": {"has-flag": "2.0.0"}}}}, "eslint-scope": {"version": "3.7.1", "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-3.7.1.tgz", "integrity": "sha1-PWPD7f2gLgbgGkUq2IyqzHzctug=", "dev": true, "requires": {"esrecurse": "4.2.0", "estraverse": "4.2.0"}}, "espree": {"version": "3.5.1", "resolved": "https://registry.npmjs.org/espree/-/espree-3.5.1.tgz", "integrity": "sha1-DJiLirRttTEAoZVK5LqZXd0n2H4=", "dev": true, "requires": {"acorn": "5.1.2", "acorn-jsx": "3.0.1"}}, "esprima": {"version": "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz", "integrity": "sha1-luO3DVd59q1JzQMmc9HDEnZ7pYE=", "dev": true}, "esquery": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/esquery/-/esquery-1.0.0.tgz", "integrity": "sha1-z7qLV9f7qT8XKYqKAGoEzaE9gPo=", "dev": true, "requires": {"estraverse": "4.2.0"}}, "esrecurse": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.0.tgz", "integrity": "sha1-+pVo2Y04I/mkHZHpAtyrnqblsWM=", "dev": true, "requires": {"estraverse": "4.2.0", "object-assign": "4.1.1"}}, "estraverse": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM=", "dev": true}, "esutils": {"version": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "integrity": "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs=", "dev": true}, "expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "dev": true, "optional": true, "requires": {"is-posix-bracket": "0.1.1"}}, "expand-range": {"version": "1.8.2", "resolved": "https://registry.npmjs.org/expand-range/-/expand-range-1.8.2.tgz", "integrity": "sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=", "dev": true, "optional": true, "requires": {"fill-range": "2.2.3"}}, "extend": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.1.tgz", "integrity": "sha1-p1Xqe8Gt/MWjHOfnYtuq3F5jZEQ=", "dev": true}, "external-editor": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/external-editor/-/external-editor-2.0.5.tgz", "integrity": "sha512-Msjo64WT5W+NhOpQXh0nOHm+n0RfU1QUwDnKYvJ8dEJ8zlwLrqXNTv5mSUTJpepf41PDJGyhueTw2vNZW+Fr/w==", "dev": true, "requires": {"iconv-lite": "0.4.19", "jschardet": "1.5.1", "tmp": "0.0.33"}}, "extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "dev": true, "optional": true, "requires": {"is-extglob": "1.0.0"}}, "extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "dev": true}, "fast-deep-equal": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-1.0.0.tgz", "integrity": "sha1-liVqO8l1WV6zbYLpkp0GDYk0Of8=", "dev": true}, "fast-levenshtein": {"version": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true}, "figures": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/figures/-/figures-2.0.0.tgz", "integrity": "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=", "dev": true, "requires": {"escape-string-regexp": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"}}, "file-entry-cache": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-2.0.0.tgz", "integrity": "sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E=", "dev": true, "requires": {"flat-cache": "1.3.0", "object-assign": "4.1.1"}}, "filename-regex": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/filename-regex/-/filename-regex-2.0.1.tgz", "integrity": "sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY=", "dev": true, "optional": true}, "fill-range": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-2.2.3.tgz", "integrity": "sha1-ULd9/X5Gm8dJJHCWNpn+eoSFpyM=", "dev": true, "optional": true, "requires": {"is-number": "2.1.0", "isobject": "2.1.0", "randomatic": "1.1.7", "repeat-element": "1.1.2", "repeat-string": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz"}}, "flat-cache": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-1.3.0.tgz", "integrity": "sha1-0wMLMrOBVPTjt+nHCfSQ9++XxIE=", "dev": true, "requires": {"circular-json": "0.3.3", "del": "2.2.2", "graceful-fs": "4.1.11", "write": "0.2.1"}}, "for-in": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true, "optional": true}, "for-own": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/for-own/-/for-own-0.1.5.tgz", "integrity": "sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=", "dev": true, "optional": true, "requires": {"for-in": "1.0.2"}}, "forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "dev": true}, "form-data": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.1.4.tgz", "integrity": "sha1-M8GDrPGTJ27KqYFDpp6Uv+4XUNE=", "dev": true, "requires": {"asynckit": "0.4.0", "combined-stream": "1.0.5", "mime-types": "2.1.17"}}, "fs-readdir-recursive": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs-readdir-recursive/-/fs-readdir-recursive-1.0.0.tgz", "integrity": "sha1-jNF0XItPiinIyuw5JHaSG6GV9WA=", "dev": true}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "fsevents": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-1.1.2.tgz", "integrity": "sha512-Sn44E5wQW4bTHXvQmvSHwqbuiXtduD6Rrjm2ZtUEGbyrig+nUH3t/QD4M4/ZXViY556TBpRgZkHLDx3JxPwxiw==", "dev": true, "optional": true, "requires": {"nan": "2.7.0", "node-pre-gyp": "0.6.36"}, "dependencies": {"abbrev": {"version": "1.1.0", "bundled": true, "dev": true, "optional": true}, "ajv": {"version": "4.11.8", "bundled": true, "dev": true, "optional": true, "requires": {"co": "4.6.0", "json-stable-stringify": "1.0.1"}}, "ansi-regex": {"version": "2.1.1", "bundled": true, "dev": true}, "aproba": {"version": "1.1.1", "bundled": true, "dev": true, "optional": true}, "are-we-there-yet": {"version": "1.1.4", "bundled": true, "dev": true, "optional": true, "requires": {"delegates": "1.0.0", "readable-stream": "2.2.9"}}, "asn1": {"version": "0.2.3", "bundled": true, "dev": true, "optional": true}, "assert-plus": {"version": "0.2.0", "bundled": true, "dev": true, "optional": true}, "asynckit": {"version": "0.4.0", "bundled": true, "dev": true, "optional": true}, "aws-sign2": {"version": "0.6.0", "bundled": true, "dev": true, "optional": true}, "aws4": {"version": "1.6.0", "bundled": true, "dev": true, "optional": true}, "balanced-match": {"version": "0.4.2", "bundled": true, "dev": true}, "bcrypt-pbkdf": {"version": "1.0.1", "bundled": true, "dev": true, "optional": true, "requires": {"tweetnacl": "0.14.5"}}, "block-stream": {"version": "0.0.9", "bundled": true, "dev": true, "requires": {"inherits": "2.0.3"}}, "boom": {"version": "2.10.1", "bundled": true, "dev": true, "requires": {"hoek": "2.16.3"}}, "brace-expansion": {"version": "1.1.7", "bundled": true, "dev": true, "requires": {"balanced-match": "0.4.2", "concat-map": "0.0.1"}}, "buffer-shims": {"version": "1.0.0", "bundled": true, "dev": true}, "caseless": {"version": "0.12.0", "bundled": true, "dev": true, "optional": true}, "co": {"version": "4.6.0", "bundled": true, "dev": true, "optional": true}, "code-point-at": {"version": "1.1.0", "bundled": true, "dev": true}, "combined-stream": {"version": "1.0.5", "bundled": true, "dev": true, "requires": {"delayed-stream": "1.0.0"}}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true}, "console-control-strings": {"version": "1.1.0", "bundled": true, "dev": true}, "core-util-is": {"version": "1.0.2", "bundled": true, "dev": true}, "cryptiles": {"version": "2.0.5", "bundled": true, "dev": true, "optional": true, "requires": {"boom": "2.10.1"}}, "dashdash": {"version": "1.14.1", "bundled": true, "dev": true, "optional": true, "requires": {"assert-plus": "1.0.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}}}, "debug": {"version": "2.6.8", "bundled": true, "dev": true, "optional": true, "requires": {"ms": "2.0.0"}}, "deep-extend": {"version": "0.4.2", "bundled": true, "dev": true, "optional": true}, "delayed-stream": {"version": "1.0.0", "bundled": true, "dev": true}, "delegates": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "ecc-jsbn": {"version": "0.1.1", "bundled": true, "dev": true, "optional": true, "requires": {"jsbn": "0.1.1"}}, "extend": {"version": "3.0.1", "bundled": true, "dev": true, "optional": true}, "extsprintf": {"version": "1.0.2", "bundled": true, "dev": true}, "forever-agent": {"version": "0.6.1", "bundled": true, "dev": true, "optional": true}, "form-data": {"version": "2.1.4", "bundled": true, "dev": true, "optional": true, "requires": {"asynckit": "0.4.0", "combined-stream": "1.0.5", "mime-types": "2.1.15"}}, "fs.realpath": {"version": "1.0.0", "bundled": true, "dev": true}, "fstream": {"version": "1.0.11", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.11", "inherits": "2.0.3", "mkdirp": "0.5.1", "rimraf": "2.6.1"}}, "fstream-ignore": {"version": "1.0.5", "bundled": true, "dev": true, "optional": true, "requires": {"fstream": "1.0.11", "inherits": "2.0.3", "minimatch": "3.0.4"}}, "gauge": {"version": "2.7.4", "bundled": true, "dev": true, "optional": true, "requires": {"aproba": "1.1.1", "console-control-strings": "1.1.0", "has-unicode": "2.0.1", "object-assign": "4.1.1", "signal-exit": "3.0.2", "string-width": "1.0.2", "strip-ansi": "3.0.1", "wide-align": "1.1.2"}}, "getpass": {"version": "0.1.7", "bundled": true, "dev": true, "optional": true, "requires": {"assert-plus": "1.0.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}}}, "glob": {"version": "7.1.2", "bundled": true, "dev": true, "requires": {"fs.realpath": "1.0.0", "inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "3.0.4", "once": "1.4.0", "path-is-absolute": "1.0.1"}}, "graceful-fs": {"version": "4.1.11", "bundled": true, "dev": true}, "har-schema": {"version": "1.0.5", "bundled": true, "dev": true, "optional": true}, "har-validator": {"version": "4.2.1", "bundled": true, "dev": true, "optional": true, "requires": {"ajv": "4.11.8", "har-schema": "1.0.5"}}, "has-unicode": {"version": "2.0.1", "bundled": true, "dev": true, "optional": true}, "hawk": {"version": "3.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"boom": "2.10.1", "cryptiles": "2.0.5", "hoek": "2.16.3", "sntp": "1.0.9"}}, "hoek": {"version": "2.16.3", "bundled": true, "dev": true}, "http-signature": {"version": "1.1.1", "bundled": true, "dev": true, "optional": true, "requires": {"assert-plus": "0.2.0", "jsprim": "1.4.0", "sshpk": "1.13.0"}}, "inflight": {"version": "1.0.6", "bundled": true, "dev": true, "requires": {"once": "1.4.0", "wrappy": "1.0.2"}}, "inherits": {"version": "2.0.3", "bundled": true, "dev": true}, "ini": {"version": "1.3.4", "bundled": true, "dev": true, "optional": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"number-is-nan": "1.0.1"}}, "is-typedarray": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "isarray": {"version": "1.0.0", "bundled": true, "dev": true}, "isstream": {"version": "0.1.2", "bundled": true, "dev": true, "optional": true}, "jodid25519": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true, "requires": {"jsbn": "0.1.1"}}, "jsbn": {"version": "0.1.1", "bundled": true, "dev": true, "optional": true}, "json-schema": {"version": "0.2.3", "bundled": true, "dev": true, "optional": true}, "json-stable-stringify": {"version": "1.0.1", "bundled": true, "dev": true, "optional": true, "requires": {"jsonify": "0.0.0"}}, "json-stringify-safe": {"version": "5.0.1", "bundled": true, "dev": true, "optional": true}, "jsonify": {"version": "0.0.0", "bundled": true, "dev": true, "optional": true}, "jsprim": {"version": "1.4.0", "bundled": true, "dev": true, "optional": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.0.2", "json-schema": "0.2.3", "verror": "1.3.6"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}}}, "mime-db": {"version": "1.27.0", "bundled": true, "dev": true}, "mime-types": {"version": "2.1.15", "bundled": true, "dev": true, "requires": {"mime-db": "1.27.0"}}, "minimatch": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"brace-expansion": "1.1.7"}}, "minimist": {"version": "0.0.8", "bundled": true, "dev": true}, "mkdirp": {"version": "0.5.1", "bundled": true, "dev": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.0.0", "bundled": true, "dev": true, "optional": true}, "node-pre-gyp": {"version": "0.6.36", "bundled": true, "dev": true, "optional": true, "requires": {"mkdirp": "0.5.1", "nopt": "4.0.1", "npmlog": "4.1.0", "rc": "1.2.1", "request": "2.81.0", "rimraf": "2.6.1", "semver": "5.3.0", "tar": "2.2.1", "tar-pack": "3.4.0"}}, "nopt": {"version": "4.0.1", "bundled": true, "dev": true, "optional": true, "requires": {"abbrev": "1.1.0", "osenv": "0.1.4"}}, "npmlog": {"version": "4.1.0", "bundled": true, "dev": true, "optional": true, "requires": {"are-we-there-yet": "1.1.4", "console-control-strings": "1.1.0", "gauge": "2.7.4", "set-blocking": "2.0.0"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "dev": true}, "oauth-sign": {"version": "0.8.2", "bundled": true, "dev": true, "optional": true}, "object-assign": {"version": "4.1.1", "bundled": true, "dev": true, "optional": true}, "once": {"version": "1.4.0", "bundled": true, "dev": true, "requires": {"wrappy": "1.0.2"}}, "os-homedir": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "os-tmpdir": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "osenv": {"version": "0.1.4", "bundled": true, "dev": true, "optional": true, "requires": {"os-homedir": "1.0.2", "os-tmpdir": "1.0.2"}}, "path-is-absolute": {"version": "1.0.1", "bundled": true, "dev": true}, "performance-now": {"version": "0.2.0", "bundled": true, "dev": true, "optional": true}, "process-nextick-args": {"version": "1.0.7", "bundled": true, "dev": true}, "punycode": {"version": "1.4.1", "bundled": true, "dev": true, "optional": true}, "qs": {"version": "6.4.0", "bundled": true, "dev": true, "optional": true}, "rc": {"version": "1.2.1", "bundled": true, "dev": true, "optional": true, "requires": {"deep-extend": "0.4.2", "ini": "1.3.4", "minimist": "1.2.0", "strip-json-comments": "2.0.1"}, "dependencies": {"minimist": {"version": "1.2.0", "bundled": true, "dev": true, "optional": true}}}, "readable-stream": {"version": "2.2.9", "bundled": true, "dev": true, "requires": {"buffer-shims": "1.0.0", "core-util-is": "1.0.2", "inherits": "2.0.3", "isarray": "1.0.0", "process-nextick-args": "1.0.7", "string_decoder": "1.0.1", "util-deprecate": "1.0.2"}}, "request": {"version": "2.81.0", "bundled": true, "dev": true, "optional": true, "requires": {"aws-sign2": "0.6.0", "aws4": "1.6.0", "caseless": "0.12.0", "combined-stream": "1.0.5", "extend": "3.0.1", "forever-agent": "0.6.1", "form-data": "2.1.4", "har-validator": "4.2.1", "hawk": "3.1.3", "http-signature": "1.1.1", "is-typedarray": "1.0.0", "isstream": "0.1.2", "json-stringify-safe": "5.0.1", "mime-types": "2.1.15", "oauth-sign": "0.8.2", "performance-now": "0.2.0", "qs": "6.4.0", "safe-buffer": "5.0.1", "stringstream": "0.0.5", "tough-cookie": "2.3.2", "tunnel-agent": "0.6.0", "uuid": "3.0.1"}}, "rimraf": {"version": "2.6.1", "bundled": true, "dev": true, "requires": {"glob": "7.1.2"}}, "safe-buffer": {"version": "5.0.1", "bundled": true, "dev": true}, "semver": {"version": "5.3.0", "bundled": true, "dev": true, "optional": true}, "set-blocking": {"version": "2.0.0", "bundled": true, "dev": true, "optional": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "dev": true, "optional": true}, "sntp": {"version": "1.0.9", "bundled": true, "dev": true, "optional": true, "requires": {"hoek": "2.16.3"}}, "sshpk": {"version": "1.13.0", "bundled": true, "dev": true, "optional": true, "requires": {"asn1": "0.2.3", "assert-plus": "1.0.0", "bcrypt-pbkdf": "1.0.1", "dashdash": "1.14.1", "ecc-jsbn": "0.1.1", "getpass": "0.1.7", "jodid25519": "1.0.2", "jsbn": "0.1.1", "tweetnacl": "0.14.5"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}}}, "string-width": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"code-point-at": "1.1.0", "is-fullwidth-code-point": "1.0.0", "strip-ansi": "3.0.1"}}, "string_decoder": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "5.0.1"}}, "stringstream": {"version": "0.0.5", "bundled": true, "dev": true, "optional": true}, "strip-ansi": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"ansi-regex": "2.1.1"}}, "strip-json-comments": {"version": "2.0.1", "bundled": true, "dev": true, "optional": true}, "tar": {"version": "2.2.1", "bundled": true, "dev": true, "requires": {"block-stream": "0.0.9", "fstream": "1.0.11", "inherits": "2.0.3"}}, "tar-pack": {"version": "3.4.0", "bundled": true, "dev": true, "optional": true, "requires": {"debug": "2.6.8", "fstream": "1.0.11", "fstream-ignore": "1.0.5", "once": "1.4.0", "readable-stream": "2.2.9", "rimraf": "2.6.1", "tar": "2.2.1", "uid-number": "0.0.6"}}, "tough-cookie": {"version": "2.3.2", "bundled": true, "dev": true, "optional": true, "requires": {"punycode": "1.4.1"}}, "tunnel-agent": {"version": "0.6.0", "bundled": true, "dev": true, "optional": true, "requires": {"safe-buffer": "5.0.1"}}, "tweetnacl": {"version": "0.14.5", "bundled": true, "dev": true, "optional": true}, "uid-number": {"version": "0.0.6", "bundled": true, "dev": true, "optional": true}, "util-deprecate": {"version": "1.0.2", "bundled": true, "dev": true}, "uuid": {"version": "3.0.1", "bundled": true, "dev": true, "optional": true}, "verror": {"version": "1.3.6", "bundled": true, "dev": true, "optional": true, "requires": {"extsprintf": "1.0.2"}}, "wide-align": {"version": "1.1.2", "bundled": true, "dev": true, "optional": true, "requires": {"string-width": "1.0.2"}}, "wrappy": {"version": "1.0.2", "bundled": true, "dev": true}}}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "dev": true}, "generate-function": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/generate-function/-/generate-function-2.0.0.tgz", "integrity": "sha1-aFj+fAlpt9TpCTM3ZHrHn2DfvnQ=", "dev": true}, "generate-object-property": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/generate-object-property/-/generate-object-property-1.2.0.tgz", "integrity": "sha1-nA4cQDCM6AT0eDYYuTf6iPmdUNA=", "dev": true, "requires": {"is-property": "1.0.2"}}, "getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "dev": true, "requires": {"assert-plus": "1.0.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true}}}, "glob": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.2.tgz", "integrity": "sha1-wZyd+aAocC1nhhI4SmVSQExjbRU=", "dev": true, "requires": {"fs.realpath": "1.0.0", "inflight": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "minimatch": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "once": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "path-is-absolute": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"}}, "glob-base": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/glob-base/-/glob-base-0.3.0.tgz", "integrity": "sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=", "dev": true, "optional": true, "requires": {"glob-parent": "2.0.0", "is-glob": "2.0.1"}}, "glob-parent": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz", "integrity": "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=", "dev": true, "requires": {"is-glob": "2.0.1"}}, "globals": {"version": "9.18.0", "resolved": "https://registry.npmjs.org/globals/-/globals-9.18.0.tgz", "integrity": "sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ==", "dev": true}, "globby": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/globby/-/globby-5.0.0.tgz", "integrity": "sha1-69hGZ8oNuzMLmbz8aOrCvFQ3Dg0=", "dev": true, "requires": {"array-union": "1.0.2", "arrify": "1.0.1", "glob": "7.1.2", "object-assign": "4.1.1", "pify": "2.3.0", "pinkie-promise": "2.0.1"}}, "graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true}, "graceful-readlink": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/graceful-readlink/-/graceful-readlink-1.0.1.tgz", "integrity": "sha1-TK+tdrxi8C+gObL5Tpo906ORpyU=", "dev": true}, "growl": {"version": "1.9.2", "resolved": "https://registry.npmjs.org/growl/-/growl-1.9.2.tgz", "integrity": "sha1-Dqd0NxXbjY3ixe3hd14bRayFwC8=", "dev": true}, "handlebars": {"version": "https://registry.npmjs.org/handlebars/-/handlebars-4.0.10.tgz", "integrity": "sha1-PTDHGLCaPZbyPqTMH0A8TTup/08=", "dev": true, "requires": {"async": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "optimist": "https://registry.npmjs.org/optimist/-/optimist-0.6.1.tgz", "source-map": "https://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "uglify-js": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz"}, "dependencies": {"source-map": {"version": "https://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "integrity": "sha1-66T12pwNyZneaAMti092FzZSA2s=", "dev": true, "requires": {"amdefine": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz"}}}}, "har-validator": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.6.tgz", "integrity": "sha1-zcvAgYgmWtEZtqWnyKtw7s+10n0=", "dev": true, "requires": {"chalk": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "commander": "https://registry.npmjs.org/commander/-/commander-2.11.0.tgz", "is-my-json-valid": "2.16.1", "pinkie-promise": "2.0.1"}}, "has-ansi": {"version": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "dev": true, "requires": {"ansi-regex": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.0.0.tgz"}}, "has-flag": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-2.0.0.tgz", "integrity": "sha1-6CB68cx7MNRGzHC3NLXovhj4jVE=", "dev": true}, "hawk": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/hawk/-/hawk-3.1.3.tgz", "integrity": "sha1-B4REvXwWQLD+VA0sm3PVlnjo4cQ=", "dev": true, "requires": {"boom": "2.10.1", "cryptiles": "2.0.5", "hoek": "2.16.3", "sntp": "1.0.9"}}, "he": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/he/-/he-1.1.1.tgz", "integrity": "sha1-k0EP0hsAlzUVH4howvJx80J+I/0=", "dev": true}, "hoek": {"version": "2.16.3", "resolved": "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz", "integrity": "sha1-ILt0A9POo5jpHcRxCo/xuCdKJe0=", "dev": true}, "home-or-tmp": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/home-or-tmp/-/home-or-tmp-2.0.0.tgz", "integrity": "sha1-42w/LSyufXRqhX440Y1fMqeILbg=", "dev": true, "requires": {"os-homedir": "1.0.2", "os-tmpdir": "1.0.2"}}, "http-signature": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.1.1.tgz", "integrity": "sha1-33LiZwZs0Kxn+3at+OE0qPvPkb8=", "dev": true, "requires": {"assert-plus": "0.2.0", "jsprim": "1.4.1", "sshpk": "1.13.1"}}, "iconv-lite": {"version": "0.4.19", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.19.tgz", "integrity": "sha512-oTZqweIP51xaGPI4uPa56/Pri/480R+mo7SeU+YETByQNhDG55ycFyNLIgta9vXhILrxXDmF7ZGhqZIcuN0gJQ==", "dev": true}, "ignore": {"version": "3.3.5", "resolved": "https://registry.npmjs.org/ignore/-/ignore-3.3.5.tgz", "integrity": "sha512-JLH93mL8amZQhh/p6mfQgVBH3M6epNq3DfsXsTSuSrInVjwyYlFE1nv2AgfRCC8PoOhM0jwQ5v8s9LgbK7yGDw==", "dev": true}, "imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "inflight": {"version": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "requires": {"once": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "wrappy": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"}}, "inherits": {"version": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true}, "inquirer": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/inquirer/-/inquirer-3.3.0.tgz", "integrity": "sha512-h+xtnyk4EwKvFWHrUYsWErEVR+igKtLdchu+o0Z1RL7VU/jVMFbYir2bp6bAj8efFNxWqHX0dIss6fJQ+/+qeQ==", "dev": true, "requires": {"ansi-escapes": "3.0.0", "chalk": "2.1.0", "cli-cursor": "2.1.0", "cli-width": "2.2.0", "external-editor": "2.0.5", "figures": "2.0.0", "lodash": "4.17.4", "mute-stream": "0.0.7", "run-async": "2.3.0", "rx-lite": "4.0.8", "rx-lite-aggregates": "4.0.8", "string-width": "2.1.1", "strip-ansi": "4.0.0", "through": "2.3.8"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "ansi-styles": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.0.tgz", "integrity": "sha512-NnSOmMEYtVR2JVMIGTzynRkkaxtiq1xnFBcdQD/DnNCYPoEPsVJhM98BDyaoNOQIi7p4okdi3E27eN7GQbsUug==", "dev": true, "requires": {"color-convert": "1.9.0"}}, "chalk": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.1.0.tgz", "integrity": "sha512-LUHGS/dge4ujbXMJrnihYMcL4AoOweGnw9Tp3kQuqy1Kx5c1qKjqvMJZ6nVJPMWJtKCTN72ZogH3oeSO9g9rXQ==", "dev": true, "requires": {"ansi-styles": "3.2.0", "escape-string-regexp": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "supports-color": "4.4.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "3.0.0"}}, "supports-color": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-4.4.0.tgz", "integrity": "sha512-rKC3+DyXWgK0ZLKwmRsrkyHVZAjNkfzeehuFWdGGcqGDTZFH73+RH6S/RDAAxl9GusSjZSUWYLmT9N5pzXFOXQ==", "dev": true, "requires": {"has-flag": "2.0.0"}}}}, "invariant": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/invariant/-/invariant-2.2.2.tgz", "integrity": "sha1-nh9WrArNtr8wMwbzOL47IErmA2A=", "dev": true, "requires": {"loose-envify": "1.3.1"}}, "is-binary-path": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "optional": true, "requires": {"binary-extensions": "1.10.0"}}, "is-buffer": {"version": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.5.tgz", "integrity": "sha1-Hzsm72E7IUuIy8ojzGwB2Hlh7sw=", "dev": true}, "is-dotfile": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/is-dotfile/-/is-dotfile-1.0.3.tgz", "integrity": "sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE=", "dev": true, "optional": true}, "is-equal-shallow": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz", "integrity": "sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=", "dev": true, "optional": true, "requires": {"is-primitive": "2.0.0"}}, "is-extendable": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "optional": true}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA=", "dev": true}, "is-finite": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-finite/-/is-finite-1.0.2.tgz", "integrity": "sha1-zGZ3aVYCvlUO8R6LSqYwU0K20Ko=", "dev": true, "requires": {"number-is-nan": "1.0.1"}}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "dev": true, "requires": {"is-extglob": "1.0.0"}}, "is-my-json-valid": {"version": "2.16.1", "resolved": "https://registry.npmjs.org/is-my-json-valid/-/is-my-json-valid-2.16.1.tgz", "integrity": "sha512-ochPsqWS1WXj8ZnMIV0vnNXooaMhp7cyL4FMSIPKTtnV0Ha/T19G2b9kkhcNsabV9bxYkze7/aLZJb/bYuFduQ==", "dev": true, "requires": {"generate-function": "2.0.0", "generate-object-property": "1.2.0", "jsonpointer": "4.0.1", "xtend": "4.0.1"}}, "is-number": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=", "dev": true, "optional": true, "requires": {"kind-of": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"}}, "is-path-cwd": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz", "integrity": "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=", "dev": true}, "is-path-in-cwd": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.0.tgz", "integrity": "sha1-ZHdYK4IU1gI0YJRWcAO+ip6sBNw=", "dev": true, "requires": {"is-path-inside": "1.0.0"}}, "is-path-inside": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.0.tgz", "integrity": "sha1-/AbloWg/vaE95mev9xe7wQpI838=", "dev": true, "requires": {"path-is-inside": "1.0.2"}}, "is-posix-bracket": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz", "integrity": "sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q=", "dev": true, "optional": true}, "is-primitive": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-primitive/-/is-primitive-2.0.0.tgz", "integrity": "sha1-IHurkWOEmcB7Kt8kCkGochADRXU=", "dev": true, "optional": true}, "is-promise": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-2.1.0.tgz", "integrity": "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=", "dev": true}, "is-property": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz", "integrity": "sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=", "dev": true}, "is-resolvable": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.0.0.tgz", "integrity": "sha1-jfV8YeouPFAUCNEA+wE8+NbgzGI=", "dev": true, "requires": {"tryit": "1.0.3"}}, "is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "dev": true}, "isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "isexe": {"version": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "optional": true, "requires": {"isarray": "1.0.0"}}, "isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "dev": true}, "istanbul": {"version": "https://registry.npmjs.org/istanbul/-/istanbul-0.4.5.tgz", "integrity": "sha1-ZcfXPUxNqE1POsMQuRj7C4Azczs=", "dev": true, "requires": {"abbrev": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz", "async": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "escodegen": "https://registry.npmjs.org/escodegen/-/escodegen-1.8.1.tgz", "esprima": "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz", "glob": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "handlebars": "https://registry.npmjs.org/handlebars/-/handlebars-4.0.10.tgz", "js-yaml": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.6.1.tgz", "mkdirp": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "nopt": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz", "once": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "resolve": "https://registry.npmjs.org/resolve/-/resolve-1.1.7.tgz", "supports-color": "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz", "which": "https://registry.npmjs.org/which/-/which-1.2.14.tgz", "wordwrap": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz"}, "dependencies": {"glob": {"version": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "integrity": "sha1-G8k2ueAvSmA/zCIuz3Yz0wuLk7E=", "dev": true, "requires": {"inflight": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "minimatch": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "once": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "path-is-absolute": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"}}, "has-flag": {"version": "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz", "integrity": "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=", "dev": true}, "supports-color": {"version": "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz", "integrity": "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=", "dev": true, "requires": {"has-flag": "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz"}}}}, "js-tokens": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "dev": true}, "js-yaml": {"version": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.6.1.tgz", "integrity": "sha1-bl/mfYsgXOTSL60Ft3geja3MSzA=", "dev": true, "requires": {"argparse": "https://registry.npmjs.org/argparse/-/argparse-1.0.9.tgz", "esprima": "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz"}}, "jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "dev": true, "optional": true}, "jschardet": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/jschardet/-/jschardet-1.5.1.tgz", "integrity": "sha512-vE2hT1D0HLZCLLclfBSfkfTTedhVj0fubHpJBHKwwUWX0nSbhPAfk+SG9rTX95BYNmau8rGFfCeaT6T5OW1C2A==", "dev": true}, "jsesc": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-1.3.0.tgz", "integrity": "sha1-RsP+yMGJKxKwgz25vHYiF226s0s=", "dev": true}, "json-schema": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=", "dev": true}, "json-schema-traverse": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz", "integrity": "sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A=", "dev": true}, "json-stable-stringify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz", "integrity": "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=", "dev": true, "requires": {"jsonify": "0.0.0"}}, "json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "dev": true}, "json3": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/json3/-/json3-3.3.2.tgz", "integrity": "sha1-PAQ0dD35Pi9cQq7nsZvLSDV19OE=", "dev": true}, "json5": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz", "integrity": "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=", "dev": true}, "jsonify": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/jsonify/-/jsonify-0.0.0.tgz", "integrity": "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=", "dev": true}, "jsonpointer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/jsonpointer/-/jsonpointer-4.0.1.tgz", "integrity": "sha1-T9kss04OnbPInIYi7PUfm5eMbLk=", "dev": true}, "jsprim": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "dev": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true}}}, "kind-of": {"version": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.5.tgz"}}, "lazy-cache": {"version": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4=", "dev": true, "optional": true}, "lcov-parse": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/lcov-parse/-/lcov-parse-0.0.10.tgz", "integrity": "sha1-GwuP+ayceIklBYK3C3ExXZ2m2aM=", "dev": true}, "levn": {"version": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "dev": true, "requires": {"prelude-ls": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "type-check": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"}}, "lodash": {"version": "4.17.4", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.4.tgz", "integrity": "sha1-eCA6TRwyiuHYbcpkYONptX9AVa4=", "dev": true}, "lodash._baseassign": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/lodash._baseassign/-/lodash._baseassign-3.2.0.tgz", "integrity": "sha1-jDigmVAPIVrQnlnxci/QxSv+Ck4=", "dev": true, "requires": {"lodash._basecopy": "3.0.1", "lodash.keys": "3.1.2"}}, "lodash._basecopy": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._basecopy/-/lodash._basecopy-3.0.1.tgz", "integrity": "sha1-jaDmqHbPNEwK2KVIghEd08XHyjY=", "dev": true}, "lodash._basecreate": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash._basecreate/-/lodash._basecreate-3.0.3.tgz", "integrity": "sha1-G8ZhYU2qf8MRt9A78WgGoCE8+CE=", "dev": true}, "lodash._getnative": {"version": "3.9.1", "resolved": "https://registry.npmjs.org/lodash._getnative/-/lodash._getnative-3.9.1.tgz", "integrity": "sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U=", "dev": true}, "lodash._isiterateecall": {"version": "3.0.9", "resolved": "https://registry.npmjs.org/lodash._isiterateecall/-/lodash._isiterateecall-3.0.9.tgz", "integrity": "sha1-UgOte6Ql+uhCRg5pbbnPPmqsBXw=", "dev": true}, "lodash.create": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/lodash.create/-/lodash.create-3.1.1.tgz", "integrity": "sha1-1/KEnw29p+BGgruM1yqwIkYd6+c=", "dev": true, "requires": {"lodash._baseassign": "3.2.0", "lodash._basecreate": "3.0.3", "lodash._isiterateecall": "3.0.9"}}, "lodash.isarguments": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz", "integrity": "sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=", "dev": true}, "lodash.isarray": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-3.0.4.tgz", "integrity": "sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U=", "dev": true}, "lodash.keys": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-3.1.2.tgz", "integrity": "sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=", "dev": true, "requires": {"lodash._getnative": "3.9.1", "lodash.isarguments": "3.1.0", "lodash.isarray": "3.0.4"}}, "log-driver": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/log-driver/-/log-driver-1.2.5.tgz", "integrity": "sha1-euTsJXMC/XkNVXyxDJcQDYV7AFY=", "dev": true}, "longest": {"version": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz", "integrity": "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=", "dev": true}, "loose-envify": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.3.1.tgz", "integrity": "sha1-0aitM/qc4OcT1l/dCsi3SNR4yEg=", "dev": true, "requires": {"js-tokens": "3.0.2"}}, "lru-cache": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.1.tgz", "integrity": "sha512-q4spe4KTfsAS1SUHLO0wz8Qiyf1+vMIAgpRYioFYDMNqKfHQbg+AVDH3i4fvpl71/P1L0dBl+fQi+P37UYf0ew==", "dev": true, "requires": {"pseudomap": "1.0.2", "yallist": "2.1.2"}}, "micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "dev": true, "optional": true, "requires": {"arr-diff": "2.0.0", "array-unique": "0.2.1", "braces": "1.8.5", "expand-brackets": "0.1.5", "extglob": "0.3.2", "filename-regex": "2.0.1", "is-extglob": "1.0.0", "is-glob": "2.0.1", "kind-of": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "normalize-path": "2.1.1", "object.omit": "2.0.1", "parse-glob": "3.0.4", "regex-cache": "0.4.4"}}, "mime-db": {"version": "1.30.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.30.0.tgz", "integrity": "sha1-dMZD2i3Z1qRTmZY0ZbJtXKfXHwE=", "dev": true}, "mime-types": {"version": "2.1.17", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.17.tgz", "integrity": "sha1-Cdejk/A+mVp5+K+Fe3Cp4KsWVXo=", "dev": true, "requires": {"mime-db": "1.30.0"}}, "mimic-fn": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.1.0.tgz", "integrity": "sha1-5md4PZLonb00KBi1IwudYqZyrRg=", "dev": true}, "minimatch": {"version": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dev": true, "requires": {"brace-expansion": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.8.tgz"}}, "minimist": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}, "mkdirp": {"version": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "dev": true, "requires": {"minimist": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz"}, "dependencies": {"minimist": {"version": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}}}, "mocha": {"version": "3.5.3", "resolved": "https://registry.npmjs.org/mocha/-/mocha-3.5.3.tgz", "integrity": "sha512-/6na001MJWEtYxHOV1WLfsmR4YIynkUEhBwzsb+fk2qmQ3iqsi258l/Q2MWHJMImAcNpZ8DEdYAK72NHoIQ9Eg==", "dev": true, "requires": {"browser-stdout": "1.3.0", "commander": "2.9.0", "debug": "2.6.8", "diff": "3.2.0", "escape-string-regexp": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "glob": "7.1.1", "growl": "1.9.2", "he": "1.1.1", "json3": "3.3.2", "lodash.create": "3.1.1", "mkdirp": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "supports-color": "3.1.2"}, "dependencies": {"commander": {"version": "2.9.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz", "integrity": "sha1-nJkJQXbhIkDLItbFFGCYQA/g99Q=", "dev": true, "requires": {"graceful-readlink": "1.0.1"}}, "debug": {"version": "2.6.8", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.8.tgz", "integrity": "sha1-5zFTHKLt4n0YgiJCfaF4IdaP9Pw=", "dev": true, "requires": {"ms": "2.0.0"}}, "glob": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.1.tgz", "integrity": "sha1-gFIR3wT6rxxjo2ADBs31reULLsg=", "dev": true, "requires": {"fs.realpath": "1.0.0", "inflight": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "minimatch": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "once": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "path-is-absolute": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"}}, "has-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz", "integrity": "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=", "dev": true}, "supports-color": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-3.1.2.tgz", "integrity": "sha1-cqJiiU2dQIuVbKBf83su2KbiotU=", "dev": true, "requires": {"has-flag": "1.0.0"}}}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "mute-stream": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.7.tgz", "integrity": "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=", "dev": true}, "nan": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/nan/-/nan-2.7.0.tgz", "integrity": "sha1-2Vv3IeyHfgjbJ27T/G63j5CDrUY=", "dev": true, "optional": true}, "natural-compare": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "nopt": {"version": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz", "integrity": "sha1-xkZdvwirzU2zWTF/eaxopkayj/k=", "dev": true, "requires": {"abbrev": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz"}}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "requires": {"remove-trailing-separator": "1.1.0"}}, "number-is-nan": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=", "dev": true}, "nyc": {"version": "11.2.1", "resolved": "https://registry.npmjs.org/nyc/-/nyc-11.2.1.tgz", "integrity": "sha1-rYUK/p261/SXByi0suR/7Rw4chw=", "dev": true, "requires": {"archy": "1.0.0", "arrify": "1.0.1", "caching-transform": "1.0.1", "convert-source-map": "1.5.0", "debug-log": "1.0.1", "default-require-extensions": "1.0.0", "find-cache-dir": "0.1.1", "find-up": "2.1.0", "foreground-child": "1.5.6", "glob": "7.1.2", "istanbul-lib-coverage": "1.1.1", "istanbul-lib-hook": "1.0.7", "istanbul-lib-instrument": "1.8.0", "istanbul-lib-report": "1.1.1", "istanbul-lib-source-maps": "1.2.1", "istanbul-reports": "1.1.2", "md5-hex": "1.3.0", "merge-source-map": "1.0.4", "micromatch": "2.3.11", "mkdirp": "0.5.1", "resolve-from": "2.0.0", "rimraf": "2.6.1", "signal-exit": "3.0.2", "spawn-wrap": "1.3.8", "test-exclude": "4.1.1", "yargs": "8.0.2", "yargs-parser": "5.0.0"}, "dependencies": {"align-text": {"version": "0.1.4", "bundled": true, "dev": true, "requires": {"kind-of": "3.2.2", "longest": "1.0.1", "repeat-string": "1.6.1"}}, "amdefine": {"version": "1.0.1", "bundled": true, "dev": true}, "ansi-regex": {"version": "2.1.1", "bundled": true, "dev": true}, "ansi-styles": {"version": "2.2.1", "bundled": true, "dev": true}, "append-transform": {"version": "0.4.0", "bundled": true, "dev": true, "requires": {"default-require-extensions": "1.0.0"}}, "archy": {"version": "1.0.0", "bundled": true, "dev": true}, "arr-diff": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"arr-flatten": "1.1.0"}}, "arr-flatten": {"version": "1.1.0", "bundled": true, "dev": true}, "array-unique": {"version": "0.2.1", "bundled": true, "dev": true}, "arrify": {"version": "1.0.1", "bundled": true, "dev": true}, "async": {"version": "1.5.2", "bundled": true, "dev": true}, "babel-code-frame": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"chalk": "1.1.3", "esutils": "2.0.2", "js-tokens": "3.0.2"}}, "babel-generator": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"babel-messages": "6.23.0", "babel-runtime": "6.26.0", "babel-types": "6.26.0", "detect-indent": "4.0.0", "jsesc": "1.3.0", "lodash": "4.17.4", "source-map": "0.5.7", "trim-right": "1.0.1"}}, "babel-messages": {"version": "6.23.0", "bundled": true, "dev": true, "requires": {"babel-runtime": "6.26.0"}}, "babel-runtime": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"core-js": "2.5.1", "regenerator-runtime": "0.11.0"}}, "babel-template": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"babel-runtime": "6.26.0", "babel-traverse": "6.26.0", "babel-types": "6.26.0", "babylon": "6.18.0", "lodash": "4.17.4"}}, "babel-traverse": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"babel-code-frame": "6.26.0", "babel-messages": "6.23.0", "babel-runtime": "6.26.0", "babel-types": "6.26.0", "babylon": "6.18.0", "debug": "2.6.8", "globals": "9.18.0", "invariant": "2.2.2", "lodash": "4.17.4"}}, "babel-types": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"babel-runtime": "6.26.0", "esutils": "2.0.2", "lodash": "4.17.4", "to-fast-properties": "1.0.3"}}, "babylon": {"version": "6.18.0", "bundled": true, "dev": true}, "balanced-match": {"version": "1.0.0", "bundled": true, "dev": true}, "brace-expansion": {"version": "1.1.8", "bundled": true, "dev": true, "requires": {"balanced-match": "1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "1.8.5", "bundled": true, "dev": true, "requires": {"expand-range": "1.8.2", "preserve": "0.2.0", "repeat-element": "1.1.2"}}, "builtin-modules": {"version": "1.1.1", "bundled": true, "dev": true}, "caching-transform": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"md5-hex": "1.3.0", "mkdirp": "0.5.1", "write-file-atomic": "1.3.4"}}, "camelcase": {"version": "1.2.1", "bundled": true, "dev": true, "optional": true}, "center-align": {"version": "0.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"align-text": "0.1.4", "lazy-cache": "1.0.4"}}, "chalk": {"version": "1.1.3", "bundled": true, "dev": true, "requires": {"ansi-styles": "2.2.1", "escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "strip-ansi": "3.0.1", "supports-color": "2.0.0"}}, "cliui": {"version": "2.1.0", "bundled": true, "dev": true, "optional": true, "requires": {"center-align": "0.1.3", "right-align": "0.1.3", "wordwrap": "0.0.2"}, "dependencies": {"wordwrap": {"version": "0.0.2", "bundled": true, "dev": true, "optional": true}}}, "code-point-at": {"version": "1.1.0", "bundled": true, "dev": true}, "commondir": {"version": "1.0.1", "bundled": true, "dev": true}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true}, "convert-source-map": {"version": "1.5.0", "bundled": true, "dev": true}, "core-js": {"version": "2.5.1", "bundled": true, "dev": true}, "cross-spawn": {"version": "4.0.2", "bundled": true, "dev": true, "requires": {"lru-cache": "4.1.1", "which": "1.3.0"}}, "debug": {"version": "2.6.8", "bundled": true, "dev": true, "requires": {"ms": "2.0.0"}}, "debug-log": {"version": "1.0.1", "bundled": true, "dev": true}, "decamelize": {"version": "1.2.0", "bundled": true, "dev": true}, "default-require-extensions": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"strip-bom": "2.0.0"}}, "detect-indent": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"repeating": "2.0.1"}}, "error-ex": {"version": "1.3.1", "bundled": true, "dev": true, "requires": {"is-arrayish": "0.2.1"}}, "escape-string-regexp": {"version": "1.0.5", "bundled": true, "dev": true}, "esutils": {"version": "2.0.2", "bundled": true, "dev": true}, "execa": {"version": "0.7.0", "bundled": true, "dev": true, "requires": {"cross-spawn": "5.1.0", "get-stream": "3.0.0", "is-stream": "1.1.0", "npm-run-path": "2.0.2", "p-finally": "1.0.0", "signal-exit": "3.0.2", "strip-eof": "1.0.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "bundled": true, "dev": true, "requires": {"lru-cache": "4.1.1", "shebang-command": "1.2.0", "which": "1.3.0"}}}}, "expand-brackets": {"version": "0.1.5", "bundled": true, "dev": true, "requires": {"is-posix-bracket": "0.1.1"}}, "expand-range": {"version": "1.8.2", "bundled": true, "dev": true, "requires": {"fill-range": "2.2.3"}}, "extglob": {"version": "0.3.2", "bundled": true, "dev": true, "requires": {"is-extglob": "1.0.0"}}, "filename-regex": {"version": "2.0.1", "bundled": true, "dev": true}, "fill-range": {"version": "2.2.3", "bundled": true, "dev": true, "requires": {"is-number": "2.1.0", "isobject": "2.1.0", "randomatic": "1.1.7", "repeat-element": "1.1.2", "repeat-string": "1.6.1"}}, "find-cache-dir": {"version": "0.1.1", "bundled": true, "dev": true, "requires": {"commondir": "1.0.1", "mkdirp": "0.5.1", "pkg-dir": "1.0.0"}}, "find-up": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"locate-path": "2.0.0"}}, "for-in": {"version": "1.0.2", "bundled": true, "dev": true}, "for-own": {"version": "0.1.5", "bundled": true, "dev": true, "requires": {"for-in": "1.0.2"}}, "foreground-child": {"version": "1.5.6", "bundled": true, "dev": true, "requires": {"cross-spawn": "4.0.2", "signal-exit": "3.0.2"}}, "fs.realpath": {"version": "1.0.0", "bundled": true, "dev": true}, "get-caller-file": {"version": "1.0.2", "bundled": true, "dev": true}, "get-stream": {"version": "3.0.0", "bundled": true, "dev": true}, "glob": {"version": "7.1.2", "bundled": true, "dev": true, "requires": {"fs.realpath": "1.0.0", "inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "3.0.4", "once": "1.4.0", "path-is-absolute": "1.0.1"}}, "glob-base": {"version": "0.3.0", "bundled": true, "dev": true, "requires": {"glob-parent": "2.0.0", "is-glob": "2.0.1"}}, "glob-parent": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"is-glob": "2.0.1"}}, "globals": {"version": "9.18.0", "bundled": true, "dev": true}, "graceful-fs": {"version": "4.1.11", "bundled": true, "dev": true}, "handlebars": {"version": "4.0.10", "bundled": true, "dev": true, "requires": {"async": "1.5.2", "optimist": "0.6.1", "source-map": "0.4.4", "uglify-js": "2.8.29"}, "dependencies": {"source-map": {"version": "0.4.4", "bundled": true, "dev": true, "requires": {"amdefine": "1.0.1"}}}}, "has-ansi": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "2.1.1"}}, "has-flag": {"version": "1.0.0", "bundled": true, "dev": true}, "hosted-git-info": {"version": "2.5.0", "bundled": true, "dev": true}, "imurmurhash": {"version": "0.1.4", "bundled": true, "dev": true}, "inflight": {"version": "1.0.6", "bundled": true, "dev": true, "requires": {"once": "1.4.0", "wrappy": "1.0.2"}}, "inherits": {"version": "2.0.3", "bundled": true, "dev": true}, "invariant": {"version": "2.2.2", "bundled": true, "dev": true, "requires": {"loose-envify": "1.3.1"}}, "invert-kv": {"version": "1.0.0", "bundled": true, "dev": true}, "is-arrayish": {"version": "0.2.1", "bundled": true, "dev": true}, "is-buffer": {"version": "1.1.5", "bundled": true, "dev": true}, "is-builtin-module": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"builtin-modules": "1.1.1"}}, "is-dotfile": {"version": "1.0.3", "bundled": true, "dev": true}, "is-equal-shallow": {"version": "0.1.3", "bundled": true, "dev": true, "requires": {"is-primitive": "2.0.0"}}, "is-extendable": {"version": "0.1.1", "bundled": true, "dev": true}, "is-extglob": {"version": "1.0.0", "bundled": true, "dev": true}, "is-finite": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"number-is-nan": "1.0.1"}}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"number-is-nan": "1.0.1"}}, "is-glob": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-extglob": "1.0.0"}}, "is-number": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"kind-of": "3.2.2"}}, "is-posix-bracket": {"version": "0.1.1", "bundled": true, "dev": true}, "is-primitive": {"version": "2.0.0", "bundled": true, "dev": true}, "is-stream": {"version": "1.1.0", "bundled": true, "dev": true}, "is-utf8": {"version": "0.2.1", "bundled": true, "dev": true}, "isarray": {"version": "1.0.0", "bundled": true, "dev": true}, "isexe": {"version": "2.0.0", "bundled": true, "dev": true}, "isobject": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"isarray": "1.0.0"}}, "istanbul-lib-coverage": {"version": "1.1.1", "bundled": true, "dev": true}, "istanbul-lib-hook": {"version": "1.0.7", "bundled": true, "dev": true, "requires": {"append-transform": "0.4.0"}}, "istanbul-lib-instrument": {"version": "1.8.0", "bundled": true, "dev": true, "requires": {"babel-generator": "6.26.0", "babel-template": "6.26.0", "babel-traverse": "6.26.0", "babel-types": "6.26.0", "babylon": "6.18.0", "istanbul-lib-coverage": "1.1.1", "semver": "5.4.1"}}, "istanbul-lib-report": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"istanbul-lib-coverage": "1.1.1", "mkdirp": "0.5.1", "path-parse": "1.0.5", "supports-color": "3.2.3"}, "dependencies": {"supports-color": {"version": "3.2.3", "bundled": true, "dev": true, "requires": {"has-flag": "1.0.0"}}}}, "istanbul-lib-source-maps": {"version": "1.2.1", "bundled": true, "dev": true, "requires": {"debug": "2.6.8", "istanbul-lib-coverage": "1.1.1", "mkdirp": "0.5.1", "rimraf": "2.6.1", "source-map": "0.5.7"}}, "istanbul-reports": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"handlebars": "4.0.10"}}, "js-tokens": {"version": "3.0.2", "bundled": true, "dev": true}, "jsesc": {"version": "1.3.0", "bundled": true, "dev": true}, "kind-of": {"version": "3.2.2", "bundled": true, "dev": true, "requires": {"is-buffer": "1.1.5"}}, "lazy-cache": {"version": "1.0.4", "bundled": true, "dev": true, "optional": true}, "lcid": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"invert-kv": "1.0.0"}}, "load-json-file": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.11", "parse-json": "2.2.0", "pify": "2.3.0", "pinkie-promise": "2.0.1", "strip-bom": "2.0.0"}}, "locate-path": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"p-locate": "2.0.0", "path-exists": "3.0.0"}, "dependencies": {"path-exists": {"version": "3.0.0", "bundled": true, "dev": true}}}, "lodash": {"version": "4.17.4", "bundled": true, "dev": true}, "longest": {"version": "1.0.1", "bundled": true, "dev": true}, "loose-envify": {"version": "1.3.1", "bundled": true, "dev": true, "requires": {"js-tokens": "3.0.2"}}, "lru-cache": {"version": "4.1.1", "bundled": true, "dev": true, "requires": {"pseudomap": "1.0.2", "yallist": "2.1.2"}}, "md5-hex": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"md5-o-matic": "0.1.1"}}, "md5-o-matic": {"version": "0.1.1", "bundled": true, "dev": true}, "mem": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"mimic-fn": "1.1.0"}}, "merge-source-map": {"version": "1.0.4", "bundled": true, "dev": true, "requires": {"source-map": "0.5.7"}}, "micromatch": {"version": "2.3.11", "bundled": true, "dev": true, "requires": {"arr-diff": "2.0.0", "array-unique": "0.2.1", "braces": "1.8.5", "expand-brackets": "0.1.5", "extglob": "0.3.2", "filename-regex": "2.0.1", "is-extglob": "1.0.0", "is-glob": "2.0.1", "kind-of": "3.2.2", "normalize-path": "2.1.1", "object.omit": "2.0.1", "parse-glob": "3.0.4", "regex-cache": "0.4.4"}}, "mimic-fn": {"version": "1.1.0", "bundled": true, "dev": true}, "minimatch": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"brace-expansion": "1.1.8"}}, "minimist": {"version": "0.0.8", "bundled": true, "dev": true}, "mkdirp": {"version": "0.5.1", "bundled": true, "dev": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.0.0", "bundled": true, "dev": true}, "normalize-package-data": {"version": "2.4.0", "bundled": true, "dev": true, "requires": {"hosted-git-info": "2.5.0", "is-builtin-module": "1.0.0", "semver": "5.4.1", "validate-npm-package-license": "3.0.1"}}, "normalize-path": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"remove-trailing-separator": "1.1.0"}}, "npm-run-path": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"path-key": "2.0.1"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "dev": true}, "object-assign": {"version": "4.1.1", "bundled": true, "dev": true}, "object.omit": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"for-own": "0.1.5", "is-extendable": "0.1.1"}}, "once": {"version": "1.4.0", "bundled": true, "dev": true, "requires": {"wrappy": "1.0.2"}}, "optimist": {"version": "0.6.1", "bundled": true, "dev": true, "requires": {"minimist": "0.0.8", "wordwrap": "0.0.3"}}, "os-homedir": {"version": "1.0.2", "bundled": true, "dev": true}, "os-locale": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"execa": "0.7.0", "lcid": "1.0.0", "mem": "1.1.0"}}, "p-finally": {"version": "1.0.0", "bundled": true, "dev": true}, "p-limit": {"version": "1.1.0", "bundled": true, "dev": true}, "p-locate": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"p-limit": "1.1.0"}}, "parse-glob": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"glob-base": "0.3.0", "is-dotfile": "1.0.3", "is-extglob": "1.0.0", "is-glob": "2.0.1"}}, "parse-json": {"version": "2.2.0", "bundled": true, "dev": true, "requires": {"error-ex": "1.3.1"}}, "path-exists": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"pinkie-promise": "2.0.1"}}, "path-is-absolute": {"version": "1.0.1", "bundled": true, "dev": true}, "path-key": {"version": "2.0.1", "bundled": true, "dev": true}, "path-parse": {"version": "1.0.5", "bundled": true, "dev": true}, "path-type": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.11", "pify": "2.3.0", "pinkie-promise": "2.0.1"}}, "pify": {"version": "2.3.0", "bundled": true, "dev": true}, "pinkie": {"version": "2.0.4", "bundled": true, "dev": true}, "pinkie-promise": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"pinkie": "2.0.4"}}, "pkg-dir": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"find-up": "1.1.2"}, "dependencies": {"find-up": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"path-exists": "2.1.0", "pinkie-promise": "2.0.1"}}}}, "preserve": {"version": "0.2.0", "bundled": true, "dev": true}, "pseudomap": {"version": "1.0.2", "bundled": true, "dev": true}, "randomatic": {"version": "1.1.7", "bundled": true, "dev": true, "requires": {"is-number": "3.0.0", "kind-of": "4.0.0"}, "dependencies": {"is-number": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "3.2.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "bundled": true, "dev": true, "requires": {"is-buffer": "1.1.5"}}}}, "kind-of": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"is-buffer": "1.1.5"}}}}, "read-pkg": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"load-json-file": "1.1.0", "normalize-package-data": "2.4.0", "path-type": "1.1.0"}}, "read-pkg-up": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"find-up": "1.1.2", "read-pkg": "1.1.0"}, "dependencies": {"find-up": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"path-exists": "2.1.0", "pinkie-promise": "2.0.1"}}}}, "regenerator-runtime": {"version": "0.11.0", "bundled": true, "dev": true}, "regex-cache": {"version": "0.4.4", "bundled": true, "dev": true, "requires": {"is-equal-shallow": "0.1.3"}}, "remove-trailing-separator": {"version": "1.1.0", "bundled": true, "dev": true}, "repeat-element": {"version": "1.1.2", "bundled": true, "dev": true}, "repeat-string": {"version": "1.6.1", "bundled": true, "dev": true}, "repeating": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-finite": "1.0.2"}}, "require-directory": {"version": "2.1.1", "bundled": true, "dev": true}, "require-main-filename": {"version": "1.0.1", "bundled": true, "dev": true}, "resolve-from": {"version": "2.0.0", "bundled": true, "dev": true}, "right-align": {"version": "0.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"align-text": "0.1.4"}}, "rimraf": {"version": "2.6.1", "bundled": true, "dev": true, "requires": {"glob": "7.1.2"}}, "semver": {"version": "5.4.1", "bundled": true, "dev": true}, "set-blocking": {"version": "2.0.0", "bundled": true, "dev": true}, "shebang-command": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"shebang-regex": "1.0.0"}}, "shebang-regex": {"version": "1.0.0", "bundled": true, "dev": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "dev": true}, "slide": {"version": "1.1.6", "bundled": true, "dev": true}, "source-map": {"version": "0.5.7", "bundled": true, "dev": true}, "spawn-wrap": {"version": "1.3.8", "bundled": true, "dev": true, "requires": {"foreground-child": "1.5.6", "mkdirp": "0.5.1", "os-homedir": "1.0.2", "rimraf": "2.6.1", "signal-exit": "3.0.2", "which": "1.3.0"}}, "spdx-correct": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"spdx-license-ids": "1.2.2"}}, "spdx-expression-parse": {"version": "1.0.4", "bundled": true, "dev": true}, "spdx-license-ids": {"version": "1.2.2", "bundled": true, "dev": true}, "string-width": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"is-fullwidth-code-point": "2.0.0", "strip-ansi": "4.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "bundled": true, "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "bundled": true, "dev": true}, "strip-ansi": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "3.0.0"}}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"ansi-regex": "2.1.1"}}, "strip-bom": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"is-utf8": "0.2.1"}}, "strip-eof": {"version": "1.0.0", "bundled": true, "dev": true}, "supports-color": {"version": "2.0.0", "bundled": true, "dev": true}, "test-exclude": {"version": "4.1.1", "bundled": true, "dev": true, "requires": {"arrify": "1.0.1", "micromatch": "2.3.11", "object-assign": "4.1.1", "read-pkg-up": "1.0.1", "require-main-filename": "1.0.1"}}, "to-fast-properties": {"version": "1.0.3", "bundled": true, "dev": true}, "trim-right": {"version": "1.0.1", "bundled": true, "dev": true}, "uglify-js": {"version": "2.8.29", "bundled": true, "dev": true, "optional": true, "requires": {"source-map": "0.5.7", "uglify-to-browserify": "1.0.2", "yargs": "3.10.0"}, "dependencies": {"yargs": {"version": "3.10.0", "bundled": true, "dev": true, "optional": true, "requires": {"camelcase": "1.2.1", "cliui": "2.1.0", "decamelize": "1.2.0", "window-size": "0.1.0"}}}}, "uglify-to-browserify": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "validate-npm-package-license": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"spdx-correct": "1.0.2", "spdx-expression-parse": "1.0.4"}}, "which": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"isexe": "2.0.0"}}, "which-module": {"version": "2.0.0", "bundled": true, "dev": true}, "window-size": {"version": "0.1.0", "bundled": true, "dev": true, "optional": true}, "wordwrap": {"version": "0.0.3", "bundled": true, "dev": true}, "wrap-ansi": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"string-width": "1.0.2", "strip-ansi": "3.0.1"}, "dependencies": {"string-width": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"code-point-at": "1.1.0", "is-fullwidth-code-point": "1.0.0", "strip-ansi": "3.0.1"}}}}, "wrappy": {"version": "1.0.2", "bundled": true, "dev": true}, "write-file-atomic": {"version": "1.3.4", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.11", "imurmurhash": "0.1.4", "slide": "1.1.6"}}, "y18n": {"version": "3.2.1", "bundled": true, "dev": true}, "yallist": {"version": "2.1.2", "bundled": true, "dev": true}, "yargs": {"version": "8.0.2", "bundled": true, "dev": true, "requires": {"camelcase": "4.1.0", "cliui": "3.2.0", "decamelize": "1.2.0", "get-caller-file": "1.0.2", "os-locale": "2.1.0", "read-pkg-up": "2.0.0", "require-directory": "2.1.1", "require-main-filename": "1.0.1", "set-blocking": "2.0.0", "string-width": "2.1.1", "which-module": "2.0.0", "y18n": "3.2.1", "yargs-parser": "7.0.0"}, "dependencies": {"camelcase": {"version": "4.1.0", "bundled": true, "dev": true}, "cliui": {"version": "3.2.0", "bundled": true, "dev": true, "requires": {"string-width": "1.0.2", "strip-ansi": "3.0.1", "wrap-ansi": "2.1.0"}, "dependencies": {"string-width": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"code-point-at": "1.1.0", "is-fullwidth-code-point": "1.0.0", "strip-ansi": "3.0.1"}}}}, "load-json-file": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.11", "parse-json": "2.2.0", "pify": "2.3.0", "strip-bom": "3.0.0"}}, "path-type": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"pify": "2.3.0"}}, "read-pkg": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"load-json-file": "2.0.0", "normalize-package-data": "2.4.0", "path-type": "2.0.0"}}, "read-pkg-up": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"find-up": "2.1.0", "read-pkg": "2.0.0"}}, "strip-bom": {"version": "3.0.0", "bundled": true, "dev": true}, "yargs-parser": {"version": "7.0.0", "bundled": true, "dev": true, "requires": {"camelcase": "4.1.0"}}}}, "yargs-parser": {"version": "5.0.0", "bundled": true, "dev": true, "requires": {"camelcase": "3.0.0"}, "dependencies": {"camelcase": {"version": "3.0.0", "bundled": true, "dev": true}}}}}, "oauth-sign": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz", "integrity": "sha1-Rqarfwrq2N6unsBWV4C31O/rnUM=", "dev": true}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}, "object.omit": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/object.omit/-/object.omit-2.0.1.tgz", "integrity": "sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=", "dev": true, "optional": true, "requires": {"for-own": "0.1.5", "is-extendable": "0.1.1"}}, "once": {"version": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "requires": {"wrappy": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"}}, "onetime": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "dev": true, "requires": {"mimic-fn": "1.1.0"}}, "optimist": {"version": "https://registry.npmjs.org/optimist/-/optimist-0.6.1.tgz", "integrity": "sha1-2j6nRob6IaGaERwybpDrFaAZZoY=", "dev": true, "requires": {"minimist": "https://registry.npmjs.org/minimist/-/minimist-0.0.10.tgz", "wordwrap": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz"}, "dependencies": {"minimist": {"version": "https://registry.npmjs.org/minimist/-/minimist-0.0.10.tgz", "integrity": "sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8=", "dev": true}, "wordwrap": {"version": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz", "integrity": "sha1-o9XabNXAvAAI03I0u68b7WMFkQc=", "dev": true}}}, "optionator": {"version": "https://registry.npmjs.org/optionator/-/optionator-0.8.2.tgz", "integrity": "sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q=", "dev": true, "requires": {"deep-is": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz", "fast-levenshtein": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "levn": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "prelude-ls": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "type-check": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz", "wordwrap": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz"}}, "os-homedir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz", "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M=", "dev": true}, "os-tmpdir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true}, "output-file-sync": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/output-file-sync/-/output-file-sync-1.1.2.tgz", "integrity": "sha1-0KM+7+YaIF+suQCS6CZZjVJFznY=", "dev": true, "requires": {"graceful-fs": "4.1.11", "mkdirp": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "object-assign": "4.1.1"}}, "parse-glob": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/parse-glob/-/parse-glob-3.0.4.tgz", "integrity": "sha1-ssN2z7EfNVE7rdFz7wu246OIORw=", "dev": true, "optional": true, "requires": {"glob-base": "0.3.0", "is-dotfile": "1.0.3", "is-extglob": "1.0.0", "is-glob": "2.0.1"}}, "path-is-absolute": {"version": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true}, "path-is-inside": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true}, "pify": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}, "pinkie": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true}, "pinkie-promise": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "requires": {"pinkie": "2.0.4"}}, "pluralize": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/pluralize/-/pluralize-7.0.0.tgz", "integrity": "sha512-ARhBOdzS3e41FbkW/XWrTEtukqqLoK5+Z/4UeDaLuSW+39JPeFgs4gCGqsrJHVZX0fUrx//4OF0K1CUGwlIFow==", "dev": true}, "prelude-ls": {"version": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=", "dev": true}, "preserve": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/preserve/-/preserve-0.2.0.tgz", "integrity": "sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks=", "dev": true, "optional": true}, "private": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/private/-/private-0.1.7.tgz", "integrity": "sha1-aM5eih7woju1cMwoU3tTMqumPvE=", "dev": true}, "process-nextick-args": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.7.tgz", "integrity": "sha1-FQ4gt1ZZCtP5EJPyWk8q2L/zC6M=", "dev": true}, "progress": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/progress/-/progress-2.0.0.tgz", "integrity": "sha1-ihvjZr+Pwj2yvSPxDG/pILQ4nR8=", "dev": true}, "pseudomap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM=", "dev": true}, "punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true}, "qs": {"version": "6.3.2", "resolved": "https://registry.npmjs.org/qs/-/qs-6.3.2.tgz", "integrity": "sha1-51vV9uJoEioqDgvaYwslUMFmUCw=", "dev": true}, "randomatic": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/randomatic/-/randomatic-1.1.7.tgz", "integrity": "sha512-D5JUjPyJbaJDkuAazpVnSfVkLlpeO3wDlPROTMLGKG1zMFNFRgrciKo1ltz/AzNTkqE0HzDx655QOL51N06how==", "dev": true, "optional": true, "requires": {"is-number": "3.0.0", "kind-of": "4.0.0"}, "dependencies": {"is-number": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "optional": true, "requires": {"kind-of": "3.2.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "optional": true, "requires": {"is-buffer": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.5.tgz"}}}}, "kind-of": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "optional": true, "requires": {"is-buffer": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.5.tgz"}}}}, "readable-stream": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.3.tgz", "integrity": "sha1-No8lEtefnUb9/HE0mueHi7weuVw=", "dev": true, "requires": {"core-util-is": "1.0.2", "inherits": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "isarray": "1.0.0", "process-nextick-args": "1.0.7", "safe-buffer": "5.1.1", "string_decoder": "1.0.3", "util-deprecate": "1.0.2"}}, "readdirp": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-2.1.0.tgz", "integrity": "sha1-TtCtBg3zBzMAxIRANz9y0cxkLXg=", "dev": true, "optional": true, "requires": {"graceful-fs": "4.1.11", "minimatch": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "readable-stream": "2.3.3", "set-immediate-shim": "1.0.1"}}, "regenerator-runtime": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.0.tgz", "integrity": "sha512-/aA0kLeRb5N9K0d4fw7ooEbI+xDe+DKD499EQqygGqeS8N3xto15p09uY2xj7ixP81sNPXvRLnAQIqdVStgb1A==", "dev": true}, "regex-cache": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.4.tgz", "integrity": "sha512-nVIZwtCjkC9YgvWkpM55B5rBhBYRZhAaJbgcFYXXsHnbZ9UZI9nnVWYZpBlCqv9ho2eZryPnWrZGsOdPwVWXWQ==", "dev": true, "optional": true, "requires": {"is-equal-shallow": "0.1.3"}}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "repeat-element": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.2.tgz", "integrity": "sha1-7wiaF40Ug7quTZPrmLT55OEdmQo=", "dev": true}, "repeat-string": {"version": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true}, "repeating": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz", "integrity": "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=", "dev": true, "requires": {"is-finite": "1.0.2"}}, "request": {"version": "2.79.0", "resolved": "https://registry.npmjs.org/request/-/request-2.79.0.tgz", "integrity": "sha1-Tf5b9r6LjNw3/Pk+BLZVd3InEN4=", "dev": true, "requires": {"aws-sign2": "0.6.0", "aws4": "1.6.0", "caseless": "0.11.0", "combined-stream": "1.0.5", "extend": "3.0.1", "forever-agent": "0.6.1", "form-data": "2.1.4", "har-validator": "2.0.6", "hawk": "3.1.3", "http-signature": "1.1.1", "is-typedarray": "1.0.0", "isstream": "0.1.2", "json-stringify-safe": "5.0.1", "mime-types": "2.1.17", "oauth-sign": "0.8.2", "qs": "6.3.2", "stringstream": "0.0.5", "tough-cookie": "2.3.3", "tunnel-agent": "0.4.3", "uuid": "3.1.0"}}, "require-uncached": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/require-uncached/-/require-uncached-1.0.3.tgz", "integrity": "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=", "dev": true, "requires": {"caller-path": "0.1.0", "resolve-from": "1.0.1"}}, "resolve": {"version": "https://registry.npmjs.org/resolve/-/resolve-1.1.7.tgz", "integrity": "sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=", "dev": true}, "resolve-from": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.1.tgz", "integrity": "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=", "dev": true}, "restore-cursor": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "dev": true, "requires": {"onetime": "2.0.1", "signal-exit": "3.0.2"}}, "right-align": {"version": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz", "integrity": "sha1-YTObci/mo1FWiSENJOFMlhSGE+8=", "dev": true, "optional": true, "requires": {"align-text": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz"}}, "rimraf": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.2.tgz", "integrity": "sha512-lreewLK/BlghmxtfH36YYVg1i8IAce4TI7oao75I1g245+6BctqTVQiBP3YUJ9C6DQOXJmkYR9X9fCLtCOJc5w==", "dev": true, "requires": {"glob": "7.1.2"}}, "run-async": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/run-async/-/run-async-2.3.0.tgz", "integrity": "sha1-A3GrSuC91yDUFm19/aZP96RFpsA=", "dev": true, "requires": {"is-promise": "2.1.0"}}, "rx-lite": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/rx-lite/-/rx-lite-4.0.8.tgz", "integrity": "sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ=", "dev": true}, "rx-lite-aggregates": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/rx-lite-aggregates/-/rx-lite-aggregates-4.0.8.tgz", "integrity": "sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74=", "dev": true, "requires": {"rx-lite": "4.0.8"}}, "safe-buffer": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz", "integrity": "sha1-iTMSr2myEj3vcfV4iQAWce6yyFM=", "dev": true}, "semver": {"version": "5.4.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.4.1.tgz", "integrity": "sha512-WfG/X9+oATh81XtllIo/I8gOiY9EXRdv1cQdyykeXK17YcUW3EXUAi2To4pcH6nZtJPr7ZOpM5OMyWJZm+8Rsg==", "dev": true}, "set-immediate-shim": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz", "integrity": "sha1-SysbJ+uAip+NzEgaWOXlb1mfP2E=", "dev": true, "optional": true}, "shebang-command": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "signal-exit": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=", "dev": true}, "slash": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz", "integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=", "dev": true}, "slice-ansi": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-1.0.0.tgz", "integrity": "sha512-POqxBK6Lb3q6s047D/XsDVNPnF9Dl8JSaqe9h9lURl0OdNqy/ujDrOiIHtsqXMGbWWTIomRzAMaTyawAU//Reg==", "dev": true, "requires": {"is-fullwidth-code-point": "2.0.0"}}, "sntp": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/sntp/-/sntp-1.0.9.tgz", "integrity": "sha1-ZUEYTMkK7qbG57NeJlkIJEPGYZg=", "dev": true, "requires": {"hoek": "2.16.3"}}, "source-map": {"version": "https://registry.npmjs.org/source-map/-/source-map-0.2.0.tgz", "integrity": "sha1-2rc/vPwrqBm03gO9b26qSBZLP50=", "dev": true, "optional": true, "requires": {"amdefine": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz"}}, "source-map-support": {"version": "0.4.18", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.4.18.tgz", "integrity": "sha512-try0/JqxPLF9nOjvSta7tVondkP5dwgyLDjVoyMDlmjugT2lRZ1OfsrYTkCd2hkDnJTKRbO/Rl3orm8vlsUzbA==", "dev": true, "requires": {"source-map": "0.5.7"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "sprintf-js": {"version": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}, "sshpk": {"version": "1.13.1", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.13.1.tgz", "integrity": "sha1-US322mKHFEMW3EwY/hzx2UBzm+M=", "dev": true, "requires": {"asn1": "0.2.3", "assert-plus": "1.0.0", "bcrypt-pbkdf": "1.0.1", "dashdash": "1.14.1", "ecc-jsbn": "0.1.1", "getpass": "0.1.7", "jsbn": "0.1.1", "tweetnacl": "0.14.5"}, "dependencies": {"assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true}}}, "string-width": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz", "integrity": "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==", "dev": true, "requires": {"is-fullwidth-code-point": "2.0.0", "strip-ansi": "4.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "3.0.0"}}}}, "string_decoder": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.0.3.tgz", "integrity": "sha1-D8Z9fBQYJd6UKC3VNr7GubzoYKs=", "dev": true, "requires": {"safe-buffer": "5.1.1"}}, "stringstream": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.5.tgz", "integrity": "sha1-TkhM1N5aC7vuGORjB3EKioFiGHg=", "dev": true}, "strip-ansi": {"version": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.0.0.tgz"}}, "strip-json-comments": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "dev": true}, "supports-color": {"version": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true}, "table": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/table/-/table-4.0.2.tgz", "integrity": "sha512-UUkEAPdSGxtRpiV9ozJ5cMTtYiqz7Ni1OGqLXRCynrvzdtR1p+cfOWe2RJLwvUG8hNanaSRjecIqwOjqeatDsA==", "dev": true, "requires": {"ajv": "5.2.3", "ajv-keywords": "2.1.0", "chalk": "2.1.0", "lodash": "4.17.4", "slice-ansi": "1.0.0", "string-width": "2.1.1"}, "dependencies": {"ansi-styles": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.0.tgz", "integrity": "sha512-NnSOmMEYtVR2JVMIGTzynRkkaxtiq1xnFBcdQD/DnNCYPoEPsVJhM98BDyaoNOQIi7p4okdi3E27eN7GQbsUug==", "dev": true, "requires": {"color-convert": "1.9.0"}}, "chalk": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.1.0.tgz", "integrity": "sha512-LUHGS/dge4ujbXMJrnihYMcL4AoOweGnw9Tp3kQuqy1Kx5c1qKjqvMJZ6nVJPMWJtKCTN72ZogH3oeSO9g9rXQ==", "dev": true, "requires": {"ansi-styles": "3.2.0", "escape-string-regexp": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "supports-color": "4.4.0"}}, "supports-color": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-4.4.0.tgz", "integrity": "sha512-rKC3+DyXWgK0ZLKwmRsrkyHVZAjNkfzeehuFWdGGcqGDTZFH73+RH6S/RDAAxl9GusSjZSUWYLmT9N5pzXFOXQ==", "dev": true, "requires": {"has-flag": "2.0.0"}}}}, "text-table": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true}, "through": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=", "dev": true}, "tmp": {"version": "0.0.33", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==", "dev": true, "requires": {"os-tmpdir": "1.0.2"}}, "to-fast-properties": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.3.tgz", "integrity": "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=", "dev": true}, "tough-cookie": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.3.tgz", "integrity": "sha1-C2GKVWW23qkL80JdBNVe3EdadWE=", "dev": true, "requires": {"punycode": "1.4.1"}}, "trim-right": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz", "integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=", "dev": true}, "tryit": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/tryit/-/tryit-1.0.3.tgz", "integrity": "sha1-OTvnMKlEb9Hq1tpZoBQwjzbCics=", "dev": true}, "tunnel-agent": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz", "integrity": "sha1-Y3PbdpCf5XDgjXNYM2Xtgop07us=", "dev": true}, "tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "dev": true, "optional": true}, "type-check": {"version": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "dev": true, "requires": {"prelude-ls": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"}}, "typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true}, "uglify-js": {"version": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz", "integrity": "sha1-KcVzMUgFe7Th913zW3qcty5qWd0=", "dev": true, "optional": true, "requires": {"source-map": "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz", "uglify-to-browserify": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz", "yargs": "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz"}, "dependencies": {"source-map": {"version": "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz", "integrity": "sha1-dc449SvwczxafwwRjYEzSiu19BI=", "dev": true, "optional": true}}}, "uglify-to-browserify": {"version": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz", "integrity": "sha1-bgkk1r2mta/jSeOabWMoUKD4grc=", "dev": true, "optional": true}, "user-home": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/user-home/-/user-home-1.1.1.tgz", "integrity": "sha1-K1viOjK2Onyd640PKNSFcko98ZA=", "dev": true}, "util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "uuid": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.1.0.tgz", "integrity": "sha512-DIWtzUkw04M4k3bf1IcpS2tngXEL26YUD2M0tMDUpnUrz2hgzUBlD55a4FjdLGPvfHxS6uluGWvaVEqgBcVa+g==", "dev": true}, "v8flags": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/v8flags/-/v8flags-2.1.1.tgz", "integrity": "sha1-qrGh+jDUX4jdMhFIh1rALAtV5bQ=", "dev": true, "requires": {"user-home": "1.1.1"}}, "verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "dev": true, "requires": {"assert-plus": "1.0.0", "core-util-is": "1.0.2", "extsprintf": "1.3.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true}}}, "which": {"version": "https://registry.npmjs.org/which/-/which-1.2.14.tgz", "integrity": "sha1-mofEN48D6CfOyvGs31bHNsAcFOU=", "dev": true, "requires": {"isexe": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"}}, "window-size": {"version": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz", "integrity": "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=", "dev": true, "optional": true}, "wordwrap": {"version": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz", "integrity": "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=", "dev": true}, "wrappy": {"version": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "write": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/write/-/write-0.2.1.tgz", "integrity": "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=", "dev": true, "requires": {"mkdirp": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz"}}, "xtend": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz", "integrity": "sha1-pcbVMr5lbiPbgg77lDofBJmNY68=", "dev": true}, "yallist": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true}, "yargs": {"version": "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz", "integrity": "sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=", "dev": true, "optional": true, "requires": {"camelcase": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "cliui": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz", "decamelize": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "window-size": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz"}}}}
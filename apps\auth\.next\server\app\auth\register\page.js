(()=>{var e={};e.id=983,e.ids=[983],e.modules={685:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>s});var t=r(8828);function s({children:e}){return(0,t.jsx)("html",{lang:"id",children:(0,t.jsx)("body",{children:(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900 p-4",children:e})})})}r(1365),r(2843)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2449:()=>{},2721:()=>{},2843:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3782:(e,a,r)=>{Promise.resolve().then(r.t.bind(r,385,23)),Promise.resolve().then(r.t.bind(r,3737,23)),Promise.resolve().then(r.t.bind(r,6081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,5856,23)),Promise.resolve().then(r.t.bind(r,5492,23)),Promise.resolve().then(r.t.bind(r,9082,23)),Promise.resolve().then(r.t.bind(r,5812,23))},3873:e=>{"use strict";e.exports=require("path")},4030:(e,a,r)=>{Promise.resolve().then(r.t.bind(r,9355,23)),Promise.resolve().then(r.t.bind(r,4439,23)),Promise.resolve().then(r.t.bind(r,7851,23)),Promise.resolve().then(r.t.bind(r,4730,23)),Promise.resolve().then(r.t.bind(r,9774,23)),Promise.resolve().then(r.t.bind(r,3170,23)),Promise.resolve().then(r.t.bind(r,968,23)),Promise.resolve().then(r.t.bind(r,8298,23))},4521:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>c});var t=r(3486),s=r(9989),o=r.n(s),n=r(5626),d=r(5519),i=r(1507),l=r(6907);let m=i.Ik({name:i.Yj().min(1,{message:"Nama lengkap tidak boleh kosong."}),email:i.Yj().email({message:"Alamat email tidak valid."}),password:i.Yj().min(8,{message:"Password harus minimal 8 karakter."}),confirmPassword:i.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Password tidak cocok",path:["confirmPassword"]});function c(){let{register:e,handleSubmit:a,formState:{errors:r,isSubmitting:s}}=(0,n.mN)({resolver:(0,d.u)(m)}),i=async e=>{try{let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,email:e.email,password:e.password,role:"promoter"})}),r=await a.json();if(!a.ok)throw Error(r.error||"Pendaftaran gagal. Coba lagi nanti.");alert("Pendaftaran berhasil! Silakan login dengan akun Anda."),window.location.href="/auth/login"}catch(e){console.error(e),alert(e.message||"Terjadi kesalahan saat mendaftar")}};return(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900 p-4",children:(0,t.jsx)(l.Zp,{className:"w-full max-w-md",children:(0,t.jsxs)("form",{onSubmit:a(i),children:[(0,t.jsxs)(l.aR,{className:"space-y-1 text-center",children:[(0,t.jsx)(l.ZB,{className:"text-2xl font-bold",children:"Daftar Akun PromotePro"}),(0,t.jsx)(l.BT,{children:"Buat akun baru untuk memulai perjalanan Anda."})]}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.JU,{htmlFor:"name",children:"Nama Lengkap"}),(0,t.jsx)(l.pd,{id:"name",type:"text",placeholder:"John Doe",...e("name")}),r.name&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:r.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.JU,{htmlFor:"email",children:"Email"}),(0,t.jsx)(l.pd,{id:"email",type:"email",placeholder:"<EMAIL>",...e("email")}),r.email&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:r.email.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.JU,{htmlFor:"password",children:"Password"}),(0,t.jsx)(l.pd,{id:"password",type:"password",...e("password")}),r.password&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:r.password.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.JU,{htmlFor:"confirm-password",children:"Konfirmasi Password"}),(0,t.jsx)(l.pd,{id:"confirm-password",type:"password",...e("confirmPassword")}),r.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:r.confirmPassword.message})]}),(0,t.jsx)(l.$n,{type:"submit",className:"w-full",disabled:s,children:s?"Memproses...":"Daftar"})]}),(0,t.jsx)(l.wL,{className:"flex flex-col gap-2 text-sm text-center",children:(0,t.jsxs)("p",{children:["Sudah punya akun?"," ",(0,t.jsx)(o(),{href:"/auth/login",className:"underline",children:"Login"})]})})]})})})}},4959:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>t});let t=(0,r(3952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\apps\\\\auth\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\auth\\register\\page.tsx","default")},5261:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>p,tree:()=>l});var t=r(4332),s=r(8819),o=r(7851),n=r.n(o),d=r(7540),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);r.d(a,i);let l={children:["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4959)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\auth\\register\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,2341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=["C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\auth\\register\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},6303:(e,a,r)=>{Promise.resolve().then(r.bind(r,4521))},6907:(e,a,r)=>{"use strict";r.d(a,{$n:()=>c,Zp:()=>h,Wu:()=>y,BT:()=>v,wL:()=>w,aR:()=>b,ZB:()=>g,pd:()=>p,JU:()=>x});var t=r(3486),s=r(159),o=r(3072),n=r(6353),d=r(4627),i=r(5855);function l(...e){return(0,i.QP)((0,d.$)(e))}let m=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef(({className:e,variant:a,size:r,asChild:s=!1,...n},d)=>{let i=s?o.DX:"button";return(0,t.jsx)(i,{className:l(m({variant:a,size:r,className:e})),ref:d,...n})});c.displayName="Button";let p=s.forwardRef(({className:e,type:a,...r},s)=>(0,t.jsx)("input",{type:a,className:l("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...r}));p.displayName="Input";var f=r(6370);let u=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),x=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(f.b,{ref:r,className:l(u(),e),...a}));x.displayName=f.b.displayName;let h=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{ref:r,className:l("rounded-lg border bg-card text-card-foreground shadow-sm",e),...a}));h.displayName="Card";let b=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{ref:r,className:l("flex flex-col space-y-1.5 p-6",e),...a}));b.displayName="CardHeader";let g=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("h3",{ref:r,className:l("text-2xl font-semibold leading-none tracking-tight",e),...a}));g.displayName="CardTitle";let v=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("p",{ref:r,className:l("text-sm text-muted-foreground",e),...a}));v.displayName="CardDescription";let y=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{ref:r,className:l("p-6 pt-0",e),...a}));y.displayName="CardContent";let w=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{ref:r,className:l("flex items-center p-6 pt-0",e),...a}));w.displayName="CardFooter";var j=r(2616),N=r(3967),P=r(9391),k=r(2283);j.bL,j.l9,j.YJ,j.ZL,j.Pb,j.z6,s.forwardRef(({className:e,inset:a,children:r,...s},o)=>(0,t.jsxs)(j.ZP,{ref:o,className:l("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",e),...s,children:[r,(0,t.jsx)(N.A,{className:"ml-auto h-4 w-4"})]})).displayName=j.ZP.displayName,s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(j.G5,{ref:r,className:l("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})).displayName=j.G5.displayName,s.forwardRef(({className:e,sideOffset:a=4,...r},s)=>(0,t.jsx)(j.ZL,{children:(0,t.jsx)(j.UC,{ref:s,sideOffset:a,className:l("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})})).displayName=j.UC.displayName,s.forwardRef(({className:e,inset:a,...r},s)=>(0,t.jsx)(j.q7,{ref:s,className:l("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a&&"pl-8",e),...r})).displayName=j.q7.displayName,s.forwardRef(({className:e,children:a,checked:r,...s},o)=>(0,t.jsxs)(j.H_,{ref:o,className:l("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:r,...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(j.VF,{children:(0,t.jsx)(P.A,{className:"h-4 w-4"})})}),a]})).displayName=j.H_.displayName,s.forwardRef(({className:e,children:a,...r},s)=>(0,t.jsxs)(j.hN,{ref:s,className:l("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(j.VF,{children:(0,t.jsx)(k.A,{className:"h-2 w-2 fill-current"})})}),a]})).displayName=j.hN.displayName,s.forwardRef(({className:e,inset:a,...r},s)=>(0,t.jsx)(j.JU,{ref:s,className:l("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",e),...r})).displayName=j.JU.displayName,s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(j.wv,{ref:r,className:l("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=j.wv.displayName,s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:r,className:l("w-full caption-bottom text-sm",e),...a})})).displayName="Table",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("thead",{ref:r,className:l("[&_tr]:border-b",e),...a})).displayName="TableHeader",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("tbody",{ref:r,className:l("[&_tr:last-child]:border-0",e),...a})).displayName="TableBody",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("tfoot",{ref:r,className:l("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("tr",{ref:r,className:l("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a})).displayName="TableRow",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("th",{ref:r,className:l("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a})).displayName="TableHead",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("td",{ref:r,className:l("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a})).displayName="TableCell",s.forwardRef(({className:e,...a},r)=>(0,t.jsx)("caption",{ref:r,className:l("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"},6983:(e,a,r)=>{Promise.resolve().then(r.bind(r,4959))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9699:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>s});var t=r(1253);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var a=require("../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[191,787,253,130],()=>r(5261));module.exports=t})();
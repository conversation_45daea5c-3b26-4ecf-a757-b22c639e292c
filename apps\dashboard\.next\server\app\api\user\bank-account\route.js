(()=>{var e={};e.id=498,e.ids=[498],e.modules={408:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7032:()=>{},8504:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>v,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>k});var s={};r.r(s),r.d(s,{POST:()=>d});var a=r(8106),n=r(8819),o=r(2050),u=r(4235),i=r(5208);let c=null;async function p(e){return e?{id:"user-123"}:null}async function d(e){let t=await (0,i.UL)(),r=t.get("auth_token")?.value;if(!await p(r))return u.NextResponse.json({error:"Unauthorized"},{status:401});try{let{bankName:t,accountHolderName:r,accountNumber:s}=await e.json();return c={bankName:t,accountHolderName:r,accountNumber:s},console.log("Bank account details saved:",c),u.NextResponse.json({success:!0})}catch(e){return u.NextResponse.json({error:"Failed to save bank account"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/user/bank-account/route",pathname:"/api/user/bank-account",filename:"route",bundlePath:"app/api/user/bank-account/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\api\\user\\bank-account\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:k,serverHooks:v}=l;function g(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:k})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[191,744,208],()=>r(8504));module.exports=s})();
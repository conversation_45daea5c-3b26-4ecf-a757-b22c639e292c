(()=>{var e={};e.id=974,e.ids=[974],e.modules={75:(e,t,n)=>{Promise.resolve().then(n.bind(n,3878))},89:(e,t)=>{"use strict";function n(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return n}})},97:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return a},STATIC_METADATA_IMAGES:function(){return i},getExtensionRegexString:function(){return s},isMetadataPage:function(){return c},isMetadataRoute:function(){return f},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return d}});let r=n(89),o=n(4773),l=n(467),i={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},a=["js","jsx","ts","tsx"],s=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,n){let o=(n?"":"?")+"$",l=`\\d?${n?"":"(-\\w{6})?"}`,a=[RegExp(`^[\\\\/]robots${s(t.concat("txt"),null)}${o}`),RegExp(`^[\\\\/]manifest${s(t.concat("webmanifest","json"),null)}${o}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${s(["xml"],t)}${o}`),RegExp(`[\\\\/]${i.icon.filename}${l}${s(i.icon.extensions,t)}${o}`),RegExp(`[\\\\/]${i.apple.filename}${l}${s(i.apple.extensions,t)}${o}`),RegExp(`[\\\\/]${i.openGraph.filename}${l}${s(i.openGraph.extensions,t)}${o}`),RegExp(`[\\\\/]${i.twitter.filename}${l}${s(i.twitter.extensions,t)}${o}`)],u=(0,r.normalizePathSep)(e);return a.some(e=>e.test(u))}function d(e){let t=e.replace(/\/route$/,"");return(0,l.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function c(e){return!(0,l.isAppRouteRoute)(e)&&u(e,[],!1)}function f(e){let t=(0,o.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,l.isAppRouteRoute)(e)&&u(t,[],!1)}},264:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return l}});let r=n(6542),o=n(3833);function l(e){if(e.startsWith("/"))return(0,o.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,r.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},467:(e,t)=>{"use strict";function n(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return n}})},685:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});var r=n(8828);function o({children:e}){return(0,r.jsx)("html",{lang:"id",children:(0,r.jsx)("body",{className:"bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"admin-wrapper",children:e})})})}n(2843),n(1365)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1253:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillMetadataSegment:function(){return f},normalizeMetadataPageToRoute:function(){return g},normalizeMetadataRoute:function(){return p}});let r=n(97),o=function(e){return e&&e.__esModule?e:{default:e}}(n(4026)),l=n(9606),i=n(9824),a=n(5003),s=n(4773),u=n(89),d=n(3566);function c(e){let t=o.default.dirname(e);if(e.endsWith("/sitemap"))return"";let n="";return t.split("/").some(e=>(0,d.isGroupSegment)(e)||(0,d.isParallelRouteSegment)(e))&&(n=(0,a.djb2Hash)(t).toString(36).slice(0,6)),n}function f(e,t,n){let r=(0,s.normalizeAppPath)(e),a=(0,i.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),d=(0,l.interpolateDynamicPath)(r,t,a),{name:f,ext:p}=o.default.parse(n),g=c(o.default.posix.join(e,f)),m=g?`-${g}`:"";return(0,u.normalizePathSep)(o.default.join(d,`${f}${m}${p}`))}function p(e){if(!(0,r.isMetadataPage)(e))return e;let t=e,n="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":n=c(e),!t.endsWith("/route")){let{dir:e,name:r,ext:l}=o.default.parse(t);t=o.default.posix.join(e,`${r}${n?`-${n}`:""}${l}`,"route")}return t}function g(e,t){let n=e.endsWith("/route"),r=n?e.slice(0,-6):e,o=r.endsWith("/sitemap")?".xml":"";return(t?`${r}/[__metadata_id__]`:`${r}${o}`)+(n?"/route":"")}},2265:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:n,value:e[n++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if("{"===r){t.push({type:"OPEN",index:n,value:e[n++]});continue}if("}"===r){t.push({type:"CLOSE",index:n,value:e[n++]});continue}if(":"===r){for(var o="",l=n+1;l<e.length;){var i=e.charCodeAt(l);if(i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122||95===i){o+=e[l++];continue}break}if(!o)throw TypeError("Missing parameter name at "+n);t.push({type:"NAME",index:n,value:o}),n=l;continue}if("("===r){var a=1,s="",l=n+1;if("?"===e[l])throw TypeError('Pattern cannot start with "?" at '+l);for(;l<e.length;){if("\\"===e[l]){s+=e[l++]+e[l++];continue}if(")"===e[l]){if(0==--a){l++;break}}else if("("===e[l]&&(a++,"?"!==e[l+1]))throw TypeError("Capturing groups are not allowed at "+l);s+=e[l++]}if(a)throw TypeError("Unbalanced pattern at "+n);if(!s)throw TypeError("Missing pattern at "+n);t.push({type:"PATTERN",index:n,value:s}),n=l;continue}t.push({type:"CHAR",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),r=t.prefixes,l=void 0===r?"./":r,i="[^"+o(t.delimiter||"/#?")+"]+?",a=[],s=0,u=0,d="",c=function(e){if(u<n.length&&n[u].type===e)return n[u++].value},f=function(e){var t=c(e);if(void 0!==t)return t;var r=n[u];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+e)},p=function(){for(var e,t="";e=c("CHAR")||c("ESCAPED_CHAR");)t+=e;return t};u<n.length;){var g=c("CHAR"),m=c("NAME"),h=c("PATTERN");if(m||h){var v=g||"";-1===l.indexOf(v)&&(d+=v,v=""),d&&(a.push(d),d=""),a.push({name:m||s++,prefix:v,suffix:"",pattern:h||i,modifier:c("MODIFIER")||""});continue}var b=g||c("ESCAPED_CHAR");if(b){d+=b;continue}if(d&&(a.push(d),d=""),c("OPEN")){var v=p(),w=c("NAME")||"",y=c("PATTERN")||"",x=p();f("CLOSE"),a.push({name:w||(y?s++:""),pattern:w&&!y?i:y,prefix:v,suffix:x,modifier:c("MODIFIER")||""});continue}f("END")}return a}function n(e,t){void 0===t&&(t={});var n=l(t),r=t.encode,o=void 0===r?function(e){return e}:r,i=t.validate,a=void 0===i||i,s=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",n)});return function(t){for(var n="",r=0;r<e.length;r++){var l=e[r];if("string"==typeof l){n+=l;continue}var i=t?t[l.name]:void 0,u="?"===l.modifier||"*"===l.modifier,d="*"===l.modifier||"+"===l.modifier;if(Array.isArray(i)){if(!d)throw TypeError('Expected "'+l.name+'" to not repeat, but got an array');if(0===i.length){if(u)continue;throw TypeError('Expected "'+l.name+'" to not be empty')}for(var c=0;c<i.length;c++){var f=o(i[c],l);if(a&&!s[r].test(f))throw TypeError('Expected all "'+l.name+'" to match "'+l.pattern+'", but got "'+f+'"');n+=l.prefix+f+l.suffix}continue}if("string"==typeof i||"number"==typeof i){var f=o(String(i),l);if(a&&!s[r].test(f))throw TypeError('Expected "'+l.name+'" to match "'+l.pattern+'", but got "'+f+'"');n+=l.prefix+f+l.suffix;continue}if(!u){var p=d?"an array":"a string";throw TypeError('Expected "'+l.name+'" to be '+p)}}return n}}function r(e,t,n){void 0===n&&(n={});var r=n.decode,o=void 0===r?function(e){return e}:r;return function(n){var r=e.exec(n);if(!r)return!1;for(var l=r[0],i=r.index,a=Object.create(null),s=1;s<r.length;s++)!function(e){if(void 0!==r[e]){var n=t[e-1];"*"===n.modifier||"+"===n.modifier?a[n.name]=r[e].split(n.prefix+n.suffix).map(function(e){return o(e,n)}):a[n.name]=o(r[e],n)}}(s);return{path:l,index:i,params:a}}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function l(e){return e&&e.sensitive?"":"i"}function i(e,t,n){void 0===n&&(n={});for(var r=n.strict,i=void 0!==r&&r,a=n.start,s=n.end,u=n.encode,d=void 0===u?function(e){return e}:u,c="["+o(n.endsWith||"")+"]|$",f="["+o(n.delimiter||"/#?")+"]",p=void 0===a||a?"^":"",g=0;g<e.length;g++){var m=e[g];if("string"==typeof m)p+=o(d(m));else{var h=o(d(m.prefix)),v=o(d(m.suffix));if(m.pattern)if(t&&t.push(m),h||v)if("+"===m.modifier||"*"===m.modifier){var b="*"===m.modifier?"?":"";p+="(?:"+h+"((?:"+m.pattern+")(?:"+v+h+"(?:"+m.pattern+"))*)"+v+")"+b}else p+="(?:"+h+"("+m.pattern+")"+v+")"+m.modifier;else p+="("+m.pattern+")"+m.modifier;else p+="(?:"+h+v+")"+m.modifier}}if(void 0===s||s)i||(p+=f+"?"),p+=n.endsWith?"(?="+c+")":"$";else{var w=e[e.length-1],y="string"==typeof w?f.indexOf(w[w.length-1])>-1:void 0===w;i||(p+="(?:"+f+"(?="+c+"))?"),y||(p+="(?="+f+"|"+c+")")}return new RegExp(p,l(n))}function a(t,n,r){if(t instanceof RegExp){if(!n)return t;var o=t.source.match(/\((?!\?)/g);if(o)for(var s=0;s<o.length;s++)n.push({name:s,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return a(e,n,r).source}).join("|")+")",l(r)):i(e(t,r),n,r)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,r){return n(e(t,r),r)},t.tokensToFunction=n,t.match=function(e,t){var n=[];return r(a(e,n,t),n,t)},t.regexpToFunction=r,t.tokensToRegexp=i,t.pathToRegexp=a})(),e.exports=t})()},2449:()=>{},2721:()=>{},2843:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3732:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function o(e){return n.test(e)?e.replace(r,"\\$&"):e}},3782:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,385,23)),Promise.resolve().then(n.t.bind(n,3737,23)),Promise.resolve().then(n.t.bind(n,6081,23)),Promise.resolve().then(n.t.bind(n,1904,23)),Promise.resolve().then(n.t.bind(n,5856,23)),Promise.resolve().then(n.t.bind(n,5492,23)),Promise.resolve().then(n.t.bind(n,9082,23)),Promise.resolve().then(n.t.bind(n,5812,23))},3833:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return o}}),n(5138);let r=n(6542);function o(e,t,n){void 0===n&&(n=!0);let o=new URL("http://n"),l=t?new URL(t,o):e.startsWith(".")?new URL("http://n"):o,{pathname:i,searchParams:a,search:s,hash:u,href:d,origin:c}=new URL(e,l);if(c!==o.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:i,query:n?(0,r.searchParamsToUrlQuery)(a):void 0,search:s,hash:u,href:d.slice(c.length)}}},3873:e=>{"use strict";e.exports=require("path")},3878:(e,t,n)=>{"use strict";n.d(t,{UsersDataTable:()=>lM});var r,o,l,i=n(3486),a=n(159),s=n.t(a,2);function u(e,t){return"function"==typeof e?e(t):e}function d(e,t){return n=>{t.setState(t=>({...t,[e]:u(n,t[e])}))}}function c(e){return e instanceof Function}function f(e,t,n){let r,o=[];return l=>{let i,a;n.key&&n.debug&&(i=Date.now());let s=e(l);if(!(s.length!==o.length||s.some((e,t)=>o[t]!==e)))return r;if(o=s,n.key&&n.debug&&(a=Date.now()),r=t(...s),null==n||null==n.onChange||n.onChange(r),n.key&&n.debug&&null!=n&&n.debug()){let e=Math.round((Date.now()-i)*100)/100,t=Math.round((Date.now()-a)*100)/100,r=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${o(t,5)} /${o(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*r,120))}deg 100% 31%);`,null==n?void 0:n.key)}return r}}function p(e,t,n,r){return{debug:()=>{var n;return null!=(n=null==e?void 0:e.debugAll)?n:e[t]},key:!1,onChange:r}}let g="debugHeaders";function m(e,t,n){var r;let o={id:null!=(r=n.id)?r:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=n=>{n.subHeaders&&n.subHeaders.length&&n.subHeaders.map(t),e.push(n)};return t(o),e},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(o,e)}),o}function h(e,t,n,r){var o,l;let i=0,a=function(e,t){void 0===t&&(t=1),i=Math.max(i,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var n;null!=(n=e.columns)&&n.length&&a(e.columns,t+1)},0)};a(e);let s=[],u=(e,t)=>{let o={depth:t,id:[r,`${t}`].filter(Boolean).join("_"),headers:[]},l=[];e.forEach(e=>{let i,a=[...l].reverse()[0],s=e.column.depth===o.depth,u=!1;if(s&&e.column.parent?i=e.column.parent:(i=e.column,u=!0),a&&(null==a?void 0:a.column)===i)a.subHeaders.push(e);else{let o=m(n,i,{id:[r,t,i.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:u,placeholderId:u?`${l.filter(e=>e.column===i).length}`:void 0,depth:t,index:l.length});o.subHeaders.push(e),l.push(o)}o.headers.push(e),e.headerGroup=o}),s.push(o),t>0&&u(l,t-1)};u(t.map((e,t)=>m(n,e,{depth:i,index:t})),i-1),s.reverse();let d=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,n=0,r=[0];return e.subHeaders&&e.subHeaders.length?(r=[],d(e.subHeaders).forEach(e=>{let{colSpan:n,rowSpan:o}=e;t+=n,r.push(o)})):t=1,n+=Math.min(...r),e.colSpan=t,e.rowSpan=n,{colSpan:t,rowSpan:n}});return d(null!=(o=null==(l=s[0])?void 0:l.headers)?o:[]),s}let v=(e,t,n,r,o,l,i)=>{let a={id:t,index:r,original:n,depth:o,parentId:i,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(a._valuesCache.hasOwnProperty(t))return a._valuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return a._valuesCache[t]=n.accessorFn(a.original,r),a._valuesCache[t]},getUniqueValues:t=>{if(a._uniqueValuesCache.hasOwnProperty(t))return a._uniqueValuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return n.columnDef.getUniqueValues?a._uniqueValuesCache[t]=n.columnDef.getUniqueValues(a.original,r):a._uniqueValuesCache[t]=[a.getValue(t)],a._uniqueValuesCache[t]},renderValue:t=>{var n;return null!=(n=a.getValue(t))?n:e.options.renderFallbackValue},subRows:null!=l?l:[],getLeafRows:()=>(function(e,t){let n=[],r=e=>{e.forEach(e=>{n.push(e);let o=t(e);null!=o&&o.length&&r(o)})};return r(e),n})(a.subRows,e=>e.subRows),getParentRow:()=>a.parentId?e.getRow(a.parentId,!0):void 0,getParentRows:()=>{let e=[],t=a;for(;;){let n=t.getParentRow();if(!n)break;e.push(n),t=n}return e.reverse()},getAllCells:f(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,n,r){let o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(r),renderValue:()=>{var t;return null!=(t=o.getValue())?t:e.options.renderFallbackValue},getContext:f(()=>[e,n,t,o],(e,t,n,r)=>({table:e,column:t,row:n,cell:r,getValue:r.getValue,renderValue:r.renderValue}),p(e.options,"debugCells","cell.getContext"))};return e._features.forEach(r=>{null==r.createCell||r.createCell(o,n,t,e)},{}),o})(e,a,t,t.id)),p(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:f(()=>[a.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),p(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let n=e._features[t];null==n||null==n.createRow||n.createRow(a,e)}return a},b=(e,t,n)=>{var r,o;let l=null==n||null==(r=n.toString())?void 0:r.toLowerCase();return!!(null==(o=e.getValue(t))||null==(o=o.toString())||null==(o=o.toLowerCase())?void 0:o.includes(l))};b.autoRemove=e=>_(e);let w=(e,t,n)=>{var r;return!!(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.includes(n))};w.autoRemove=e=>_(e);let y=(e,t,n)=>{var r;return(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.toLowerCase())===(null==n?void 0:n.toLowerCase())};y.autoRemove=e=>_(e);let x=(e,t,n)=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)};x.autoRemove=e=>_(e);let R=(e,t,n)=>!n.some(n=>{var r;return!(null!=(r=e.getValue(t))&&r.includes(n))});R.autoRemove=e=>_(e)||!(null!=e&&e.length);let C=(e,t,n)=>n.some(n=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)});C.autoRemove=e=>_(e)||!(null!=e&&e.length);let S=(e,t,n)=>e.getValue(t)===n;S.autoRemove=e=>_(e);let E=(e,t,n)=>e.getValue(t)==n;E.autoRemove=e=>_(e);let P=(e,t,n)=>{let[r,o]=n,l=e.getValue(t);return l>=r&&l<=o};P.resolveFilterValue=e=>{let[t,n]=e,r="number"!=typeof t?parseFloat(t):t,o="number"!=typeof n?parseFloat(n):n,l=null===t||Number.isNaN(r)?-1/0:r,i=null===n||Number.isNaN(o)?1/0:o;if(l>i){let e=l;l=i,i=e}return[l,i]},P.autoRemove=e=>_(e)||_(e[0])&&_(e[1]);let M={includesString:b,includesStringSensitive:w,equalsString:y,arrIncludes:x,arrIncludesAll:R,arrIncludesSome:C,equals:S,weakEquals:E,inNumberRange:P};function _(e){return null==e||""===e}function A(e,t,n){return!!e&&!!e.autoRemove&&e.autoRemove(t,n)||void 0===t||"string"==typeof t&&!t}let F={sum:(e,t,n)=>n.reduce((t,n)=>{let r=n.getValue(e);return t+("number"==typeof r?r:0)},0),min:(e,t,n)=>{let r;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(r>n||void 0===r&&n>=n)&&(r=n)}),r},max:(e,t,n)=>{let r;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(r<n||void 0===r&&n>=n)&&(r=n)}),r},extent:(e,t,n)=>{let r,o;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(void 0===r?n>=n&&(r=o=n):(r>n&&(r=n),o<n&&(o=n)))}),[r,o]},mean:(e,t)=>{let n=0,r=0;if(t.forEach(t=>{let o=t.getValue(e);null!=o&&(o*=1)>=o&&(++n,r+=o)}),n)return r/n},median:(e,t)=>{if(!t.length)return;let n=t.map(t=>t.getValue(e));if(!function(e){return Array.isArray(e)&&e.every(e=>"number"==typeof e)}(n))return;if(1===n.length)return n[0];let r=Math.floor(n.length/2),o=n.sort((e,t)=>e-t);return n.length%2!=0?o[r]:(o[r-1]+o[r])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},j=()=>({left:[],right:[]}),I={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},O=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),N=null;function T(e){return"touchstart"===e.type}function D(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let k=()=>({pageIndex:0,pageSize:10}),L=()=>({top:[],bottom:[]}),V=(e,t,n,r,o)=>{var l;let i=o.getRow(t,!0);n?(i.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),i.getCanSelect()&&(e[t]=!0)):delete e[t],r&&null!=(l=i.subRows)&&l.length&&i.getCanSelectSubRows()&&i.subRows.forEach(t=>V(e,t.id,n,r,o))};function z(e,t){let n=e.getState().rowSelection,r=[],o={},l=function(e,t){return e.map(e=>{var t;let i=G(e,n);if(i&&(r.push(e),o[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:l(e.subRows)}),i)return e}).filter(Boolean)};return{rows:l(t.rows),flatRows:r,rowsById:o}}function G(e,t){var n;return null!=(n=t[e.id])&&n}function H(e,t,n){var r;if(!(null!=(r=e.subRows)&&r.length))return!1;let o=!0,l=!1;return e.subRows.forEach(e=>{if((!l||o)&&(e.getCanSelect()&&(G(e,t)?l=!0:o=!1),e.subRows&&e.subRows.length)){let n=H(e,t);"all"===n?l=!0:("some"===n&&(l=!0),o=!1)}}),o?"all":!!l&&"some"}let $=/([0-9]+)/gm;function U(e,t){return e===t?0:e>t?1:-1}function B(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function W(e,t){let n=e.split($).filter(Boolean),r=t.split($).filter(Boolean);for(;n.length&&r.length;){let e=n.shift(),t=r.shift(),o=parseInt(e,10),l=parseInt(t,10),i=[o,l].sort();if(isNaN(i[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(i[1]))return isNaN(o)?-1:1;if(o>l)return 1;if(l>o)return -1}return n.length-r.length}let q={alphanumeric:(e,t,n)=>W(B(e.getValue(n)).toLowerCase(),B(t.getValue(n)).toLowerCase()),alphanumericCaseSensitive:(e,t,n)=>W(B(e.getValue(n)),B(t.getValue(n))),text:(e,t,n)=>U(B(e.getValue(n)).toLowerCase(),B(t.getValue(n)).toLowerCase()),textCaseSensitive:(e,t,n)=>U(B(e.getValue(n)),B(t.getValue(n))),datetime:(e,t,n)=>{let r=e.getValue(n),o=t.getValue(n);return r>o?1:r<o?-1:0},basic:(e,t,n)=>U(e.getValue(n),t.getValue(n))},K=[{createTable:e=>{e.getHeaderGroups=f(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>{var l,i;let a=null!=(l=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?l:[],s=null!=(i=null==o?void 0:o.map(e=>n.find(t=>t.id===e)).filter(Boolean))?i:[];return h(t,[...a,...n.filter(e=>!(null!=r&&r.includes(e.id))&&!(null!=o&&o.includes(e.id))),...s],e)},p(e.options,g,"getHeaderGroups")),e.getCenterHeaderGroups=f(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>h(t,n=n.filter(e=>!(null!=r&&r.includes(e.id))&&!(null!=o&&o.includes(e.id))),e,"center"),p(e.options,g,"getCenterHeaderGroups")),e.getLeftHeaderGroups=f(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,r)=>{var o;return h(t,null!=(o=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"left")},p(e.options,g,"getLeftHeaderGroups")),e.getRightHeaderGroups=f(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,r)=>{var o;return h(t,null!=(o=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"right")},p(e.options,g,"getRightHeaderGroups")),e.getFooterGroups=f(()=>[e.getHeaderGroups()],e=>[...e].reverse(),p(e.options,g,"getFooterGroups")),e.getLeftFooterGroups=f(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),p(e.options,g,"getLeftFooterGroups")),e.getCenterFooterGroups=f(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),p(e.options,g,"getCenterFooterGroups")),e.getRightFooterGroups=f(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),p(e.options,g,"getRightFooterGroups")),e.getFlatHeaders=f(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),p(e.options,g,"getFlatHeaders")),e.getLeftFlatHeaders=f(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),p(e.options,g,"getLeftFlatHeaders")),e.getCenterFlatHeaders=f(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),p(e.options,g,"getCenterFlatHeaders")),e.getRightFlatHeaders=f(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),p(e.options,g,"getRightFlatHeaders")),e.getCenterLeafHeaders=f(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),p(e.options,g,"getCenterLeafHeaders")),e.getLeftLeafHeaders=f(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),p(e.options,g,"getLeftLeafHeaders")),e.getRightLeafHeaders=f(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),p(e.options,g,"getRightLeafHeaders")),e.getLeafHeaders=f(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,n)=>{var r,o,l,i,a,s;return[...null!=(r=null==(o=e[0])?void 0:o.headers)?r:[],...null!=(l=null==(i=t[0])?void 0:i.headers)?l:[],...null!=(a=null==(s=n[0])?void 0:s.headers)?a:[]].map(e=>e.getLeafHeaders()).flat()},p(e.options,g,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:d("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=n?n:!e.getIsVisible()}))},e.getIsVisible=()=>{var n,r;let o=e.columns;return null==(n=o.length?o.some(e=>e.getIsVisible()):null==(r=t.getState().columnVisibility)?void 0:r[e.id])||n},e.getCanHide=()=>{var n,r;return(null==(n=e.columnDef.enableHiding)||n)&&(null==(r=t.options.enableHiding)||r)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=f(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),p(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=f(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,n)=>[...e,...t,...n],p(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,n)=>f(()=>[n(),n().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),p(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:null!=(n=e.initialState.columnVisibility)?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=null!=(n=t)?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,n)=>({...e,[n.id]:t||!(null!=n.getCanHide&&n.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible(null==(n=t.target)?void 0:n.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:d("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=f(e=>[D(t,e)],t=>t.findIndex(t=>t.id===e.id),p(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var r;return(null==(r=D(t,n)[0])?void 0:r.id)===e.id},e.getIsLastColumn=n=>{var r;let o=D(t,n);return(null==(r=o[o.length-1])?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:null!=(n=e.initialState.columnOrder)?n:[])},e._getOrderColumnsFn=f(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,n)=>r=>{let o=[];if(null!=e&&e.length){let t=[...e],n=[...r];for(;n.length&&t.length;){let e=t.shift(),r=n.findIndex(t=>t.id===e);r>-1&&o.push(n.splice(r,1)[0])}o=[...o,...n]}else o=r;return function(e,t,n){if(!(null!=t&&t.length)||!n)return e;let r=e.filter(e=>!t.includes(e.id));return"remove"===n?r:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...r]}(o,t,n)},p(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:j(),...e}),getDefaultOptions:e=>({onColumnPinningChange:d("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{let r=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,o,l,i,a,s;return"right"===n?{left:(null!=(l=null==e?void 0:e.left)?l:[]).filter(e=>!(null!=r&&r.includes(e))),right:[...(null!=(i=null==e?void 0:e.right)?i:[]).filter(e=>!(null!=r&&r.includes(e))),...r]}:"left"===n?{left:[...(null!=(a=null==e?void 0:e.left)?a:[]).filter(e=>!(null!=r&&r.includes(e))),...r],right:(null!=(s=null==e?void 0:e.right)?s:[]).filter(e=>!(null!=r&&r.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=r&&r.includes(e))),right:(null!=(o=null==e?void 0:e.right)?o:[]).filter(e=>!(null!=r&&r.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var n,r,o;return(null==(n=e.columnDef.enablePinning)||n)&&(null==(r=null!=(o=t.options.enableColumnPinning)?o:t.options.enablePinning)||r)}),e.getIsPinned=()=>{let n=e.getLeafColumns().map(e=>e.id),{left:r,right:o}=t.getState().columnPinning,l=n.some(e=>null==r?void 0:r.includes(e)),i=n.some(e=>null==o?void 0:o.includes(e));return l?"left":!!i&&"right"},e.getPinnedIndex=()=>{var n,r;let o=e.getIsPinned();return o?null!=(n=null==(r=t.getState().columnPinning)||null==(r=r[o])?void 0:r.indexOf(e.id))?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=f(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,n)=>{let r=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!r.includes(e.column.id))},p(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=f(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),p(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=f(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),p(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,r;return e.setColumnPinning(t?j():null!=(n=null==(r=e.initialState)?void 0:r.columnPinning)?n:j())},e.getIsSomeColumnsPinned=t=>{var n,r,o;let l=e.getState().columnPinning;return t?!!(null==(n=l[t])?void 0:n.length):!!((null==(r=l.left)?void 0:r.length)||(null==(o=l.right)?void 0:o.length))},e.getLeftLeafColumns=f(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),p(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=f(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),p(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=f(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,n)=>{let r=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!r.includes(e.id))},p(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:d("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"string"==typeof r?M.includesString:"number"==typeof r?M.inNumberRange:"boolean"==typeof r||null!==r&&"object"==typeof r?M.equals:Array.isArray(r)?M.arrIncludes:M.weakEquals},e.getFilterFn=()=>{var n,r;return c(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(n=null==(r=t.options.filterFns)?void 0:r[e.columnDef.filterFn])?n:M[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,r,o;return(null==(n=e.columnDef.enableColumnFilter)||n)&&(null==(r=t.options.enableColumnFilters)||r)&&(null==(o=t.options.enableFilters)||o)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return null==(n=t.getState().columnFilters)||null==(n=n.find(t=>t.id===e.id))?void 0:n.value},e.getFilterIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().columnFilters)?void 0:r.findIndex(t=>t.id===e.id))?n:-1},e.setFilterValue=n=>{t.setColumnFilters(t=>{var r,o;let l=e.getFilterFn(),i=null==t?void 0:t.find(t=>t.id===e.id),a=u(n,i?i.value:void 0);if(A(l,a,e))return null!=(r=null==t?void 0:t.filter(t=>t.id!==e.id))?r:[];let s={id:e.id,value:a};return i?null!=(o=null==t?void 0:t.map(t=>t.id===e.id?s:t))?o:[]:null!=t&&t.length?[...t,s]:[s]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let n=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var r;return null==(r=u(t,e))?void 0:r.filter(e=>{let t=n.find(t=>t.id===e.id);return!(t&&A(t.getFilterFn(),e.value,t))&&!0})})},e.resetColumnFilters=t=>{var n,r;e.setColumnFilters(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.columnFilters)?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:d("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;let r=null==(n=e.getCoreRowModel().flatRows[0])||null==(n=n._getAllCellsByColumnId()[t.id])?void 0:n.getValue();return"string"==typeof r||"number"==typeof r}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,r,o,l;return(null==(n=e.columnDef.enableGlobalFilter)||n)&&(null==(r=t.options.enableGlobalFilter)||r)&&(null==(o=t.options.enableFilters)||o)&&(null==(l=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||l)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>M.includesString,e.getGlobalFilterFn=()=>{var t,n;let{globalFilterFn:r}=e.options;return c(r)?r:"auto"===r?e.getGlobalAutoFilterFn():null!=(t=null==(n=e.options.filterFns)?void 0:n[r])?t:M[r]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:d("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let n=t.getFilteredRowModel().flatRows.slice(10),r=!1;for(let t of n){let n=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(n))return q.datetime;if("string"==typeof n&&(r=!0,n.split($).length>1))return q.alphanumeric}return r?q.text:q.basic},e.getAutoSortDir=()=>{let n=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==n?void 0:n.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var n,r;if(!e)throw Error();return c(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(n=null==(r=t.options.sortingFns)?void 0:r[e.columnDef.sortingFn])?n:q[e.columnDef.sortingFn]},e.toggleSorting=(n,r)=>{let o=e.getNextSortingOrder(),l=null!=n;t.setSorting(i=>{let a,s=null==i?void 0:i.find(t=>t.id===e.id),u=null==i?void 0:i.findIndex(t=>t.id===e.id),d=[],c=l?n:"desc"===o;if("toggle"!=(a=null!=i&&i.length&&e.getCanMultiSort()&&r?s?"toggle":"add":null!=i&&i.length&&u!==i.length-1?"replace":s?"toggle":"replace")||l||o||(a="remove"),"add"===a){var f;(d=[...i,{id:e.id,desc:c}]).splice(0,d.length-(null!=(f=t.options.maxMultiSortColCount)?f:Number.MAX_SAFE_INTEGER))}else d="toggle"===a?i.map(t=>t.id===e.id?{...t,desc:c}:t):"remove"===a?i.filter(t=>t.id!==e.id):[{id:e.id,desc:c}];return d})},e.getFirstSortDir=()=>{var n,r;return(null!=(n=null!=(r=e.columnDef.sortDescFirst)?r:t.options.sortDescFirst)?n:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=n=>{var r,o;let l=e.getFirstSortDir(),i=e.getIsSorted();return i?(i===l||null!=(r=t.options.enableSortingRemoval)&&!r||!!n&&null!=(o=t.options.enableMultiRemove)&&!o)&&("desc"===i?"asc":"desc"):l},e.getCanSort=()=>{var n,r;return(null==(n=e.columnDef.enableSorting)||n)&&(null==(r=t.options.enableSorting)||r)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,r;return null!=(n=null!=(r=e.columnDef.enableMultiSort)?r:t.options.enableMultiSort)?n:!!e.accessorFn},e.getIsSorted=()=>{var n;let r=null==(n=t.getState().sorting)?void 0:n.find(t=>t.id===e.id);return!!r&&(r.desc?"desc":"asc")},e.getSortIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().sorting)?void 0:r.findIndex(t=>t.id===e.id))?n:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let n=e.getCanSort();return r=>{n&&(null==r.persist||r.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(r))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,r;e.setSorting(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.sorting)?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return null!=(t=null==(n=e.getValue())||null==n.toString?void 0:n.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:d("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var n,r;return(null==(n=e.columnDef.enableGrouping)||n)&&(null==(r=t.options.enableGrouping)||r)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"number"==typeof r?F.sum:"[object Date]"===Object.prototype.toString.call(r)?F.extent:void 0},e.getAggregationFn=()=>{var n,r;if(!e)throw Error();return c(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(n=null==(r=t.options.aggregationFns)?void 0:r[e.columnDef.aggregationFn])?n:F[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,r;e.setGrouping(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.grouping)?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];let r=t.getColumn(n);return null!=r&&r.columnDef.getGroupingValue?(e._groupingValuesCache[n]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,r)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=n.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:d("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var r,o;if(!t)return void e._queue(()=>{t=!0});if(null!=(r=null!=(o=e.options.autoResetAll)?o:e.options.autoResetExpanded)?r:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var n,r;e.setExpanded(t?{}:null!=(n=null==(r=e.initialState)?void 0:r.expanded)?n:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let n=e.split(".");t=Math.max(t,n.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(r=>{var o;let l=!0===r||!!(null!=r&&r[e.id]),i={};if(!0===r?Object.keys(t.getRowModel().rowsById).forEach(e=>{i[e]=!0}):i=r,n=null!=(o=n)?o:!l,!l&&n)return{...i,[e.id]:!0};if(l&&!n){let{[e.id]:t,...n}=i;return n}return r})},e.getIsExpanded=()=>{var n;let r=t.getState().expanded;return!!(null!=(n=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?n:!0===r||(null==r?void 0:r[e.id]))},e.getCanExpand=()=>{var n,r,o;return null!=(n=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?n:(null==(r=t.options.enableExpanding)||r)&&!!(null!=(o=e.subRows)&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,r=e;for(;n&&r.parentId;)n=(r=t.getRow(r.parentId,!0)).getIsExpanded();return n},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{...k(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:d("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var r,o;if(!t)return void e._queue(()=>{t=!0});if(null!=(r=null!=(o=e.options.autoResetAll)?o:e.options.autoResetPageIndex)?r:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>u(t,e)),e.resetPagination=t=>{var n;e.setPagination(t?k():null!=(n=e.initialState.pagination)?n:k())},e.setPageIndex=t=>{e.setPagination(n=>{let r=u(t,n.pageIndex);return r=Math.max(0,Math.min(r,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...n,pageIndex:r}})},e.resetPageIndex=t=>{var n,r;e.setPageIndex(t?0:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageIndex)?n:0)},e.resetPageSize=t=>{var n,r;e.setPageSize(t?10:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageSize)?n:10)},e.setPageSize=t=>{e.setPagination(e=>{let n=Math.max(1,u(t,e.pageSize)),r=Math.floor(e.pageSize*e.pageIndex/n);return{...e,pageIndex:r,pageSize:n}})},e.setPageCount=t=>e.setPagination(n=>{var r;let o=u(t,null!=(r=e.options.pageCount)?r:-1);return"number"==typeof o&&(o=Math.max(-1,o)),{...n,pageCount:o}}),e.getPageOptions=f(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},p(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,n=e.getPageCount();return -1===n||0!==n&&t<n-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:L(),...e}),getDefaultOptions:e=>({onRowPinningChange:d("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,r,o)=>{let l=r?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],i=new Set([...o?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],e.id,...l]);t.setRowPinning(e=>{var t,r,o,l,a,s;return"bottom"===n?{top:(null!=(o=null==e?void 0:e.top)?o:[]).filter(e=>!(null!=i&&i.has(e))),bottom:[...(null!=(l=null==e?void 0:e.bottom)?l:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)]}:"top"===n?{top:[...(null!=(a=null==e?void 0:e.top)?a:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)],bottom:(null!=(s=null==e?void 0:e.bottom)?s:[]).filter(e=>!(null!=i&&i.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=i&&i.has(e))),bottom:(null!=(r=null==e?void 0:e.bottom)?r:[]).filter(e=>!(null!=i&&i.has(e)))}})},e.getCanPin=()=>{var n;let{enableRowPinning:r,enablePinning:o}=t.options;return"function"==typeof r?r(e):null==(n=null!=r?r:o)||n},e.getIsPinned=()=>{let n=[e.id],{top:r,bottom:o}=t.getState().rowPinning,l=n.some(e=>null==r?void 0:r.includes(e)),i=n.some(e=>null==o?void 0:o.includes(e));return l?"top":!!i&&"bottom"},e.getPinnedIndex=()=>{var n,r;let o=e.getIsPinned();if(!o)return -1;let l=null==(n="top"===o?t.getTopRows():t.getBottomRows())?void 0:n.map(e=>{let{id:t}=e;return t});return null!=(r=null==l?void 0:l.indexOf(e.id))?r:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,r;return e.setRowPinning(t?L():null!=(n=null==(r=e.initialState)?void 0:r.rowPinning)?n:L())},e.getIsSomeRowsPinned=t=>{var n,r,o;let l=e.getState().rowPinning;return t?!!(null==(n=l[t])?void 0:n.length):!!((null==(r=l.top)?void 0:r.length)||(null==(o=l.bottom)?void 0:o.length))},e._getPinnedRows=(t,n,r)=>{var o;return(null==(o=e.options.keepPinnedRows)||o?(null!=n?n:[]).map(t=>{let n=e.getRow(t,!0);return n.getIsAllParentsExpanded()?n:null}):(null!=n?n:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:r}))},e.getTopRows=f(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),p(e.options,"debugRows","getTopRows")),e.getBottomRows=f(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),p(e.options,"debugRows","getBottomRows")),e.getCenterRows=f(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,n)=>{let r=new Set([...null!=t?t:[],...null!=n?n:[]]);return e.filter(e=>!r.has(e.id))},p(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:d("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:null!=(n=e.initialState.rowSelection)?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let r={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(e=>{e.getCanSelect()&&(r[e.id]=!0)}):o.forEach(e=>{delete r[e.id]}),r})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{let r=void 0!==t?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(t=>{V(o,t.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=f(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?z(e,n):{rows:[],flatRows:[],rowsById:{}},p(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=f(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?z(e,n):{rows:[],flatRows:[],rowsById:{}},p(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=f(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?z(e,n):{rows:[],flatRows:[],rowsById:{}},p(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState(),r=!!(t.length&&Object.keys(n).length);return r&&t.some(e=>e.getCanSelect()&&!n[e.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:n}=e.getState(),r=!!t.length;return r&&t.some(e=>!n[e.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var t;let n=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,r)=>{let o=e.getIsSelected();t.setRowSelection(l=>{var i;if(n=void 0!==n?n:!o,e.getCanSelect()&&o===n)return l;let a={...l};return V(a,e.id,n,null==(i=null==r?void 0:r.selectChildren)||i,t),a})},e.getIsSelected=()=>{let{rowSelection:n}=t.getState();return G(e,n)},e.getIsSomeSelected=()=>{let{rowSelection:n}=t.getState();return"some"===H(e,n)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:n}=t.getState();return"all"===H(e,n)},e.getCanSelect=()=>{var n;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(n=t.options.enableRowSelection)||n},e.getCanSelectSubRows=()=>{var n;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(n=t.options.enableSubRowSelection)||n},e.getCanMultiSelect=()=>{var n;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(n=t.options.enableMultiRowSelection)||n},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return n=>{var r;t&&e.toggleSelected(null==(r=n.target)?void 0:r.checked)}}}},{getDefaultColumnDef:()=>I,getInitialState:e=>({columnSizing:{},columnSizingInfo:O(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:d("columnSizing",e),onColumnSizingInfoChange:d("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,r,o;let l=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(n=e.columnDef.minSize)?n:I.minSize,null!=(r=null!=l?l:e.columnDef.size)?r:I.size),null!=(o=e.columnDef.maxSize)?o:I.maxSize)},e.getStart=f(e=>[e,D(t,e),t.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),p(t.options,"debugColumns","getStart")),e.getAfter=f(e=>[e,D(t,e),t.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),p(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:n,...r}=t;return r})},e.getCanResize=()=>{var n,r;return(null==(n=e.columnDef.enableResizing)||n)&&(null==(r=t.options.enableColumnResizing)||r)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,n=e=>{if(e.subHeaders.length)e.subHeaders.forEach(n);else{var r;t+=null!=(r=e.column.getSize())?r:0}};return n(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=n=>{let r=t.getColumn(e.column.id),o=null==r?void 0:r.getCanResize();return l=>{if(!r||!o||(null==l.persist||l.persist(),T(l)&&l.touches&&l.touches.length>1))return;let i=e.getSize(),a=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[r.id,r.getSize()]],s=T(l)?Math.round(l.touches[0].clientX):l.clientX,u={},d=(e,n)=>{"number"==typeof n&&(t.setColumnSizingInfo(e=>{var r,o;let l="rtl"===t.options.columnResizeDirection?-1:1,i=(n-(null!=(r=null==e?void 0:e.startOffset)?r:0))*l,a=Math.max(i/(null!=(o=null==e?void 0:e.startSize)?o:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,n]=e;u[t]=Math.round(100*Math.max(n+n*a,0))/100}),{...e,deltaOffset:i,deltaPercentage:a}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...u})))},c=e=>d("move",e),f=e=>{d("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},p=n||("undefined"!=typeof document?document:null),g={moveHandler:e=>c(e.clientX),upHandler:e=>{null==p||p.removeEventListener("mousemove",g.moveHandler),null==p||p.removeEventListener("mouseup",g.upHandler),f(e.clientX)}},m={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),c(e.touches[0].clientX),!1),upHandler:e=>{var t;null==p||p.removeEventListener("touchmove",m.moveHandler),null==p||p.removeEventListener("touchend",m.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),f(null==(t=e.touches[0])?void 0:t.clientX)}},h=!!function(){if("boolean"==typeof N)return N;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return N=e}()&&{passive:!1};T(l)?(null==p||p.addEventListener("touchmove",m.moveHandler,h),null==p||p.addEventListener("touchend",m.upHandler,h)):(null==p||p.addEventListener("mousemove",g.moveHandler,h),null==p||p.addEventListener("mouseup",g.upHandler,h)),t.setColumnSizingInfo(e=>({...e,startOffset:s,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:a,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:null!=(n=e.initialState.columnSizing)?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?O():null!=(n=e.initialState.columnSizingInfo)?n:O())},e.getTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getLeftHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getCenterHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getRightHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function X(e,t){var n,r,o;return e?"function"==typeof(r=n=e)&&(()=>{let e=Object.getPrototypeOf(r);return e.prototype&&e.prototype.isReactComponent})()||"function"==typeof n||"object"==typeof(o=n)&&"symbol"==typeof o.$$typeof&&["react.memo","react.forward_ref"].includes(o.$$typeof.description)?a.createElement(e,t):e:null}function Y(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function Q(...e){return t=>{let n=!1,r=e.map(e=>{let r=Y(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():Y(e[t],null)}}}}function Z(...e){return a.useCallback(Q(...e),e)}function J(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:n,...r}=e;if(a.isValidElement(n)){var o;let e,l,i=(o=n,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==a.Fragment&&(s.ref=t?Q(t,i):i),a.cloneElement(n,s)}return a.Children.count(n)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=a.forwardRef((e,n)=>{let{children:r,...o}=e,l=a.Children.toArray(r),s=l.find(en);if(s){let e=s.props.children,r=l.map(t=>t!==s?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:n,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,i.jsx)(t,{...o,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var ee=J("Slot"),et=Symbol("radix.slottable");function en(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===et}function er(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var l=t.length;for(n=0;n<l;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}(e))&&(r&&(r+=" "),r+=t);return r}let eo=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,el=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return er(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:o,defaultVariants:l}=t,i=Object.keys(o).map(e=>{let t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;let i=eo(t)||eo(r);return o[e][i]}),a=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return er(e,i,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...a}[t]):({...l,...a})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)},ei=e=>{let t=ed(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),ea(n,t)||eu(e)},getConflictingClassGroupIds:(e,t)=>{let o=n[e]||[];return t&&r[e]?[...o,...r[e]]:o}}},ea=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),o=r?ea(e.slice(1),r):void 0;if(o)return o;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},es=/^\[(.+)\]$/,eu=e=>{if(es.test(e)){let t=es.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},ed=e=>{let{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return eg(Object.entries(e.classGroups),n).forEach(([e,n])=>{ec(n,r,e,t)}),r},ec=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:ef(t,e)).classGroupId=n;return}if("function"==typeof e)return ep(e)?void ec(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,o])=>{ec(o,ef(t,e),n,r)})})},ef=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},ep=e=>e.isThemeGetter,eg=(e,t)=>t?e.map(([e,n])=>[e,n.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,n])=>[t+e,n])):e)]):e,em=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,o=(o,l)=>{n.set(o,l),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(o(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):o(e,t)}}},eh=e=>{let{separator:t,experimentalParseClassName:n}=e,r=1===t.length,o=t[0],l=t.length,i=e=>{let n,i=[],a=0,s=0;for(let u=0;u<e.length;u++){let d=e[u];if(0===a){if(d===o&&(r||e.slice(u,u+l)===t)){i.push(e.slice(s,u)),s=u+l;continue}if("/"===d){n=u;continue}}"["===d?a++:"]"===d&&a--}let u=0===i.length?e:e.substring(s),d=u.startsWith("!"),c=d?u.substring(1):u;return{modifiers:i,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:n&&n>s?n-s:void 0}};return n?e=>n({className:e,parseClassName:i}):i},ev=e=>{if(e.length<=1)return e;let t=[],n=[];return e.forEach(e=>{"["===e[0]?(t.push(...n.sort(),e),n=[]):n.push(e)}),t.push(...n.sort()),t},eb=e=>({cache:em(e.cacheSize),parseClassName:eh(e),...ei(e)}),ew=/\s+/,ey=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,l=[],i=e.trim().split(ew),a="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{modifiers:s,hasImportantModifier:u,baseClassName:d,maybePostfixModifierPosition:c}=n(t),f=!!c,p=r(f?d.substring(0,c):d);if(!p){if(!f||!(p=r(d))){a=t+(a.length>0?" "+a:a);continue}f=!1}let g=ev(s).join(":"),m=u?g+"!":g,h=m+p;if(l.includes(h))continue;l.push(h);let v=o(p,f);for(let e=0;e<v.length;++e){let t=v[e];l.push(m+t)}a=t+(a.length>0?" "+a:a)}return a};function ex(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=eR(e))&&(r&&(r+=" "),r+=t);return r}let eR=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=eR(e[r]))&&(n&&(n+=" "),n+=t);return n},eC=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},eS=/^\[(?:([a-z-]+):)?(.+)\]$/i,eE=/^\d+\/\d+$/,eP=new Set(["px","full","screen"]),eM=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,e_=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,eA=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,eF=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ej=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,eI=e=>eN(e)||eP.has(e)||eE.test(e),eO=e=>eq(e,"length",eK),eN=e=>!!e&&!Number.isNaN(Number(e)),eT=e=>eq(e,"number",eN),eD=e=>!!e&&Number.isInteger(Number(e)),ek=e=>e.endsWith("%")&&eN(e.slice(0,-1)),eL=e=>eS.test(e),eV=e=>eM.test(e),ez=new Set(["length","size","percentage"]),eG=e=>eq(e,ez,eX),eH=e=>eq(e,"position",eX),e$=new Set(["image","url"]),eU=e=>eq(e,e$,eQ),eB=e=>eq(e,"",eY),eW=()=>!0,eq=(e,t,n)=>{let r=eS.exec(e);return!!r&&(r[1]?"string"==typeof t?r[1]===t:t.has(r[1]):n(r[2]))},eK=e=>e_.test(e)&&!eA.test(e),eX=()=>!1,eY=e=>eF.test(e),eQ=e=>ej.test(e);Symbol.toStringTag;let eZ=function(e,...t){let n,r,o,l=function(a){return r=(n=eb(t.reduce((e,t)=>t(e),e()))).cache.get,o=n.cache.set,l=i,i(a)};function i(e){let t=r(e);if(t)return t;let l=ey(e,n);return o(e,l),l}return function(){return l(ex.apply(null,arguments))}}(()=>{let e=eC("colors"),t=eC("spacing"),n=eC("blur"),r=eC("brightness"),o=eC("borderColor"),l=eC("borderRadius"),i=eC("borderSpacing"),a=eC("borderWidth"),s=eC("contrast"),u=eC("grayscale"),d=eC("hueRotate"),c=eC("invert"),f=eC("gap"),p=eC("gradientColorStops"),g=eC("gradientColorStopPositions"),m=eC("inset"),h=eC("margin"),v=eC("opacity"),b=eC("padding"),w=eC("saturate"),y=eC("scale"),x=eC("sepia"),R=eC("skew"),C=eC("space"),S=eC("translate"),E=()=>["auto","contain","none"],P=()=>["auto","hidden","clip","visible","scroll"],M=()=>["auto",eL,t],_=()=>[eL,t],A=()=>["",eI,eO],F=()=>["auto",eN,eL],j=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],I=()=>["solid","dashed","dotted","double","none"],O=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],N=()=>["start","end","center","between","around","evenly","stretch"],T=()=>["","0",eL],D=()=>["auto","avoid","all","avoid-page","page","left","right","column"],k=()=>[eN,eL];return{cacheSize:500,separator:":",theme:{colors:[eW],spacing:[eI,eO],blur:["none","",eV,eL],brightness:k(),borderColor:[e],borderRadius:["none","","full",eV,eL],borderSpacing:_(),borderWidth:A(),contrast:k(),grayscale:T(),hueRotate:k(),invert:T(),gap:_(),gradientColorStops:[e],gradientColorStopPositions:[ek,eO],inset:M(),margin:M(),opacity:k(),padding:_(),saturate:k(),scale:k(),sepia:T(),skew:k(),space:_(),translate:_()},classGroups:{aspect:[{aspect:["auto","square","video",eL]}],container:["container"],columns:[{columns:[eV]}],"break-after":[{"break-after":D()}],"break-before":[{"break-before":D()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...j(),eL]}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",eD,eL]}],basis:[{basis:M()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",eL]}],grow:[{grow:T()}],shrink:[{shrink:T()}],order:[{order:["first","last","none",eD,eL]}],"grid-cols":[{"grid-cols":[eW]}],"col-start-end":[{col:["auto",{span:["full",eD,eL]},eL]}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":[eW]}],"row-start-end":[{row:["auto",{span:[eD,eL]},eL]}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",eL]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",eL]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...N()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...N(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...N(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[C]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[C]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",eL,t]}],"min-w":[{"min-w":[eL,t,"min","max","fit"]}],"max-w":[{"max-w":[eL,t,"none","full","min","max","fit","prose",{screen:[eV]},eV]}],h:[{h:[eL,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[eL,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[eL,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[eL,t,"auto","min","max","fit"]}],"font-size":[{text:["base",eV,eO]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",eT]}],"font-family":[{font:[eW]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",eL]}],"line-clamp":[{"line-clamp":["none",eN,eT]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",eI,eL]}],"list-image":[{"list-image":["none",eL]}],"list-style-type":[{list:["none","disc","decimal",eL]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...I(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",eI,eO]}],"underline-offset":[{"underline-offset":["auto",eI,eL]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",eL]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",eL]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...j(),eH]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",eG]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},eU]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...I(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:I()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...I()]}],"outline-offset":[{"outline-offset":[eI,eL]}],"outline-w":[{outline:[eI,eO]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:A()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[eI,eO]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",eV,eB]}],"shadow-color":[{shadow:[eW]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...O(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":O()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",eV,eL]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[w]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",eL]}],duration:[{duration:k()}],ease:[{ease:["linear","in","out","in-out",eL]}],delay:[{delay:k()}],animate:[{animate:["none","spin","ping","pulse","bounce",eL]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[eD,eL]}],"translate-x":[{"translate-x":[S]}],"translate-y":[{"translate-y":[S]}],"skew-x":[{"skew-x":[R]}],"skew-y":[{"skew-y":[R]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",eL]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",eL]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",eL]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[eI,eO,eT]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function eJ(...e){return eZ(er(e))}let e0=el("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),e1=a.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},l)=>(0,i.jsx)(r?ee:"button",{className:eJ(e0({variant:t,size:n,className:e})),ref:l,...o}));e1.displayName="Button";let e2=a.forwardRef(({className:e,type:t,...n},r)=>(0,i.jsx)("input",{type:t,className:eJ("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));e2.displayName="Input";var e3=n(2358),e5=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=J(`Primitive.${t}`),r=a.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:t,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function e9(e,t){e&&e3.flushSync(()=>e.dispatchEvent(t))}var e4=a.forwardRef((e,t)=>(0,i.jsx)(e5.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));e4.displayName="Label";let e8=el("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70");a.forwardRef(({className:e,...t},n)=>(0,i.jsx)(e4,{ref:n,className:eJ(e8(),e),...t})).displayName=e4.displayName;let e6=a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("div",{ref:n,className:eJ("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));function e7(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function te(e,t=[]){let n=[],r=()=>{let t=n.map(e=>a.createContext(e));return function(n){let r=n?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=a.createContext(r),l=n.length;n=[...n,r];let s=t=>{let{scope:n,children:r,...s}=t,u=n?.[e]?.[l]||o,d=a.useMemo(()=>s,Object.values(s));return(0,i.jsx)(u.Provider,{value:d,children:r})};return s.displayName=t+"Provider",[s,function(n,i){let s=i?.[e]?.[l]||o,u=a.useContext(s);if(u)return u;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}e6.displayName="Card",a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("div",{ref:n,className:eJ("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("h3",{ref:n,className:eJ("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("p",{ref:n,className:eJ("text-sm text-muted-foreground",e),...t})).displayName="CardDescription",a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("div",{ref:n,className:eJ("p-6 pt-0",e),...t})).displayName="CardContent",a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("div",{ref:n,className:eJ("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";var tt=globalThis?.document?a.useLayoutEffect:()=>{},tn=s[" useInsertionEffect ".trim().toString()]||tt;function tr({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,i]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),o=a.useRef(n),l=a.useRef(t);return tn(()=>{l.current=t},[t]),a.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),s=void 0!==e,u=s?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[u,a.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&i.current?.(n)}else l(t)},[s,e,l,i])]}function to(e){let t=e+"CollectionProvider",[n,r]=te(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=a.useRef(null),l=a.useRef(new Map).current;return(0,i.jsx)(o,{scope:t,itemMap:l,collectionRef:r,children:n})};s.displayName=t;let u=e+"CollectionSlot",d=J(u),c=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=Z(t,l(u,n).collectionRef);return(0,i.jsx)(d,{ref:o,children:r})});c.displayName=u;let f=e+"CollectionItemSlot",p="data-radix-collection-item",g=J(f),m=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,s=a.useRef(null),u=Z(t,s),d=l(f,n);return a.useEffect(()=>(d.itemMap.set(s,{ref:s,...o}),()=>void d.itemMap.delete(s))),(0,i.jsx)(g,{...{[p]:""},ref:u,children:r})});return m.displayName=f,[{Provider:s,Slot:c,ItemSlot:m},function(t){let n=l(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}Symbol("RADIX:SYNC_STATE");var tl=new WeakMap;function ti(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=ta(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function ta(e){return e!=e||0===e?0:Math.trunc(e)}var ts=a.createContext(void 0);function tu(e){let t=a.useContext(ts);return e||t||"ltr"}function td(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var tc="dismissableLayer.update",tf=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),tp=a.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:l,onFocusOutside:s,onInteractOutside:u,onDismiss:d,...c}=e,f=a.useContext(tf),[p,g]=a.useState(null),m=p?.ownerDocument??globalThis?.document,[,h]=a.useState({}),v=Z(t,e=>g(e)),b=Array.from(f.layers),[w]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),y=b.indexOf(w),x=p?b.indexOf(p):-1,R=f.layersWithOutsidePointerEventsDisabled.size>0,C=x>=y,S=function(e,t=globalThis?.document){let n=td(e),r=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){tm("dismissableLayer.pointerDownOutside",n,l,{discrete:!0})},l={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(l),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...f.branches].some(e=>e.contains(t));C&&!n&&(l?.(e),u?.(e),e.defaultPrevented||d?.())},m),E=function(e,t=globalThis?.document){let n=td(e),r=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!r.current&&tm("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...f.branches].some(e=>e.contains(t))&&(s?.(e),u?.(e),e.defaultPrevented||d?.())},m);return!function(e,t=globalThis?.document){let n=td(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{x===f.layers.size-1&&(r?.(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},m),a.useEffect(()=>{if(p)return n&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(o=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(p)),f.layers.add(p),tg(),()=>{n&&1===f.layersWithOutsidePointerEventsDisabled.size&&(m.body.style.pointerEvents=o)}},[p,m,n,f]),a.useEffect(()=>()=>{p&&(f.layers.delete(p),f.layersWithOutsidePointerEventsDisabled.delete(p),tg())},[p,f]),a.useEffect(()=>{let e=()=>h({});return document.addEventListener(tc,e),()=>document.removeEventListener(tc,e)},[]),(0,i.jsx)(e5.div,{...c,ref:v,style:{pointerEvents:R?C?"auto":"none":void 0,...e.style},onFocusCapture:e7(e.onFocusCapture,E.onFocusCapture),onBlurCapture:e7(e.onBlurCapture,E.onBlurCapture),onPointerDownCapture:e7(e.onPointerDownCapture,S.onPointerDownCapture)})});function tg(){let e=new CustomEvent(tc);document.dispatchEvent(e)}function tm(e,t,n,{discrete:r}){let o=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?e9(o,l):o.dispatchEvent(l)}tp.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(tf),r=a.useRef(null),o=Z(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,i.jsx)(e5.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var th=0;function tv(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var tb="focusScope.autoFocusOnMount",tw="focusScope.autoFocusOnUnmount",ty={bubbles:!1,cancelable:!0},tx=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:l,...s}=e,[u,d]=a.useState(null),c=td(o),f=td(l),p=a.useRef(null),g=Z(t,e=>d(e)),m=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(m.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:tS(p.current,{select:!0})},t=function(e){if(m.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||tS(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&tS(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,m.paused]),a.useEffect(()=>{if(u){tE.add(m);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(tb,ty);u.addEventListener(tb,c),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(tS(r,{select:t}),document.activeElement!==n)return}(tR(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&tS(u))}return()=>{u.removeEventListener(tb,c),setTimeout(()=>{let t=new CustomEvent(tw,ty);u.addEventListener(tw,f),u.dispatchEvent(t),t.defaultPrevented||tS(e??document.body,{select:!0}),u.removeEventListener(tw,f),tE.remove(m)},0)}}},[u,c,f,m]);let h=a.useCallback(e=>{if(!n&&!r||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,l]=function(e){let t=tR(e);return[tC(t,e),tC(t.reverse(),e)]}(t);r&&l?e.shiftKey||o!==l?e.shiftKey&&o===r&&(e.preventDefault(),n&&tS(l,{select:!0})):(e.preventDefault(),n&&tS(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,m.paused]);return(0,i.jsx)(e5.div,{tabIndex:-1,...s,ref:g,onKeyDown:h})});function tR(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function tC(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function tS(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}tx.displayName="FocusScope";var tE=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=tP(e,t)).unshift(t)},remove(t){e=tP(e,t),e[0]?.resume()}}}();function tP(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var tM=s[" useId ".trim().toString()]||(()=>void 0),t_=0;function tA(e){let[t,n]=a.useState(tM());return tt(()=>{e||n(e=>e??String(t_++))},[e]),e||(t?`radix-${t}`:"")}let tF=["top","right","bottom","left"],tj=Math.min,tI=Math.max,tO=Math.round,tN=Math.floor,tT=e=>({x:e,y:e}),tD={left:"right",right:"left",bottom:"top",top:"bottom"},tk={start:"end",end:"start"};function tL(e,t){return"function"==typeof e?e(t):e}function tV(e){return e.split("-")[0]}function tz(e){return e.split("-")[1]}function tG(e){return"x"===e?"y":"x"}function tH(e){return"y"===e?"height":"width"}function t$(e){return["top","bottom"].includes(tV(e))?"y":"x"}function tU(e){return e.replace(/start|end/g,e=>tk[e])}function tB(e){return e.replace(/left|right|bottom|top/g,e=>tD[e])}function tW(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function tq(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function tK(e,t,n){let r,{reference:o,floating:l}=e,i=t$(t),a=tG(t$(t)),s=tH(a),u=tV(t),d="y"===i,c=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,p=o[s]/2-l[s]/2;switch(u){case"top":r={x:c,y:o.y-l.height};break;case"bottom":r={x:c,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-l.width,y:f};break;default:r={x:o.x,y:o.y}}switch(tz(t)){case"start":r[a]-=p*(n&&d?-1:1);break;case"end":r[a]+=p*(n&&d?-1:1)}return r}let tX=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:c}=tK(u,r,s),f=r,p={},g=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:h,y:v,data:b,reset:w}=await m({x:d,y:c,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});d=null!=h?h:d,c=null!=v?v:c,p={...p,[l]:{...p[l],...b}},w&&g<=50&&(g++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:d,y:c}=tK(u,f,s)),n=-1)}return{x:d,y:c,placement:f,strategy:o,middlewareData:p}};async function tY(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:c="floating",altBoundary:f=!1,padding:p=0}=tL(t,e),g=tW(p),m=a[f?"floating"===c?"reference":"floating":c],h=tq(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(m)))||n?m:m.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:d,strategy:s})),v="floating"===c?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),w=await (null==l.isElement?void 0:l.isElement(b))&&await (null==l.getScale?void 0:l.getScale(b))||{x:1,y:1},y=tq(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:b,strategy:s}):v);return{top:(h.top-y.top+g.top)/w.y,bottom:(y.bottom-h.bottom+g.bottom)/w.y,left:(h.left-y.left+g.left)/w.x,right:(y.right-h.right+g.right)/w.x}}function tQ(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function tZ(e){return tF.some(t=>e[t]>=0)}async function tJ(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=tV(n),a=tz(n),s="y"===t$(n),u=["left","top"].includes(i)?-1:1,d=l&&s?-1:1,c=tL(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:g}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof g&&(p="end"===a?-1*g:g),s?{x:p*d,y:f*u}:{x:f*u,y:p*d}}function t0(){return"undefined"!=typeof window}function t1(e){return t5(e)?(e.nodeName||"").toLowerCase():"#document"}function t2(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function t3(e){var t;return null==(t=(t5(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function t5(e){return!!t0()&&(e instanceof Node||e instanceof t2(e).Node)}function t9(e){return!!t0()&&(e instanceof Element||e instanceof t2(e).Element)}function t4(e){return!!t0()&&(e instanceof HTMLElement||e instanceof t2(e).HTMLElement)}function t8(e){return!!t0()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof t2(e).ShadowRoot)}function t6(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=nr(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function t7(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function ne(e){let t=nt(),n=t9(e)?nr(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function nt(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function nn(e){return["html","body","#document"].includes(t1(e))}function nr(e){return t2(e).getComputedStyle(e)}function no(e){return t9(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function nl(e){if("html"===t1(e))return e;let t=e.assignedSlot||e.parentNode||t8(e)&&e.host||t3(e);return t8(t)?t.host:t}function ni(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=nl(t);return nn(n)?t.ownerDocument?t.ownerDocument.body:t.body:t4(n)&&t6(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=t2(o);if(l){let e=na(i);return t.concat(i,i.visualViewport||[],t6(o)?o:[],e&&n?ni(e):[])}return t.concat(o,ni(o,[],n))}function na(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ns(e){let t=nr(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=t4(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=tO(n)!==l||tO(r)!==i;return a&&(n=l,r=i),{width:n,height:r,$:a}}function nu(e){return t9(e)?e:e.contextElement}function nd(e){let t=nu(e);if(!t4(t))return tT(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=ns(t),i=(l?tO(n.width):n.width)/r,a=(l?tO(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),a&&Number.isFinite(a)||(a=1),{x:i,y:a}}let nc=tT(0);function nf(e){let t=t2(e);return nt()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:nc}function np(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=nu(e),a=tT(1);t&&(r?t9(r)&&(a=nd(r)):a=nd(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===t2(i))&&o)?nf(i):tT(0),u=(l.left+s.x)/a.x,d=(l.top+s.y)/a.y,c=l.width/a.x,f=l.height/a.y;if(i){let e=t2(i),t=r&&t9(r)?t2(r):r,n=e,o=na(n);for(;o&&r&&t!==n;){let e=nd(o),t=o.getBoundingClientRect(),r=nr(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,d*=e.y,c*=e.x,f*=e.y,u+=l,d+=i,o=na(n=t2(o))}}return tq({width:c,height:f,x:u,y:d})}function ng(e,t){let n=no(e).scrollLeft;return t?t.left+n:np(t3(e)).left+n}function nm(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ng(e,r)),y:r.top+t.scrollTop}}function nh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=t2(e),r=t3(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=nt();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=t3(e),n=no(e),r=e.ownerDocument.body,o=tI(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=tI(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),i=-n.scrollLeft+ng(e),a=-n.scrollTop;return"rtl"===nr(r).direction&&(i+=tI(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:i,y:a}}(t3(e));else if(t9(t))r=function(e,t){let n=np(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=t4(e)?nd(e):tT(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=nf(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return tq(r)}function nv(e){return"static"===nr(e).position}function nb(e,t){if(!t4(e)||"fixed"===nr(e).position)return null;if(t)return t(e);let n=e.offsetParent;return t3(e)===n&&(n=n.ownerDocument.body),n}function nw(e,t){let n=t2(e);if(t7(e))return n;if(!t4(e)){let t=nl(e);for(;t&&!nn(t);){if(t9(t)&&!nv(t))return t;t=nl(t)}return n}let r=nb(e,t);for(;r&&["table","td","th"].includes(t1(r))&&nv(r);)r=nb(r,t);return r&&nn(r)&&nv(r)&&!ne(r)?n:r||function(e){let t=nl(e);for(;t4(t)&&!nn(t);){if(ne(t))return t;if(t7(t))break;t=nl(t)}return null}(e)||n}let ny=async function(e){let t=this.getOffsetParent||nw,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=t4(t),o=t3(t),l="fixed"===n,i=np(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=tT(0);if(r||!r&&!l)if(("body"!==t1(t)||t6(o))&&(a=no(t)),r){let e=np(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=ng(o));l&&!r&&o&&(s.x=ng(o));let u=!o||r||l?tT(0):nm(o,a);return{x:i.left+a.scrollLeft-s.x-u.x,y:i.top+a.scrollTop-s.y-u.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},nx={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=t3(r),a=!!t&&t7(t.floating);if(r===i||a&&l)return n;let s={scrollLeft:0,scrollTop:0},u=tT(1),d=tT(0),c=t4(r);if((c||!c&&!l)&&(("body"!==t1(r)||t6(i))&&(s=no(r)),t4(r))){let e=np(r);u=nd(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let f=!i||c||l?tT(0):nm(i,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+d.x+f.x,y:n.y*u.y-s.scrollTop*u.y+d.y+f.y}},getDocumentElement:t3,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?t7(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=ni(e,[],!1).filter(e=>t9(e)&&"body"!==t1(e)),o=null,l="fixed"===nr(e).position,i=l?nl(e):e;for(;t9(i)&&!nn(i);){let t=nr(i),n=ne(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||t6(i)&&!n&&function e(t,n){let r=nl(t);return!(r===n||!t9(r)||nn(r))&&("fixed"===nr(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=nl(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=l[0],a=l.reduce((e,n)=>{let r=nh(t,n,o);return e.top=tI(r.top,e.top),e.right=tj(r.right,e.right),e.bottom=tj(r.bottom,e.bottom),e.left=tI(r.left,e.left),e},nh(t,i,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:nw,getElementRects:ny,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ns(e);return{width:t,height:n}},getScale:nd,isElement:t9,isRTL:function(e){return"rtl"===nr(e).direction}};function nR(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let nC=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:i,elements:a,middlewareData:s}=t,{element:u,padding:d=0}=tL(e,t)||{};if(null==u)return{};let c=tW(d),f={x:n,y:r},p=tG(t$(o)),g=tH(p),m=await i.getDimensions(u),h="y"===p,v=h?"clientHeight":"clientWidth",b=l.reference[g]+l.reference[p]-f[p]-l.floating[g],w=f[p]-l.reference[p],y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(u)),x=y?y[v]:0;x&&await (null==i.isElement?void 0:i.isElement(y))||(x=a.floating[v]||l.floating[g]);let R=x/2-m[g]/2-1,C=tj(c[h?"top":"left"],R),S=tj(c[h?"bottom":"right"],R),E=x-m[g]-S,P=x/2-m[g]/2+(b/2-w/2),M=tI(C,tj(P,E)),_=!s.arrow&&null!=tz(o)&&P!==M&&l.reference[g]/2-(P<C?C:S)-m[g]/2<0,A=_?P<C?P-C:P-E:0;return{[p]:f[p]+A,data:{[p]:M,centerOffset:P-M-A,..._&&{alignmentOffset:A}},reset:_}}}),nS=(e,t,n)=>{let r=new Map,o={platform:nx,...n},l={...o.platform,_c:r};return tX(e,t,{...o,platform:l})};var nE="undefined"!=typeof document?a.useLayoutEffect:function(){};function nP(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!nP(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!nP(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function nM(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function n_(e,t){let n=nM(e);return Math.round(t*n)/n}function nA(e){let t=a.useRef(e);return nE(()=>{t.current=e}),t}let nF=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?nC({element:n.current,padding:r}).fn(t):{}:n?nC({element:n,padding:r}).fn(t):{}}}),nj=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await tJ(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}),nI=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=tL(e,t),u={x:n,y:r},d=await tY(t,s),c=t$(tV(o)),f=tG(c),p=u[f],g=u[c];if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+d[e],r=p-d[t];p=tI(n,tj(p,r))}if(i){let e="y"===c?"top":"left",t="y"===c?"bottom":"right",n=g+d[e],r=g-d[t];g=tI(n,tj(g,r))}let m=a.fn({...t,[f]:p,[c]:g});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:l,[c]:i}}}}}}(e),options:[e,t]}),nO=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=tL(e,t),d={x:n,y:r},c=t$(o),f=tG(c),p=d[f],g=d[c],m=tL(a,t),h="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=l.reference[f]-l.floating[e]+h.mainAxis,n=l.reference[f]+l.reference[e]-h.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var v,b;let e="y"===f?"width":"height",t=["top","left"].includes(tV(o)),n=l.reference[c]-l.floating[e]+(t&&(null==(v=i.offset)?void 0:v[c])||0)+(t?0:h.crossAxis),r=l.reference[c]+l.reference[e]+(t?0:(null==(b=i.offset)?void 0:b[c])||0)-(t?h.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[f]:p,[c]:g}}}}(e),options:[e,t]}),nN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:s,rects:u,initialPlacement:d,platform:c,elements:f}=t,{mainAxis:p=!0,crossAxis:g=!0,fallbackPlacements:m,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:b=!0,...w}=tL(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let y=tV(a),x=t$(d),R=tV(d)===d,C=await (null==c.isRTL?void 0:c.isRTL(f.floating)),S=m||(R||!b?[tB(d)]:function(e){let t=tB(e);return[tU(e),t,tU(t)]}(d)),E="none"!==v;!m&&E&&S.push(...function(e,t,n,r){let o=tz(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(tV(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(tU)))),l}(d,b,v,C));let P=[d,...S],M=await tY(t,w),_=[],A=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&_.push(M[y]),g){let e=function(e,t,n){void 0===n&&(n=!1);let r=tz(e),o=tG(t$(e)),l=tH(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=tB(i)),[i,tB(i)]}(a,u,C);_.push(M[e[0]],M[e[1]])}if(A=[...A,{placement:a,overflows:_}],!_.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=P[e];if(t&&("alignment"!==g||x===t$(t)||A.every(e=>e.overflows[0]>0&&t$(e.placement)===x)))return{data:{index:e,overflows:A},reset:{placement:t}};let n=null==(l=A.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(h){case"bestFit":{let e=null==(i=A.filter(e=>{if(E){let t=t$(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=d}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),nT=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:i,rects:a,platform:s,elements:u}=t,{apply:d=()=>{},...c}=tL(e,t),f=await tY(t,c),p=tV(i),g=tz(i),m="y"===t$(i),{width:h,height:v}=a.floating;"top"===p||"bottom"===p?(o=p,l=g===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(l=p,o="end"===g?"top":"bottom");let b=v-f.top-f.bottom,w=h-f.left-f.right,y=tj(v-f[o],b),x=tj(h-f[l],w),R=!t.middlewareData.shift,C=y,S=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(S=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(C=b),R&&!g){let e=tI(f.left,0),t=tI(f.right,0),n=tI(f.top,0),r=tI(f.bottom,0);m?S=h-2*(0!==e||0!==t?e+t:tI(f.left,f.right)):C=v-2*(0!==n||0!==r?n+r:tI(f.top,f.bottom))}await d({...t,availableWidth:S,availableHeight:C});let E=await s.getDimensions(u.floating);return h!==E.width||v!==E.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),nD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=tL(e,t);switch(r){case"referenceHidden":{let e=tQ(await tY(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:tZ(e)}}}case"escaped":{let e=tQ(await tY(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:tZ(e)}}}default:return{}}}}}(e),options:[e,t]}),nk=(e,t)=>({...nF(e),options:[e,t]});var nL=a.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,i.jsx)(e5.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,i.jsx)("polygon",{points:"0,0 30,0 15,10"})})});nL.displayName="Arrow";var nV="Popper",[nz,nG]=te(nV),[nH,n$]=nz(nV),nU=e=>{let{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return(0,i.jsx)(nH,{scope:t,anchor:r,onAnchorChange:o,children:n})};nU.displayName=nV;var nB="PopperAnchor",nW=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,l=n$(nB,n),s=a.useRef(null),u=Z(t,s);return a.useEffect(()=>{l.onAnchorChange(r?.current||s.current)}),r?null:(0,i.jsx)(e5.div,{...o,ref:u})});nW.displayName=nB;var nq="PopperContent",[nK,nX]=nz(nq),nY=a.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:l="center",alignOffset:s=0,arrowPadding:u=0,avoidCollisions:d=!0,collisionBoundary:c=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:g=!1,updatePositionStrategy:m="optimized",onPlaced:h,...v}=e,b=n$(nq,n),[w,y]=a.useState(null),x=Z(t,e=>y(e)),[R,C]=a.useState(null),S=function(e){let[t,n]=a.useState(void 0);return tt(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(R),E=S?.width??0,P=S?.height??0,M="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},_=Array.isArray(c)?c:[c],A=_.length>0,F={padding:M,boundary:_.filter(n0),altBoundary:A},{refs:j,floatingStyles:I,placement:O,isPositioned:N,middlewareData:T}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:l,floating:i}={},transform:s=!0,whileElementsMounted:u,open:d}=e,[c,f]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,g]=a.useState(r);nP(p,r)||g(r);let[m,h]=a.useState(null),[v,b]=a.useState(null),w=a.useCallback(e=>{e!==C.current&&(C.current=e,h(e))},[]),y=a.useCallback(e=>{e!==S.current&&(S.current=e,b(e))},[]),x=l||m,R=i||v,C=a.useRef(null),S=a.useRef(null),E=a.useRef(c),P=null!=u,M=nA(u),_=nA(o),A=nA(d),F=a.useCallback(()=>{if(!C.current||!S.current)return;let e={placement:t,strategy:n,middleware:p};_.current&&(e.platform=_.current),nS(C.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};j.current&&!nP(E.current,t)&&(E.current=t,e3.flushSync(()=>{f(t)}))})},[p,t,n,_,A]);nE(()=>{!1===d&&E.current.isPositioned&&(E.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[d]);let j=a.useRef(!1);nE(()=>(j.current=!0,()=>{j.current=!1}),[]),nE(()=>{if(x&&(C.current=x),R&&(S.current=R),x&&R){if(M.current)return M.current(x,R,F);F()}},[x,R,F,M,P]);let I=a.useMemo(()=>({reference:C,floating:S,setReference:w,setFloating:y}),[w,y]),O=a.useMemo(()=>({reference:x,floating:R}),[x,R]),N=a.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=n_(O.floating,c.x),r=n_(O.floating,c.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...nM(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,O.floating,c.x,c.y]);return a.useMemo(()=>({...c,update:F,refs:I,elements:O,floatingStyles:N}),[c,F,I,O,N])}({strategy:"fixed",placement:r+("center"!==l?"-"+l:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,d=nu(e),c=l||i?[...d?ni(d):[],...ni(t)]:[];c.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});let f=d&&s?function(e,t){let n,r=null,o=t3(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function i(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),l();let u=e.getBoundingClientRect(),{left:d,top:c,width:f,height:p}=u;if(a||t(),!f||!p)return;let g=tN(c),m=tN(o.clientWidth-(d+f)),h={rootMargin:-g+"px "+-m+"px "+-tN(o.clientHeight-(c+p))+"px "+-tN(d)+"px",threshold:tI(0,tj(1,s))||1},v=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!v)return i();r?i(!1,r):n=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==r||nR(u,e.getBoundingClientRect())||i(),v=!1}try{r=new IntersectionObserver(b,{...h,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,h)}r.observe(e)}(!0),l}(d,n):null,p=-1,g=null;a&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===d&&g&&(g.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),d&&!u&&g.observe(d),g.observe(t));let m=u?np(e):null;return u&&function t(){let r=np(e);m&&!nR(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;c.forEach(e=>{l&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=g)||e.disconnect(),g=null,u&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===m}),elements:{reference:b.anchor},middleware:[nj({mainAxis:o+P,alignmentAxis:s}),d&&nI({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?nO():void 0,...F}),d&&nN({...F}),nT({...F,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:l}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${n}px`),i.setProperty("--radix-popper-available-height",`${r}px`),i.setProperty("--radix-popper-anchor-width",`${o}px`),i.setProperty("--radix-popper-anchor-height",`${l}px`)}}),R&&nk({element:R,padding:u}),n1({arrowWidth:E,arrowHeight:P}),g&&nD({strategy:"referenceHidden",...F})]}),[D,k]=n2(O),L=td(h);tt(()=>{N&&L?.()},[N,L]);let V=T.arrow?.x,z=T.arrow?.y,G=T.arrow?.centerOffset!==0,[H,$]=a.useState();return tt(()=>{w&&$(window.getComputedStyle(w).zIndex)},[w]),(0,i.jsx)("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:N?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:H,"--radix-popper-transform-origin":[T.transformOrigin?.x,T.transformOrigin?.y].join(" "),...T.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,i.jsx)(nK,{scope:n,placedSide:D,onArrowChange:C,arrowX:V,arrowY:z,shouldHideArrow:G,children:(0,i.jsx)(e5.div,{"data-side":D,"data-align":k,...v,ref:x,style:{...v.style,animation:N?void 0:"none"}})})})});nY.displayName=nq;var nQ="PopperArrow",nZ={top:"bottom",right:"left",bottom:"top",left:"right"},nJ=a.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=nX(nQ,n),l=nZ[o.placedSide];return(0,i.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,i.jsx)(nL,{...r,ref:t,style:{...r.style,display:"block"}})})});function n0(e){return null!==e}nJ.displayName=nQ;var n1=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,l=o.arrow?.centerOffset!==0,i=l?0:e.arrowWidth,a=l?0:e.arrowHeight,[s,u]=n2(n),d={start:"0%",center:"50%",end:"100%"}[u],c=(o.arrow?.x??0)+i/2,f=(o.arrow?.y??0)+a/2,p="",g="";return"bottom"===s?(p=l?d:`${c}px`,g=`${-a}px`):"top"===s?(p=l?d:`${c}px`,g=`${r.floating.height+a}px`):"right"===s?(p=`${-a}px`,g=l?d:`${f}px`):"left"===s&&(p=`${r.floating.width+a}px`,g=l?d:`${f}px`),{data:{x:p,y:g}}}});function n2(e){let[t,n="center"]=e.split("-");return[t,n]}var n3=a.forwardRef((e,t)=>{let{container:n,...r}=e,[o,l]=a.useState(!1);tt(()=>l(!0),[]);let s=n||o&&globalThis?.document?.body;return s?e3.createPortal((0,i.jsx)(e5.div,{...r,ref:t}),s):null});n3.displayName="Portal";var n5=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=a.useState(),l=a.useRef(null),i=a.useRef(e),s=a.useRef("none"),[u,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>n[e][t]??e,t));return a.useEffect(()=>{let e=n9(l.current);s.current="mounted"===u?e:"none"},[u]),tt(()=>{let t=l.current,n=i.current;if(n!==e){let r=s.current,o=n9(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),tt(()=>{if(r){let e,t=r.ownerDocument.defaultView??window,n=n=>{let o=n9(l.current).includes(n.animationName);if(n.target===r&&o&&(d("ANIMATION_END"),!i.current)){let n=r.style.animationFillMode;r.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=n)})}},o=e=>{e.target===r&&(s.current=n9(l.current))};return r.addEventListener("animationstart",o),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{t.clearTimeout(e),r.removeEventListener("animationstart",o),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}d("ANIMATION_END")},[r,d]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):a.Children.only(n),l=Z(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?a.cloneElement(o,{ref:l}):null};function n9(e){return e?.animationName||"none"}n5.displayName="Presence";var n4="rovingFocusGroup.onEntryFocus",n8={bubbles:!1,cancelable:!0},n6="RovingFocusGroup",[n7,re,rt]=to(n6),[rn,rr]=te(n6,[rt]),[ro,rl]=rn(n6),ri=a.forwardRef((e,t)=>(0,i.jsx)(n7.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,i.jsx)(n7.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,i.jsx)(ra,{...e,ref:t})})}));ri.displayName=n6;var ra=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:l,currentTabStopId:s,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:d,onEntryFocus:c,preventScrollOnEntryFocus:f=!1,...p}=e,g=a.useRef(null),m=Z(t,g),h=tu(l),[v,b]=tr({prop:s,defaultProp:u??null,onChange:d,caller:n6}),[w,y]=a.useState(!1),x=td(c),R=re(n),C=a.useRef(!1),[S,E]=a.useState(0);return a.useEffect(()=>{let e=g.current;if(e)return e.addEventListener(n4,x),()=>e.removeEventListener(n4,x)},[x]),(0,i.jsx)(ro,{scope:n,orientation:r,dir:h,loop:o,currentTabStopId:v,onItemFocus:a.useCallback(e=>b(e),[b]),onItemShiftTab:a.useCallback(()=>y(!0),[]),onFocusableItemAdd:a.useCallback(()=>E(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>E(e=>e-1),[]),children:(0,i.jsx)(e5.div,{tabIndex:w||0===S?-1:0,"data-orientation":r,...p,ref:m,style:{outline:"none",...e.style},onMouseDown:e7(e.onMouseDown,()=>{C.current=!0}),onFocus:e7(e.onFocus,e=>{let t=!C.current;if(e.target===e.currentTarget&&t&&!w){let t=new CustomEvent(n4,n8);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=R().filter(e=>e.focusable);rc([e.find(e=>e.active),e.find(e=>e.id===v),...e].filter(Boolean).map(e=>e.ref.current),f)}}C.current=!1}),onBlur:e7(e.onBlur,()=>y(!1))})})}),rs="RovingFocusGroupItem",ru=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:l,children:s,...u}=e,d=tA(),c=l||d,f=rl(rs,n),p=f.currentTabStopId===c,g=re(n),{onFocusableItemAdd:m,onFocusableItemRemove:h,currentTabStopId:v}=f;return a.useEffect(()=>{if(r)return m(),()=>h()},[r,m,h]),(0,i.jsx)(n7.ItemSlot,{scope:n,id:c,focusable:r,active:o,children:(0,i.jsx)(e5.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...u,ref:t,onMouseDown:e7(e.onMouseDown,e=>{r?f.onItemFocus(c):e.preventDefault()}),onFocus:e7(e.onFocus,()=>f.onItemFocus(c)),onKeyDown:e7(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return rd[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>rc(n))}}),children:"function"==typeof s?s({isCurrentTabStop:p,hasTabStop:null!=v}):s})})});ru.displayName=rs;var rd={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function rc(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var rf=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},rp=new WeakMap,rg=new WeakMap,rm={},rh=0,rv=function(e){return e&&(e.host||rv(e.parentNode))},rb=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=rv(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});rm[n]||(rm[n]=new WeakMap);var l=rm[n],i=[],a=new Set,s=new Set(o),u=function(e){!e||a.has(e)||(a.add(e),u(e.parentNode))};o.forEach(u);var d=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))d(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,s=(rp.get(e)||0)+1,u=(l.get(e)||0)+1;rp.set(e,s),l.set(e,u),i.push(e),1===s&&o&&rg.set(e,!0),1===u&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return d(t),a.clear(),rh++,function(){i.forEach(function(e){var t=rp.get(e)-1,o=l.get(e)-1;rp.set(e,t),l.set(e,o),t||(rg.has(e)||e.removeAttribute(r),rg.delete(e)),o||e.removeAttribute(n)}),--rh||(rp=new WeakMap,rp=new WeakMap,rg=new WeakMap,rm={})}},rw=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||rf(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),rb(r,o,n,"aria-hidden")):function(){return null}},ry=function(){return(ry=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function rx(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var rR=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),rC="width-before-scroll-bar";function rS(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var rE="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,rP=new WeakMap;function rM(e){return e}var r_=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=rM),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var l=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(l)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return l.options=ry({async:!0,ssr:!1},e),l}(),rA=function(){},rF=a.forwardRef(function(e,t){var n,r,o,l,i=a.useRef(null),s=a.useState({onScrollCapture:rA,onWheelCapture:rA,onTouchMoveCapture:rA}),u=s[0],d=s[1],c=e.forwardProps,f=e.children,p=e.className,g=e.removeScrollBar,m=e.enabled,h=e.shards,v=e.sideCar,b=e.noRelative,w=e.noIsolation,y=e.inert,x=e.allowPinchZoom,R=e.as,C=e.gapMode,S=rx(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=(n=[i,t],r=function(e){return n.forEach(function(t){return rS(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,rE(function(){var e=rP.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||rS(e,null)}),r.forEach(function(e){t.has(e)||rS(e,o)})}rP.set(l,n)},[n]),l),P=ry(ry({},S),u);return a.createElement(a.Fragment,null,m&&a.createElement(v,{sideCar:r_,removeScrollBar:g,shards:h,noRelative:b,noIsolation:w,inert:y,setCallbacks:d,allowPinchZoom:!!x,lockRef:i,gapMode:C}),c?a.cloneElement(a.Children.only(f),ry(ry({},P),{ref:E})):a.createElement(void 0===R?"div":R,ry({},P,{className:p,ref:E}),f))});rF.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},rF.classNames={fullWidth:rC,zeroRight:rR};var rj=function(e){var t=e.sideCar,n=rx(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,ry({},n))};rj.isSideCarExport=!0;var rI=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=l||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},rO=function(){var e=rI();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},rN=function(){var e=rO();return function(t){return e(t.styles,t.dynamic),null}},rT={left:0,top:0,right:0,gap:0},rD=function(e){return parseInt(e||"",10)||0},rk=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[rD(n),rD(r),rD(o)]},rL=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return rT;var t=rk(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},rV=rN(),rz="data-scroll-locked",rG=function(e,t,n,r){var o=e.left,l=e.top,i=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(rz,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(rR," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(rC," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(rR," .").concat(rR," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(rC," .").concat(rC," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(rz,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},rH=function(){var e=parseInt(document.body.getAttribute(rz)||"0",10);return isFinite(e)?e:0},r$=function(){a.useEffect(function(){return document.body.setAttribute(rz,(rH()+1).toString()),function(){var e=rH()-1;e<=0?document.body.removeAttribute(rz):document.body.setAttribute(rz,e.toString())}},[])},rU=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;r$();var l=a.useMemo(function(){return rL(o)},[o]);return a.createElement(rV,{styles:rG(l,!t,o,n?"":"!important")})},rB=!1;if("undefined"!=typeof window)try{var rW=Object.defineProperty({},"passive",{get:function(){return rB=!0,!0}});window.addEventListener("test",rW,rW),window.removeEventListener("test",rW,rW)}catch(e){rB=!1}var rq=!!rB&&{passive:!1},rK=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},rX=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),rY(e,r)){var o=rQ(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},rY=function(e,t){return"v"===e?rK(t,"overflowY"):rK(t,"overflowX")},rQ=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},rZ=function(e,t,n,r,o){var l,i=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),a=i*r,s=n.target,u=t.contains(s),d=!1,c=a>0,f=0,p=0;do{if(!s)break;var g=rQ(e,s),m=g[0],h=g[1]-g[2]-i*m;(m||h)&&rY(e,s)&&(f+=h,p+=m);var v=s.parentNode;s=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return c&&(o&&1>Math.abs(f)||!o&&a>f)?d=!0:!c&&(o&&1>Math.abs(p)||!o&&-a>p)&&(d=!0),d},rJ=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},r0=function(e){return[e.deltaX,e.deltaY]},r1=function(e){return e&&"current"in e?e.current:e},r2=0,r3=[];let r5=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(r2++)[0],l=a.useState(rN)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,l=t.length;o<l;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(r1),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,l=rJ(e),a=n.current,s="deltaX"in e?e.deltaX:a[0]-l[0],u="deltaY"in e?e.deltaY:a[1]-l[1],d=e.target,c=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===c&&"range"===d.type)return!1;var f=rX(c,d);if(!f)return!0;if(f?o=c:(o="v"===c?"h":"v",f=rX(c,d)),!f)return!1;if(!r.current&&"changedTouches"in e&&(s||u)&&(r.current=o),!o)return!0;var p=r.current||o;return rZ(p,t,e,"h"===p?s:u,!0)},[]),u=a.useCallback(function(e){if(r3.length&&r3[r3.length-1]===l){var n="deltaY"in e?r0(e):rJ(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(r1).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=a.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),c=a.useCallback(function(e){n.current=rJ(e),r.current=void 0},[]),f=a.useCallback(function(t){d(t.type,r0(t),t.target,s(t,e.lockRef.current))},[]),p=a.useCallback(function(t){d(t.type,rJ(t),t.target,s(t,e.lockRef.current))},[]);a.useEffect(function(){return r3.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,rq),document.addEventListener("touchmove",u,rq),document.addEventListener("touchstart",c,rq),function(){r3=r3.filter(function(e){return e!==l}),document.removeEventListener("wheel",u,rq),document.removeEventListener("touchmove",u,rq),document.removeEventListener("touchstart",c,rq)}},[]);var g=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,g?a.createElement(rU,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},r_.useMedium(r),rj);var r9=a.forwardRef(function(e,t){return a.createElement(rF,ry({},e,{ref:t,sideCar:r5}))});r9.classNames=rF.classNames;var r4=["Enter"," "],r8=["ArrowUp","PageDown","End"],r6=["ArrowDown","PageUp","Home",...r8],r7={ltr:[...r4,"ArrowRight"],rtl:[...r4,"ArrowLeft"]},oe={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ot="Menu",[on,or,oo]=to(ot),[ol,oi]=te(ot,[oo,nG,rr]),oa=nG(),os=rr(),[ou,od]=ol(ot),[oc,of]=ol(ot),op=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:l,modal:s=!0}=e,u=oa(t),[d,c]=a.useState(null),f=a.useRef(!1),p=td(l),g=tu(o);return a.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,i.jsx)(nU,{...u,children:(0,i.jsx)(ou,{scope:t,open:n,onOpenChange:p,content:d,onContentChange:c,children:(0,i.jsx)(oc,{scope:t,onClose:a.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:g,modal:s,children:r})})})};op.displayName=ot;var og=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=oa(n);return(0,i.jsx)(nW,{...o,...r,ref:t})});og.displayName="MenuAnchor";var om="MenuPortal",[oh,ov]=ol(om,{forceMount:void 0}),ob=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,l=od(om,t);return(0,i.jsx)(oh,{scope:t,forceMount:n,children:(0,i.jsx)(n5,{present:n||l.open,children:(0,i.jsx)(n3,{asChild:!0,container:o,children:r})})})};ob.displayName=om;var ow="MenuContent",[oy,ox]=ol(ow),oR=a.forwardRef((e,t)=>{let n=ov(ow,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,l=od(ow,e.__scopeMenu),a=of(ow,e.__scopeMenu);return(0,i.jsx)(on.Provider,{scope:e.__scopeMenu,children:(0,i.jsx)(n5,{present:r||l.open,children:(0,i.jsx)(on.Slot,{scope:e.__scopeMenu,children:a.modal?(0,i.jsx)(oC,{...o,ref:t}):(0,i.jsx)(oS,{...o,ref:t})})})})}),oC=a.forwardRef((e,t)=>{let n=od(ow,e.__scopeMenu),r=a.useRef(null),o=Z(t,r);return a.useEffect(()=>{let e=r.current;if(e)return rw(e)},[]),(0,i.jsx)(oP,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:e7(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),oS=a.forwardRef((e,t)=>{let n=od(ow,e.__scopeMenu);return(0,i.jsx)(oP,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),oE=J("MenuContent.ScrollLock"),oP=a.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:s,disableOutsidePointerEvents:u,onEntryFocus:d,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:g,onDismiss:m,disableOutsideScroll:h,...v}=e,b=od(ow,n),w=of(ow,n),y=oa(n),x=os(n),R=or(n),[C,S]=a.useState(null),E=a.useRef(null),P=Z(t,E,b.onContentChange),M=a.useRef(0),_=a.useRef(""),A=a.useRef(0),F=a.useRef(null),j=a.useRef("right"),I=a.useRef(0),O=h?r9:a.Fragment,N=e=>{let t=_.current+e,n=R().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,i=(r=Math.max(l,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(n.map(e=>e.textValue),t,o),i=n.find(e=>e.textValue===l)?.ref.current;!function e(t){_.current=t,window.clearTimeout(M.current),""!==t&&(M.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};a.useEffect(()=>()=>window.clearTimeout(M.current),[]),a.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??tv()),document.body.insertAdjacentElement("beforeend",e[1]??tv()),th++,()=>{1===th&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),th--}},[]);let T=a.useCallback(e=>j.current===F.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,d=a.x,c=a.y;u>r!=c>r&&n<(d-s)*(r-u)/(c-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,F.current?.area),[]);return(0,i.jsx)(oy,{scope:n,searchRef:_,onItemEnter:a.useCallback(e=>{T(e)&&e.preventDefault()},[T]),onItemLeave:a.useCallback(e=>{T(e)||(E.current?.focus(),S(null))},[T]),onTriggerLeave:a.useCallback(e=>{T(e)&&e.preventDefault()},[T]),pointerGraceTimerRef:A,onPointerGraceIntentChange:a.useCallback(e=>{F.current=e},[]),children:(0,i.jsx)(O,{...h?{as:oE,allowPinchZoom:!0}:void 0,children:(0,i.jsx)(tx,{asChild:!0,trapped:o,onMountAutoFocus:e7(l,e=>{e.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,i.jsx)(tp,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:g,onDismiss:m,children:(0,i.jsx)(ri,{asChild:!0,...x,dir:w.dir,orientation:"vertical",loop:r,currentTabStopId:C,onCurrentTabStopIdChange:S,onEntryFocus:e7(d,e=>{w.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,i.jsx)(nY,{role:"menu","aria-orientation":"vertical","data-state":oJ(b.open),"data-radix-menu-content":"",dir:w.dir,...y,...v,ref:P,style:{outline:"none",...v.style},onKeyDown:e7(v.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&N(e.key));let o=E.current;if(e.target!==o||!r6.includes(e.key))return;e.preventDefault();let l=R().filter(e=>!e.disabled).map(e=>e.ref.current);r8.includes(e.key)&&l.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(l)}),onBlur:e7(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(M.current),_.current="")}),onPointerMove:e7(e.onPointerMove,o2(e=>{let t=e.target,n=I.current!==e.clientX;e.currentTarget.contains(t)&&n&&(j.current=e.clientX>I.current?"right":"left",I.current=e.clientX)}))})})})})})})});oR.displayName=ow;var oM=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,i.jsx)(e5.div,{role:"group",...r,ref:t})});oM.displayName="MenuGroup";var o_=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,i.jsx)(e5.div,{...r,ref:t})});o_.displayName="MenuLabel";var oA="MenuItem",oF="menu.itemSelect",oj=a.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,l=a.useRef(null),s=of(oA,e.__scopeMenu),u=ox(oA,e.__scopeMenu),d=Z(t,l),c=a.useRef(!1);return(0,i.jsx)(oI,{...o,ref:d,disabled:n,onClick:e7(e.onClick,()=>{let e=l.current;if(!n&&e){let t=new CustomEvent(oF,{bubbles:!0,cancelable:!0});e.addEventListener(oF,e=>r?.(e),{once:!0}),e9(e,t),t.defaultPrevented?c.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),c.current=!0},onPointerUp:e7(e.onPointerUp,e=>{c.current||e.currentTarget?.click()}),onKeyDown:e7(e.onKeyDown,e=>{let t=""!==u.searchRef.current;n||t&&" "===e.key||r4.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});oj.displayName=oA;var oI=a.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...l}=e,s=ox(oA,n),u=os(n),d=a.useRef(null),c=Z(t,d),[f,p]=a.useState(!1),[g,m]=a.useState("");return a.useEffect(()=>{let e=d.current;e&&m((e.textContent??"").trim())},[l.children]),(0,i.jsx)(on.ItemSlot,{scope:n,disabled:r,textValue:o??g,children:(0,i.jsx)(ru,{asChild:!0,...u,focusable:!r,children:(0,i.jsx)(e5.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...l,ref:c,onPointerMove:e7(e.onPointerMove,o2(e=>{r?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:e7(e.onPointerLeave,o2(e=>s.onItemLeave(e))),onFocus:e7(e.onFocus,()=>p(!0)),onBlur:e7(e.onBlur,()=>p(!1))})})})}),oO=a.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,i.jsx)(oG,{scope:e.__scopeMenu,checked:n,children:(0,i.jsx)(oj,{role:"menuitemcheckbox","aria-checked":o0(n)?"mixed":n,...o,ref:t,"data-state":o1(n),onSelect:e7(o.onSelect,()=>r?.(!!o0(n)||!n),{checkForDefaultPrevented:!1})})})});oO.displayName="MenuCheckboxItem";var oN="MenuRadioGroup",[oT,oD]=ol(oN,{value:void 0,onValueChange:()=>{}}),ok=a.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,l=td(r);return(0,i.jsx)(oT,{scope:e.__scopeMenu,value:n,onValueChange:l,children:(0,i.jsx)(oM,{...o,ref:t})})});ok.displayName=oN;var oL="MenuRadioItem",oV=a.forwardRef((e,t)=>{let{value:n,...r}=e,o=oD(oL,e.__scopeMenu),l=n===o.value;return(0,i.jsx)(oG,{scope:e.__scopeMenu,checked:l,children:(0,i.jsx)(oj,{role:"menuitemradio","aria-checked":l,...r,ref:t,"data-state":o1(l),onSelect:e7(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});oV.displayName=oL;var oz="MenuItemIndicator",[oG,oH]=ol(oz,{checked:!1}),o$=a.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,l=oH(oz,n);return(0,i.jsx)(n5,{present:r||o0(l.checked)||!0===l.checked,children:(0,i.jsx)(e5.span,{...o,ref:t,"data-state":o1(l.checked)})})});o$.displayName=oz;var oU=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,i.jsx)(e5.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});oU.displayName="MenuSeparator";var oB=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=oa(n);return(0,i.jsx)(nJ,{...o,...r,ref:t})});oB.displayName="MenuArrow";var oW="MenuSub",[oq,oK]=ol(oW);var oX="MenuSubTrigger",oY=a.forwardRef((e,t)=>{let n=od(oX,e.__scopeMenu),r=of(oX,e.__scopeMenu),o=oK(oX,e.__scopeMenu),l=ox(oX,e.__scopeMenu),s=a.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:d}=l,c={__scopeMenu:e.__scopeMenu},f=a.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return a.useEffect(()=>f,[f]),a.useEffect(()=>{let e=u.current;return()=>{window.clearTimeout(e),d(null)}},[u,d]),(0,i.jsx)(og,{asChild:!0,...c,children:(0,i.jsx)(oI,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":oJ(n.open),...e,ref:Q(t,o.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:e7(e.onPointerMove,o2(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(l.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:e7(e.onPointerLeave,o2(e=>{f();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(u.current),u.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:e7(e.onKeyDown,t=>{let o=""!==l.searchRef.current;e.disabled||o&&" "===t.key||r7[r.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});oY.displayName=oX;var oQ="MenuSubContent",oZ=a.forwardRef((e,t)=>{let n=ov(ow,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,l=od(ow,e.__scopeMenu),s=of(ow,e.__scopeMenu),u=oK(oQ,e.__scopeMenu),d=a.useRef(null),c=Z(t,d);return(0,i.jsx)(on.Provider,{scope:e.__scopeMenu,children:(0,i.jsx)(n5,{present:r||l.open,children:(0,i.jsx)(on.Slot,{scope:e.__scopeMenu,children:(0,i.jsx)(oP,{id:u.contentId,"aria-labelledby":u.triggerId,...o,ref:c,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:e7(e.onFocusOutside,e=>{e.target!==u.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:e7(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:e7(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=oe[s.dir].includes(e.key);t&&n&&(l.onOpenChange(!1),u.trigger?.focus(),e.preventDefault())})})})})})});function oJ(e){return e?"open":"closed"}function o0(e){return"indeterminate"===e}function o1(e){return o0(e)?"indeterminate":e?"checked":"unchecked"}function o2(e){return t=>"mouse"===t.pointerType?e(t):void 0}oZ.displayName=oQ;var o3="DropdownMenu",[o5,o9]=te(o3,[oi]),o4=oi(),[o8,o6]=o5(o3);var o7="DropdownMenuTrigger";a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,l=o6(o7,n),a=o4(n);return(0,i.jsx)(og,{asChild:!0,...a,children:(0,i.jsx)(e5.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:Q(t,l.triggerRef),onPointerDown:e7(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:e7(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})}).displayName=o7;var le=e=>{let{__scopeDropdownMenu:t,...n}=e,r=o4(t);return(0,i.jsx)(ob,{...r,...n})};le.displayName="DropdownMenuPortal";var lt="DropdownMenuContent",ln=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o6(lt,n),l=o4(n),s=a.useRef(!1);return(0,i.jsx)(oR,{id:o.contentId,"aria-labelledby":o.triggerId,...l,...r,ref:t,onCloseAutoFocus:e7(e.onCloseAutoFocus,e=>{s.current||o.triggerRef.current?.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:e7(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ln.displayName=lt,a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(oM,{...o,...r,ref:t})}).displayName="DropdownMenuGroup";var lr=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(o_,{...o,...r,ref:t})});lr.displayName="DropdownMenuLabel";var lo=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(oj,{...o,...r,ref:t})});lo.displayName="DropdownMenuItem";var ll=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(oO,{...o,...r,ref:t})});ll.displayName="DropdownMenuCheckboxItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(ok,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup";var li=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(oV,{...o,...r,ref:t})});li.displayName="DropdownMenuRadioItem";var la=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(o$,{...o,...r,ref:t})});la.displayName="DropdownMenuItemIndicator";var ls=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(oU,{...o,...r,ref:t})});ls.displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(oB,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var lu=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(oY,{...o,...r,ref:t})});lu.displayName="DropdownMenuSubTrigger";var ld=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=o4(n);return(0,i.jsx)(oZ,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ld.displayName="DropdownMenuSubContent";let lc=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),lf=(...e)=>e.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ");var lp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let lg=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:l,iconNode:i,...s},u)=>(0,a.createElement)("svg",{ref:u,...lp,width:t,height:t,stroke:e,strokeWidth:r?24*Number(n)/Number(t):n,className:lf("lucide",o),...s},[...i.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(l)?l:[l]])),lm=(e,t)=>{let n=(0,a.forwardRef)(({className:n,...r},o)=>(0,a.createElement)(lg,{ref:o,iconNode:t,className:lf(`lucide-${lc(e)}`,n),...r}));return n.displayName=`${e}`,n},lh=lm("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),lv=lm("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),lb=lm("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);a.forwardRef(({className:e,inset:t,children:n,...r},o)=>(0,i.jsxs)(lu,{ref:o,className:eJ("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[n,(0,i.jsx)(lh,{className:"ml-auto h-4 w-4"})]})).displayName=lu.displayName,a.forwardRef(({className:e,...t},n)=>(0,i.jsx)(ld,{ref:n,className:eJ("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=ld.displayName,a.forwardRef(({className:e,sideOffset:t=4,...n},r)=>(0,i.jsx)(le,{children:(0,i.jsx)(ln,{ref:r,sideOffset:t,className:eJ("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})})).displayName=ln.displayName,a.forwardRef(({className:e,inset:t,...n},r)=>(0,i.jsx)(lo,{ref:r,className:eJ("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n})).displayName=lo.displayName,a.forwardRef(({className:e,children:t,checked:n,...r},o)=>(0,i.jsxs)(ll,{ref:o,className:eJ("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[(0,i.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,i.jsx)(la,{children:(0,i.jsx)(lv,{className:"h-4 w-4"})})}),t]})).displayName=ll.displayName,a.forwardRef(({className:e,children:t,...n},r)=>(0,i.jsxs)(li,{ref:r,className:eJ("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[(0,i.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,i.jsx)(la,{children:(0,i.jsx)(lb,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=li.displayName,a.forwardRef(({className:e,inset:t,...n},r)=>(0,i.jsx)(lr,{ref:r,className:eJ("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n})).displayName=lr.displayName,a.forwardRef(({className:e,...t},n)=>(0,i.jsx)(ls,{ref:n,className:eJ("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=ls.displayName;let lw=a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("div",{className:"relative w-full overflow-auto",children:(0,i.jsx)("table",{ref:n,className:eJ("w-full caption-bottom text-sm",e),...t})}));lw.displayName="Table";let ly=a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("thead",{ref:n,className:eJ("[&_tr]:border-b",e),...t}));ly.displayName="TableHeader";let lx=a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("tbody",{ref:n,className:eJ("[&_tr:last-child]:border-0",e),...t}));lx.displayName="TableBody",a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("tfoot",{ref:n,className:eJ("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let lR=a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("tr",{ref:n,className:eJ("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));lR.displayName="TableRow";let lC=a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("th",{ref:n,className:eJ("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));lC.displayName="TableHead";let lS=a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("td",{ref:n,className:eJ("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));lS.displayName="TableCell",a.forwardRef(({className:e,...t},n)=>(0,i.jsx)("caption",{ref:n,className:eJ("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption";let lE=[{id:"1",name:"John Doe",email:"<EMAIL>",joinDate:"2023-01-15",status:"Aktif"},{id:"2",name:"Jane Smith",email:"<EMAIL>",joinDate:"2023-02-20",status:"Aktif"},{id:"3",name:"Sam Wilson",email:"<EMAIL>",joinDate:"2023-03-10",status:"Nonaktif"}],lP=[{accessorKey:"name",header:"Nama"},{accessorKey:"email",header:"Email"},{accessorKey:"joinDate",header:"Tanggal Bergabung"},{accessorKey:"status",header:"Status"},{id:"actions",cell:({row:e})=>(e.original,(0,i.jsx)(e1,{variant:"ghost",size:"sm",children:"Aksi"}))}];function lM(){let[e]=(0,a.useState)(()=>[...lE]),[t,n]=(0,a.useState)([]),[r,o]=(0,a.useState)([]),l=function(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=a.useState(()=>({current:function(e){var t,n;let r=[...K,...null!=(t=e._features)?t:[]],o={_features:r},l=o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(o)),{}),i=e=>o.options.mergeOptions?o.options.mergeOptions(l,e):{...l,...e},a={...null!=(n=e.initialState)?n:{}};o._features.forEach(e=>{var t;a=null!=(t=null==e.getInitialState?void 0:e.getInitialState(a))?t:a});let s=[],d=!1,c={_features:r,options:{...l,...e},initialState:a,_queue:e=>{s.push(e),d||(d=!0,Promise.resolve().then(()=>{for(;s.length;)s.shift()();d=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{o.setState(o.initialState)},setOptions:e=>{let t=u(e,o.options);o.options=i(t)},getState:()=>o.options.state,setState:e=>{null==o.options.onStateChange||o.options.onStateChange(e)},_getRowId:(e,t,n)=>{var r;return null!=(r=null==o.options.getRowId?void 0:o.options.getRowId(e,t,n))?r:`${n?[n.id,t].join("."):t}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(e,t)=>{let n=(t?o.getPrePaginationRowModel():o.getRowModel()).rowsById[e];if(!n&&!(n=o.getCoreRowModel().rowsById[e]))throw Error();return n},_getDefaultColumnDef:f(()=>[o.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,n;return null!=(t=null==(n=e.renderValue())||null==n.toString?void 0:n.toString())?t:null},...o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},p(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>o.options.columns,getAllColumns:f(()=>[o._getColumnDefs()],e=>{let t=function(e,n,r){return void 0===r&&(r=0),e.map(e=>{let l=function(e,t,n,r){var o,l;let i,a={...e._getDefaultColumnDef(),...t},s=a.accessorKey,u=null!=(o=null!=(l=a.id)?l:s?"function"==typeof String.prototype.replaceAll?s.replaceAll(".","_"):s.replace(/\./g,"_"):void 0)?o:"string"==typeof a.header?a.header:void 0;if(a.accessorFn?i=a.accessorFn:s&&(i=s.includes(".")?e=>{let t=e;for(let e of s.split(".")){var n;t=null==(n=t)?void 0:n[e]}return t}:e=>e[a.accessorKey]),!u)throw Error();let d={id:`${String(u)}`,accessorFn:i,parent:r,depth:n,columnDef:a,columns:[],getFlatColumns:f(()=>[!0],()=>{var e;return[d,...null==(e=d.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},p(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:f(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=d.columns)&&t.length?e(d.columns.flatMap(e=>e.getLeafColumns())):[d]},p(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(d,e);return d}(o,e,r,n);return l.columns=e.columns?t(e.columns,l,r+1):[],l})};return t(e)},p(e,"debugColumns","getAllColumns")),getAllFlatColumns:f(()=>[o.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),p(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:f(()=>[o.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),p(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:f(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),p(e,"debugColumns","getAllLeafColumns")),getColumn:e=>o._getAllFlatColumnsById()[e]};Object.assign(o,c);for(let e=0;e<o._features.length;e++){let t=o._features[e];null==t||null==t.createTable||t.createTable(o)}return o}(t)})),[r,o]=a.useState(()=>n.current.initialState);return n.current.setOptions(t=>({...t,...e,state:{...r,...e.state},onStateChange:t=>{o(t),null==e.onStateChange||e.onStateChange(t)}})),n.current}({data:e,columns:lP,getCoreRowModel:e=>f(()=>[e.options.data],t=>{let n={rows:[],flatRows:[],rowsById:{}},r=function(t,o,l){void 0===o&&(o=0);let i=[];for(let s=0;s<t.length;s++){let u=v(e,e._getRowId(t[s],s,l),t[s],s,o,void 0,null==l?void 0:l.id);if(n.flatRows.push(u),n.rowsById[u.id]=u,i.push(u),e.options.getSubRows){var a;u.originalSubRows=e.options.getSubRows(t[s],s),null!=(a=u.originalSubRows)&&a.length&&(u.subRows=r(u.originalSubRows,o+1,u))}}return i};return n.rows=r(t),n},p(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex())),getPaginationRowModel:e=>f(()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded],(t,n)=>{let r;if(!n.rows.length)return n;let{pageSize:o,pageIndex:l}=t,{rows:i,flatRows:a,rowsById:s}=n,u=o*l;i=i.slice(u,u+o),(r=e.options.paginateExpandedRows?{rows:i,flatRows:a,rowsById:s}:function(e){let t=[],n=e=>{var r;t.push(e),null!=(r=e.subRows)&&r.length&&e.getIsExpanded()&&e.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}({rows:i,flatRows:a,rowsById:s})).flatRows=[];let d=e=>{r.flatRows.push(e),e.subRows.length&&e.subRows.forEach(d)};return r.rows.forEach(d),r},p(e.options,"debugTable","getPaginationRowModel")),onSortingChange:n,getSortedRowModel:e=>f(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(null!=t&&t.length))return n;let r=e.getState().sorting,o=[],l=r.filter(t=>{var n;return null==(n=e.getColumn(t.id))?void 0:n.getCanSort()}),i={};l.forEach(t=>{let n=e.getColumn(t.id);n&&(i[t.id]={sortUndefined:n.columnDef.sortUndefined,invertSorting:n.columnDef.invertSorting,sortingFn:n.getSortingFn()})});let a=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let r=0;r<l.length;r+=1){var n;let o=l[r],a=i[o.id],s=a.sortUndefined,u=null!=(n=null==o?void 0:o.desc)&&n,d=0;if(s){let n=e.getValue(o.id),r=t.getValue(o.id),l=void 0===n,i=void 0===r;if(l||i){if("first"===s)return l?-1:1;if("last"===s)return l?1:-1;d=l&&i?0:l?s:-s}}if(0===d&&(d=a.sortingFn(e,t,o.id)),0!==d)return u&&(d*=-1),a.invertSorting&&(d*=-1),d}return e.index-t.index}),t.forEach(e=>{var t;o.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=a(e.subRows))}),t};return{rows:a(n.rows),flatRows:o,rowsById:n.rowsById}},p(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex())),onColumnFiltersChange:o,getFilteredRowModel:e=>f(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,n,r)=>{var o,l,i;let a,s;if(!t.rows.length||!(null!=n&&n.length)&&!r){for(let e=0;e<t.flatRows.length;e++)t.flatRows[e].columnFilters={},t.flatRows[e].columnFiltersMeta={};return t}let u=[],d=[];(null!=n?n:[]).forEach(t=>{var n;let r=e.getColumn(t.id);if(!r)return;let o=r.getFilterFn();o&&u.push({id:t.id,filterFn:o,resolvedValue:null!=(n=null==o.resolveFilterValue?void 0:o.resolveFilterValue(t.value))?n:t.value})});let c=(null!=n?n:[]).map(e=>e.id),f=e.getGlobalFilterFn(),p=e.getAllLeafColumns().filter(e=>e.getCanGlobalFilter());r&&f&&p.length&&(c.push("__global__"),p.forEach(e=>{var t;d.push({id:e.id,filterFn:f,resolvedValue:null!=(t=null==f.resolveFilterValue?void 0:f.resolveFilterValue(r))?t:r})}));for(let e=0;e<t.flatRows.length;e++){let n=t.flatRows[e];if(n.columnFilters={},u.length)for(let e=0;e<u.length;e++){let t=(a=u[e]).id;n.columnFilters[t]=a.filterFn(n,t,a.resolvedValue,e=>{n.columnFiltersMeta[t]=e})}if(d.length){for(let e=0;e<d.length;e++){let t=(s=d[e]).id;if(s.filterFn(n,t,s.resolvedValue,e=>{n.columnFiltersMeta[t]=e})){n.columnFilters.__global__=!0;break}}!0!==n.columnFilters.__global__&&(n.columnFilters.__global__=!1)}}return o=t.rows,l=e=>{for(let t=0;t<c.length;t++)if(!1===e.columnFilters[c[t]])return!1;return!0},(i=e).options.filterFromLeafRows?function(e,t,n){var r;let o=[],l={},i=null!=(r=n.options.maxLeafRowFilterDepth)?r:100,a=function(e,r){void 0===r&&(r=0);let s=[];for(let d=0;d<e.length;d++){var u;let c=e[d],f=v(n,c.id,c.original,c.index,c.depth,void 0,c.parentId);if(f.columnFilters=c.columnFilters,null!=(u=c.subRows)&&u.length&&r<i){if(f.subRows=a(c.subRows,r+1),t(c=f)&&!f.subRows.length||t(c)||f.subRows.length){s.push(c),l[c.id]=c,o.push(c);continue}}else t(c=f)&&(s.push(c),l[c.id]=c,o.push(c))}return s};return{rows:a(e),flatRows:o,rowsById:l}}(o,l,i):function(e,t,n){var r;let o=[],l={},i=null!=(r=n.options.maxLeafRowFilterDepth)?r:100,a=function(e,r){void 0===r&&(r=0);let s=[];for(let d=0;d<e.length;d++){let c=e[d];if(t(c)){var u;if(null!=(u=c.subRows)&&u.length&&r<i){let e=v(n,c.id,c.original,c.index,c.depth,void 0,c.parentId);e.subRows=a(c.subRows,r+1),c=e}s.push(c),o.push(c),l[c.id]=c}}return s};return{rows:a(e),flatRows:o,rowsById:l}}(o,l,i)},p(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex())),state:{sorting:t,columnFilters:r}});return(0,i.jsxs)(e6,{children:[(0,i.jsx)("div",{className:"p-4",children:(0,i.jsx)(e2,{placeholder:"Cari pengguna...",value:l.getColumn("name")?.getFilterValue()??"",onChange:e=>l.getColumn("name")?.setFilterValue(e.target.value),className:"max-w-sm"})}),(0,i.jsx)("div",{className:"rounded-md border",children:(0,i.jsxs)(lw,{children:[(0,i.jsx)(ly,{children:l.getHeaderGroups().map(e=>(0,i.jsx)(lR,{children:e.headers.map(e=>(0,i.jsx)(lC,{children:e.isPlaceholder?null:X(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,i.jsx)(lx,{children:l.getRowModel().rows?.length?l.getRowModel().rows.map(e=>(0,i.jsx)(lR,{"data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,i.jsx)(lS,{children:X(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,i.jsx)(lR,{children:(0,i.jsx)(lS,{colSpan:lP.length,className:"h-24 text-center",children:"No results."})})})]})}),(0,i.jsxs)("div",{className:"flex items-center justify-end space-x-2 p-4",children:[(0,i.jsx)(e1,{variant:"outline",size:"sm",onClick:()=>l.previousPage(),disabled:!l.getCanPreviousPage(),children:"Previous"}),(0,i.jsx)(e1,{variant:"outline",size:"sm",onClick:()=>l.nextPage(),disabled:!l.getCanNextPage(),children:"Next"})]})]})}},4030:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,9355,23)),Promise.resolve().then(n.t.bind(n,4439,23)),Promise.resolve().then(n.t.bind(n,7851,23)),Promise.resolve().then(n.t.bind(n,4730,23)),Promise.resolve().then(n.t.bind(n,9774,23)),Promise.resolve().then(n.t.bind(n,3170,23)),Promise.resolve().then(n.t.bind(n,968,23)),Promise.resolve().then(n.t.bind(n,8298,23))},4773:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{normalizeAppPath:function(){return l},normalizeRscURL:function(){return i}});let r=n(7004),o=n(3566);function l(e){return(0,r.ensureLeadingSlash)(e.split("/").reduce((e,t,n,r)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&n===r.length-1?e:e+"/"+t,""))}function i(e){return e.replace(/\.rsc($|\?)/,"$1")}},5003:(e,t)=>{"use strict";function n(e){let t=5381;for(let n=0;n<e.length;n++)t=(t<<5)+t+e.charCodeAt(n)|0;return t>>>0}function r(e){return n(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{djb2Hash:function(){return n},hexHash:function(){return r}})},5138:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return g},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return v},NormalizeError:function(){return m},PageNotFoundError:function(){return h},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return s},getLocationOrigin:function(){return i},getURL:function(){return a},isAbsoluteUrl:function(){return l},isResSent:function(){return u},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return w}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),l=0;l<r;l++)o[l]=arguments[l];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>o.test(e);function i(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){let{href:e}=window.location,t=i();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function c(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await c(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class g extends Error{}class m extends Error{}class h extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function w(e){return JSON.stringify({message:e.message,stack:e.stack})}},5651:(e,t,n)=>{Promise.resolve().then(n.bind(n,7814))},6046:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>f,tree:()=>u});var r=n(4332),o=n(8819),l=n(7851),i=n.n(l),a=n(7540),s={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);n.d(t,s);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,7858)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\admin\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,2341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\admin\\src\\app\\page.tsx"],c={require:n,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6542:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function l(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return l},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},7004:(e,t)=>{"use strict";function n(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return n}})},7414:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return i},isInterceptionRouteAppPath:function(){return l}});let r=n(4773),o=["(..)(..)","(.)","(..)","(...)"];function l(e){return void 0!==e.split("/").find(e=>o.find(t=>e.startsWith(t)))}function i(e){let t,n,l;for(let r of e.split("/"))if(n=o.find(e=>r.startsWith(e))){[t,l]=e.split(n,2);break}if(!t||!n||!l)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,r.normalizeAppPath)(t),n){case"(.)":l="/"===t?"/"+l:t+"/"+l;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});l=t.split("/").slice(0,-1).concat(l).join("/");break;case"(...)":l="/"+l;break;case"(..)(..)":let i=t.split("/");if(i.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});l=i.slice(0,-2).concat(l).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:l}}},7629:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{compileNonPath:function(){return d},matchHas:function(){return u},parseDestination:function(){return c},prepareDestination:function(){return f}});let r=n(2265),o=n(3732),l=n(264),i=n(7414),a=n(9377);function s(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,n,r){void 0===n&&(n=[]),void 0===r&&(r=[]);let o={},l=n=>{let r,l=n.key;switch(n.type){case"header":l=l.toLowerCase(),r=e.headers[l];break;case"cookie":r="cookies"in e?e.cookies[n.key]:(0,a.getCookieParser)(e.headers)()[n.key];break;case"query":r=t[l];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};r=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!n.value&&r)return o[function(e){let t="";for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);(r>64&&r<91||r>96&&r<123)&&(t+=e[n])}return t}(l)]=r,!0;if(r){let e=RegExp("^"+n.value+"$"),t=Array.isArray(r)?r.slice(-1)[0].match(e):r.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{o[e]=t.groups[e]}):"host"===n.type&&t[0]&&(o.host=t[0])),!0}return!1};return!(!n.every(e=>l(e))||r.some(e=>l(e)))&&o}function d(e,t){if(!e.includes(":"))return e;for(let n of Object.keys(t))e.includes(":"+n)&&(e=e.replace(RegExp(":"+n+"\\*","g"),":"+n+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+n+"\\?","g"),":"+n+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+n+"\\+","g"),":"+n+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+n+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+n));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+e,{validate:!1})(t).slice(1)}function c(e){let t=e.destination;for(let n of Object.keys({...e.params,...e.query}))n&&(t=t.replace(RegExp(":"+(0,o.escapeStringRegexp)(n),"g"),"__ESC_COLON_"+n));let n=(0,l.parseUrl)(t),r=n.pathname;r&&(r=s(r));let i=n.href;i&&(i=s(i));let a=n.hostname;a&&(a=s(a));let u=n.hash;return u&&(u=s(u)),{...n,pathname:r,hostname:a,href:i,hash:u}}function f(e){let t,n,o=Object.assign({},e.query),l=c(e),{hostname:a,query:u}=l,f=l.pathname;l.hash&&(f=""+f+l.hash);let p=[],g=[];for(let e of((0,r.pathToRegexp)(f,g),g))p.push(e.name);if(a){let e=[];for(let t of((0,r.pathToRegexp)(a,e),e))p.push(t.name)}let m=(0,r.compile)(f,{validate:!1});for(let[n,o]of(a&&(t=(0,r.compile)(a,{validate:!1})),Object.entries(u)))Array.isArray(o)?u[n]=o.map(t=>d(s(t),e.params)):"string"==typeof o&&(u[n]=d(s(o),e.params));let h=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!h.some(e=>p.includes(e)))for(let t of h)t in u||(u[t]=e.params[t]);if((0,i.isInterceptionRouteAppPath)(f))for(let t of f.split("/")){let n=i.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(n){"(..)(..)"===n?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=n;break}}try{let[r,o]=(n=m(e.params)).split("#",2);t&&(l.hostname=t(e.params)),l.pathname=r,l.hash=(o?"#":"")+(o||""),delete l.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return l.query={...o,...l.query},{newUrl:n,destQuery:u,parsedDestination:l}}},7814:(e,t,n)=>{"use strict";n.d(t,{UsersDataTable:()=>o});var r=n(3952);(0,r.registerClientReference)(function(){throw Error("Attempted to call columns() from the server but columns is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\admin\\src\\components\\users-data-table.tsx","columns");let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call UsersDataTable() from the server but UsersDataTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\admin\\src\\components\\users-data-table.tsx","UsersDataTable")},7858:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l});var r=n(8828);n(1365);var o=n(7814);function l(){return(0,r.jsxs)("div",{className:"container mx-auto py-10",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Manajemen Pengguna"}),(0,r.jsx)(o.UsersDataTable,{})]})}},8472:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return o}});let r=n(2265);function o(e,t){let n=[],o=(0,r.pathToRegexp)(e,n,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),l=(0,r.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(o.source),o.flags):o,n);return(e,r)=>{if("string"!=typeof e)return!1;let o=l(e);if(!o)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of n)"number"==typeof e.name&&delete o.params[e.name];return{...r,...o.params}}}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9377:(e,t,n)=>{"use strict";function r(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:r}=n(9796);return r(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return r}})},9551:e=>{"use strict";e.exports=require("url")},9606:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getPreviouslyRevalidatedTags:function(){return v},getUtils:function(){return h},interpolateDynamicPath:function(){return g},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return p}});let r=n(9551),o=n(8496),l=n(8472),i=n(9824),a=n(9769),s=n(7629),u=n(3808),d=n(4773),c=n(6704),f=n(5393);function p(e,t,n){let o=(0,r.parse)(e.url,!0);for(let e of(delete o.search,Object.keys(o.query))){let r=e!==c.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(c.NEXT_QUERY_PARAM_PREFIX),l=e!==c.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(c.NEXT_INTERCEPTION_MARKER_PREFIX);(r||l||t.includes(e)||n&&Object.keys(n.groups).includes(e))&&delete o.query[e]}e.url=(0,r.format)(o)}function g(e,t,n){if(!n)return e;for(let r of Object.keys(n.groups)){let o,{optional:l,repeat:i}=n.groups[r],a=`[${i?"...":""}${r}]`;l&&(a=`[${a}]`);let s=t[r];o=Array.isArray(s)?s.map(e=>e&&encodeURIComponent(e)).join("/"):s?encodeURIComponent(s):"",e=e.replaceAll(a,o)}return e}function m(e,t,n,r){let o={};for(let l of Object.keys(t.groups)){let i=e[l];"string"==typeof i?i=(0,d.normalizeRscURL)(i):Array.isArray(i)&&(i=i.map(d.normalizeRscURL));let a=n[l],s=t.groups[l].optional;if((Array.isArray(a)?a.some(e=>Array.isArray(i)?i.some(t=>t.includes(e)):null==i?void 0:i.includes(e)):null==i?void 0:i.includes(a))||void 0===i&&!(s&&r))return{params:{},hasValidParams:!1};s&&(!i||Array.isArray(i)&&1===i.length&&("index"===i[0]||i[0]===`[[...${l}]]`))&&(i=void 0,delete e[l]),i&&"string"==typeof i&&t.groups[l].repeat&&(i=i.split("/")),i&&(o[l]=i)}return{params:o,hasValidParams:!0}}function h({page:e,i18n:t,basePath:n,rewrites:r,pageIsDynamic:d,trailingSlash:c,caseSensitive:h}){let v,b,w;return d&&(v=(0,i.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),w=(b=(0,a.getRouteMatcher)(v))(e)),{handleRewrites:function(i,a){let f={},p=a.pathname,g=r=>{let u=(0,l.getPathMatch)(r.source+(c?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!h});if(!a.pathname)return!1;let g=u(a.pathname);if((r.has||r.missing)&&g){let e=(0,s.matchHas)(i,a.query,r.has,r.missing);e?Object.assign(g,e):g=!1}if(g){let{parsedDestination:l,destQuery:i}=(0,s.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:g,query:a.query});if(l.protocol)return!0;if(Object.assign(f,i,g),Object.assign(a.query,l.query),delete l.query,Object.assign(a,l),!(p=a.pathname))return!1;if(n&&(p=p.replace(RegExp(`^${n}`),"")||"/"),t){let e=(0,o.normalizeLocalePath)(p,t.locales);p=e.pathname,a.query.nextInternalLocale=e.detectedLocale||g.nextInternalLocale}if(p===e)return!0;if(d&&b){let e=b(p);if(e)return a.query={...a.query,...e},!0}}return!1};for(let e of r.beforeFiles||[])g(e);if(p!==e){let t=!1;for(let e of r.afterFiles||[])if(t=g(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(p||"");return t===(0,u.removeTrailingSlash)(e)||(null==b?void 0:b(t))})()){for(let e of r.fallback||[])if(t=g(e))break}}return f},defaultRouteRegex:v,dynamicRouteMatcher:b,defaultRouteMatches:w,getParamsFromRouteMatches:function(e){if(!v)return null;let{groups:t,routeKeys:n}=v,r=(0,a.getRouteMatcher)({re:{exec:e=>{let r=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(r)){let n=(0,f.normalizeNextQueryParam)(e);n&&(r[n]=t,delete r[e])}let o={};for(let e of Object.keys(n)){let l=n[e];if(!l)continue;let i=t[l],a=r[e];if(!i.optional&&!a)return null;o[i.pos]=a}return o}},groups:t})(e);return r||null},normalizeDynamicRouteParams:(e,t)=>v&&w?m(e,v,w,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,v),interpolateDynamicPath:(e,t)=>g(e,t,v)}}function v(e,t){return"string"==typeof e[c.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[c.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[c.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},9699:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});var r=n(1253);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},9769:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let r=n(5138);function o(e){let{re:t,groups:n}=e;return e=>{let o=t.exec(e);if(!o)return!1;let l=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},i={};for(let[e,t]of Object.entries(n)){let n=o[t.pos];void 0!==n&&(t.repeat?i[e]=n.split("/").map(e=>l(e)):i[e]=l(n))}return i}}},9796:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,n){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},l=t.split(r),i=(n||{}).decode||e,a=0;a<l.length;a++){var s=l[a],u=s.indexOf("=");if(!(u<0)){var d=s.substr(0,u).trim(),c=s.substr(++u,s.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==o[d]&&(o[d]=function(e,t){try{return t(e)}catch(t){return e}}(c,i))}}return o},t.serialize=function(e,t,r){var l=r||{},i=l.encode||n;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var a=i(t);if(a&&!o.test(a))throw TypeError("argument val is invalid");var s=e+"="+a;if(null!=l.maxAge){var u=l.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(u)}if(l.domain){if(!o.test(l.domain))throw TypeError("option domain is invalid");s+="; Domain="+l.domain}if(l.path){if(!o.test(l.path))throw TypeError("option path is invalid");s+="; Path="+l.path}if(l.expires){if("function"!=typeof l.expires.toUTCString)throw TypeError("option expires is invalid");s+="; Expires="+l.expires.toUTCString()}if(l.httpOnly&&(s+="; HttpOnly"),l.secure&&(s+="; Secure"),l.sameSite)switch("string"==typeof l.sameSite?l.sameSite.toLowerCase():l.sameSite){case!0:case"strict":s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"none":s+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return s};var e=decodeURIComponent,n=encodeURIComponent,r=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},9824:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return g},getRouteRegex:function(){return c},parseParameter:function(){return s}});let r=n(6704),o=n(7414),l=n(3732),i=n(3808),a=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function s(e){let t=e.match(a);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let n=e.startsWith("...");return n&&(e=e.slice(3)),{key:e,repeat:n,optional:t}}function d(e,t,n){let r={},s=1,d=[];for(let c of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.find(e=>c.startsWith(e)),i=c.match(a);if(e&&i&&i[2]){let{key:t,optional:n,repeat:o}=u(i[2]);r[t]={pos:s++,repeat:o,optional:n},d.push("/"+(0,l.escapeStringRegexp)(e)+"([^/]+?)")}else if(i&&i[2]){let{key:e,repeat:t,optional:o}=u(i[2]);r[e]={pos:s++,repeat:t,optional:o},n&&i[1]&&d.push("/"+(0,l.escapeStringRegexp)(i[1]));let a=t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";n&&i[1]&&(a=a.substring(1)),d.push(a)}else d.push("/"+(0,l.escapeStringRegexp)(c));t&&i&&i[3]&&d.push((0,l.escapeStringRegexp)(i[3]))}return{parameterizedRoute:d.join(""),groups:r}}function c(e,t){let{includeSuffix:n=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:o=!1}=void 0===t?{}:t,{parameterizedRoute:l,groups:i}=d(e,n,r),a=l;return o||(a+="(?:/)?"),{re:RegExp("^"+a+"$"),groups:i}}function f(e){let t,{interceptionMarker:n,getSafeRouteKey:r,segment:o,routeKeys:i,keyPrefix:a,backreferenceDuplicateKeys:s}=e,{key:d,optional:c,repeat:f}=u(o),p=d.replace(/\W/g,"");a&&(p=""+a+p);let g=!1;(0===p.length||p.length>30)&&(g=!0),isNaN(parseInt(p.slice(0,1)))||(g=!0),g&&(p=r());let m=p in i;a?i[p]=""+a+d:i[p]=d;let h=n?(0,l.escapeStringRegexp)(n):"";return t=m&&s?"\\k<"+p+">":f?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",c?"(?:/"+h+t+")?":"/"+h+t}function p(e,t,n,s,u){let d,c=(d=0,()=>{let e="",t=++d;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},g=[];for(let d of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)),i=d.match(a);if(e&&i&&i[2])g.push(f({getSafeRouteKey:c,interceptionMarker:i[1],segment:i[2],routeKeys:p,keyPrefix:t?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(i&&i[2]){s&&i[1]&&g.push("/"+(0,l.escapeStringRegexp)(i[1]));let e=f({getSafeRouteKey:c,segment:i[2],routeKeys:p,keyPrefix:t?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});s&&i[1]&&(e=e.substring(1)),g.push(e)}else g.push("/"+(0,l.escapeStringRegexp)(d));n&&i&&i[3]&&g.push((0,l.escapeStringRegexp)(i[3]))}return{namedParameterizedRoute:g.join(""),routeKeys:p}}function g(e,t){var n,r,o;let l=p(e,t.prefixRouteKeys,null!=(n=t.includeSuffix)&&n,null!=(r=t.includePrefix)&&r,null!=(o=t.backreferenceDuplicateKeys)&&o),i=l.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(i+="(?:/)?"),{...c(e,t),namedRegex:"^"+i+"$",routeKeys:l.routeKeys}}function m(e,t){let{parameterizedRoute:n}=d(e,!1,!1),{catchAll:r=!0}=t;if("/"===n)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:o}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+o+(r?"(?:(/.*)?)":"")+"$"}}}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[191,787],()=>n(6046));module.exports=r})();
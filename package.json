{"name": "monetizr-monorepo", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "bun run --filter '*' dev", "build": "bun run build:admin && bun run build:auth && bun run build:dashboard && bun run build:landing", "build:admin": "cd apps/admin && bun run build", "build:auth": "cd apps/auth && bun run build", "build:dashboard": "cd apps/dashboard && bun run build", "build:landing": "cd apps/landing && bun run build", "start": "bun run --filter '*' start"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5.4.5"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"lightningcss-win32-x64-msvc": "^1.30.1"}}
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[31],{170:(e,t)=>{function r(e){var t;let{config:r,src:n,width:o,quality:i}=e,l=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+o+"&q="+l+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},327:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},444:(e,t)=>{function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},607:(e,t,r)=>{r.d(t,{QP:()=>q});let n=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||l(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),i=n?o(e.slice(1),n):void 0;if(i)return i;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},i=/^\[(.+)\]$/,l=e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{u(r,n,e,t)}),n},u=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:s(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void u(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{u(o,s(t,e),r,n)})})},s=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],i=t.length,l=e=>{let r,l=[],a=0,u=0;for(let s=0;s<e.length;s++){let c=e[s];if(0===a){if(c===o&&(n||e.slice(s,s+i)===t)){l.push(e.slice(u,s)),u=s+i;continue}if("/"===c){r=s;continue}}"["===c?a++:"]"===c&&a--}let s=0===l.length?e:e.substring(u),c=s.startsWith("!"),d=c?s.substring(1):s;return{modifiers:l,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>u?r-u:void 0}};return r?e=>r({className:e,parseClassName:l}):l},m=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},h=e=>({cache:f(e.cacheSize),parseClassName:p(e),...n(e)}),g=/\s+/,v=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,i=[],l=e.trim().split(g),a="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{modifiers:u,hasImportantModifier:s,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,p=n(f?c.substring(0,d):c);if(!p){if(!f||!(p=n(c))){a=t+(a.length>0?" "+a:a);continue}f=!1}let h=m(u).join(":"),g=s?h+"!":h,v=g+p;if(i.includes(v))continue;i.push(v);let b=o(p,f);for(let e=0;e<b.length;++e){let t=b[e];i.push(g+t)}a=t+(a.length>0?" "+a:a)}return a};function b(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=y(e))&&(n&&(n+=" "),n+=t);return n}let y=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=y(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,E=/^\d+\/\d+$/,C=new Set(["px","full","screen"]),R=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,P=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,j=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,O=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,k=e=>_(e)||C.has(e)||E.test(e),M=e=>K(e,"length",H),_=e=>!!e&&!Number.isNaN(Number(e)),A=e=>K(e,"number",_),T=e=>!!e&&Number.isInteger(Number(e)),N=e=>e.endsWith("%")&&_(e.slice(0,-1)),L=e=>x.test(e),D=e=>R.test(e),I=new Set(["length","size","percentage"]),F=e=>K(e,I,$),z=e=>K(e,"position",$),W=new Set(["image","url"]),U=e=>K(e,W,X),G=e=>K(e,"",V),B=()=>!0,K=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},H=e=>S.test(e)&&!P.test(e),$=()=>!1,V=e=>j.test(e),X=e=>O.test(e);Symbol.toStringTag;let q=function(e,...t){let r,n,o,i=function(a){return n=(r=h(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=l,l(a)};function l(e){let t=n(e);if(t)return t;let i=v(e,r);return o(e,i),i}return function(){return i(b.apply(null,arguments))}}(()=>{let e=w("colors"),t=w("spacing"),r=w("blur"),n=w("brightness"),o=w("borderColor"),i=w("borderRadius"),l=w("borderSpacing"),a=w("borderWidth"),u=w("contrast"),s=w("grayscale"),c=w("hueRotate"),d=w("invert"),f=w("gap"),p=w("gradientColorStops"),m=w("gradientColorStopPositions"),h=w("inset"),g=w("margin"),v=w("opacity"),b=w("padding"),y=w("saturate"),x=w("scale"),E=w("sepia"),C=w("skew"),R=w("space"),S=w("translate"),P=()=>["auto","contain","none"],j=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto",L,t],I=()=>[L,t],W=()=>["",k,M],K=()=>["auto",_,L],H=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],$=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],X=()=>["start","end","center","between","around","evenly","stretch"],q=()=>["","0",L],Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[_,L];return{cacheSize:500,separator:":",theme:{colors:[B],spacing:[k,M],blur:["none","",D,L],brightness:Z(),borderColor:[e],borderRadius:["none","","full",D,L],borderSpacing:I(),borderWidth:W(),contrast:Z(),grayscale:q(),hueRotate:Z(),invert:q(),gap:I(),gradientColorStops:[e],gradientColorStopPositions:[N,M],inset:O(),margin:O(),opacity:Z(),padding:I(),saturate:Z(),scale:Z(),sepia:q(),skew:Z(),space:I(),translate:I()},classGroups:{aspect:[{aspect:["auto","square","video",L]}],container:["container"],columns:[{columns:[D]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...H(),L]}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",T,L]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",L]}],grow:[{grow:q()}],shrink:[{shrink:q()}],order:[{order:["first","last","none",T,L]}],"grid-cols":[{"grid-cols":[B]}],"col-start-end":[{col:["auto",{span:["full",T,L]},L]}],"col-start":[{"col-start":K()}],"col-end":[{"col-end":K()}],"grid-rows":[{"grid-rows":[B]}],"row-start-end":[{row:["auto",{span:[T,L]},L]}],"row-start":[{"row-start":K()}],"row-end":[{"row-end":K()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",L]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",L]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...X()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...X(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...X(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[R]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[R]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",L,t]}],"min-w":[{"min-w":[L,t,"min","max","fit"]}],"max-w":[{"max-w":[L,t,"none","full","min","max","fit","prose",{screen:[D]},D]}],h:[{h:[L,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[L,t,"auto","min","max","fit"]}],"font-size":[{text:["base",D,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",A]}],"font-family":[{font:[B]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",L]}],"line-clamp":[{"line-clamp":["none",_,A]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",k,L]}],"list-image":[{"list-image":["none",L]}],"list-style-type":[{list:["none","disc","decimal",L]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",k,M]}],"underline-offset":[{"underline-offset":["auto",k,L]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...H(),z]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",F]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},U]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...$(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:$()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...$()]}],"outline-offset":[{"outline-offset":[k,L]}],"outline-w":[{outline:[k,M]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[k,M]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",D,G]}],"shadow-color":[{shadow:[B]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",D,L]}],grayscale:[{grayscale:[s]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[y]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",L]}],duration:[{duration:Z()}],ease:[{ease:["linear","in","out","in-out",L]}],delay:[{delay:Z()}],animate:[{animate:["none","spin","ping","pulse","bounce",L]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[T,L]}],"translate-x":[{"translate-x":[S]}],"translate-y":[{"translate-y":[S]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",L]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[k,M,A]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},615:(e,t,r)=>{r.d(t,{F:()=>l});var n=r(2987);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:a}=t,u=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let i=o(t)||o(n);return l[e][i]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,u,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...s}[t]):({...a,...s})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},749:(e,t,r)=>{r.d(t,{hO:()=>u,sG:()=>a});var n=r(7620),o=r(7509),i=r(9649),l=r(4568),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},1261:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8889).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},1352:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=r(1510)._(r(7620)).default.createContext({})},2371:(e,t)=>{function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},2987:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}},3970:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return w}});let n=r(1510),o=r(5999),i=r(4568),l=o._(r(7620)),a=n._(r(7509)),u=n._(r(8667)),s=r(7258),c=r(327),d=r(4117);r(1611);let f=r(9208),p=n._(r(170)),m=r(7849),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,r,n,o,i,l){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,o=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function v(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}let b=(0,l.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:o,height:a,width:u,decoding:s,className:c,style:d,fetchPriority:f,placeholder:p,loading:h,unoptimized:b,fill:y,onLoadRef:w,onLoadingCompleteRef:x,setBlurComplete:E,setShowAltText:C,sizesInput:R,onLoad:S,onError:P,...j}=e,O=(0,l.useCallback)(e=>{e&&(P&&(e.src=e.src),e.complete&&g(e,p,w,x,E,b,R))},[r,p,w,x,E,P,b,R]),k=(0,m.useMergedRef)(t,O);return(0,i.jsx)("img",{...j,...v(f),loading:h,width:u,height:a,decoding:s,"data-nimg":y?"fill":"1",className:c,style:d,sizes:o,srcSet:n,src:r,ref:k,onLoad:e=>{g(e.currentTarget,p,w,x,E,b,R)},onError:e=>{C(!0),"empty"!==p&&E(!0),P&&P(e)}})});function y(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...v(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,n),null):(0,i.jsx)(u.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let w=(0,l.forwardRef)((e,t)=>{let r=(0,l.useContext)(f.RouterContext),n=(0,l.useContext)(d.ImageConfigContext),o=(0,l.useMemo)(()=>{var e;let t=h||n||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:o,qualities:i}},[n]),{onLoad:a,onLoadingComplete:u}=e,m=(0,l.useRef)(a);(0,l.useEffect)(()=>{m.current=a},[a]);let g=(0,l.useRef)(u);(0,l.useEffect)(()=>{g.current=u},[u]);let[v,w]=(0,l.useState)(!1),[x,E]=(0,l.useState)(!1),{props:C,meta:R}=(0,s.getImgProps)(e,{defaultLoader:p.default,imgConf:o,blurComplete:v,showAltText:x});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b,{...C,unoptimized:R.unoptimized,placeholder:R.placeholder,fill:R.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:w,setShowAltText:E,sizesInput:e.sizes,ref:t}),R.priority?(0,i.jsx)(y,{isAppRouter:!r,imgAttributes:C}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4117:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return i}});let n=r(1510)._(r(7620)),o=r(327),i=n.default.createContext(o.imageConfigDefault)},4637:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(8490),o=r(1075);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},4762:(e,t,r)=>{r.d(t,{b:()=>a});var n=r(7620),o=r(749),i=r(4568),l=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},4931:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8889).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5908:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return a},urlObjectKeys:function(){return l}});let n=r(5999)._(r(444)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",l=e.pathname||"",a=e.hash||"",u=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==s?(s="//"+(s||""),l&&"/"!==l[0]&&(l="/"+l)):s||(s=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+i+s+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return i(e)}},6355:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},6676:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(7620),o=n.useLayoutEffect,i=n.useEffect;function l(e){let{headManager:t,reduceComponentsToState:r}=e;function l(){if(t&&t.mountedInstances){let o=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(o,e))}}return o(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=l),()=>{t&&(t._pendingUpdate=l)})),i(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},6739:(e,t)=>{function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:o,blurDataURL:i,objectFit:l}=e,a=n?40*n:t,u=o?40*o:r,s=a&&u?"viewBox='0 0 "+a+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+s+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(s?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},7167:(e,t,r)=>{r.d(t,{H_:()=>nZ,UC:()=>nV,YJ:()=>nX,q7:()=>nY,VF:()=>n0,JU:()=>nq,ZL:()=>n$,z6:()=>nJ,hN:()=>nQ,bL:()=>nK,wv:()=>n1,Pb:()=>n2,G5:()=>n7,ZP:()=>n6,l9:()=>nH});var n,o,i,l,a=r(7620),u=r.t(a,2);function s(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var c=r(9640),d=r(4568);function f(e,t=[]){let r=[],n=()=>{let t=r.map(e=>a.createContext(e));return function(r){let n=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let o=a.createContext(n),i=r.length;r=[...r,n];let l=t=>{let{scope:r,children:n,...l}=t,u=r?.[e]?.[i]||o,s=a.useMemo(()=>l,Object.values(l));return(0,d.jsx)(u.Provider,{value:s,children:n})};return l.displayName=t+"Provider",[l,function(r,l){let u=l?.[e]?.[i]||o,s=a.useContext(u);if(s)return s;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}var p=globalThis?.document?a.useLayoutEffect:()=>{},m=u[" useInsertionEffect ".trim().toString()]||p;function h({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,i,l]=function({defaultProp:e,onChange:t}){let[r,n]=a.useState(e),o=a.useRef(r),i=a.useRef(t);return m(()=>{i.current=t},[t]),a.useEffect(()=>{o.current!==r&&(i.current?.(r),o.current=r)},[r,o]),[r,n,i]}({defaultProp:t,onChange:r}),u=void 0!==e,s=u?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[s,a.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else i(t)},[u,e,i,l])]}Symbol("RADIX:SYNC_STATE");var g=r(749);function v(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function b(e,t){var r=v(e,t,"get");return r.get?r.get.call(e):r.value}function y(e,t,r){var n=v(e,t,"set");if(n.set)n.set.call(e,r);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=r}return r}var w=r(9649);function x(e){let t=e+"CollectionProvider",[r,n]=f(t),[o,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,n=a.useRef(null),i=a.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:n,children:r})};l.displayName=t;let u=e+"CollectionSlot",s=(0,w.TL)(u),p=a.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=i(u,r),l=(0,c.s)(t,o.collectionRef);return(0,d.jsx)(s,{ref:l,children:n})});p.displayName=u;let m=e+"CollectionItemSlot",h="data-radix-collection-item",g=(0,w.TL)(m),v=a.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,l=a.useRef(null),u=(0,c.s)(t,l),s=i(m,r);return a.useEffect(()=>(s.itemMap.set(l,{ref:l,...o}),()=>void s.itemMap.delete(l))),(0,d.jsx)(g,{...{[h]:""},ref:u,children:n})});return v.displayName=m,[{Provider:l,Slot:p,ItemSlot:v},function(t){let r=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var E=new WeakMap;function C(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=R(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function R(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap;var S=a.createContext(void 0);function P(e){let t=a.useContext(S);return e||t||"ltr"}function j(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var O="dismissableLayer.update",k=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),M=a.forwardRef((e,t)=>{var r,n;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:f,onInteractOutside:p,onDismiss:m,...h}=e,v=a.useContext(k),[b,y]=a.useState(null),w=null!=(n=null==b?void 0:b.ownerDocument)?n:null==(r=globalThis)?void 0:r.document,[,x]=a.useState({}),E=(0,c.s)(t,e=>y(e)),C=Array.from(v.layers),[R]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),S=C.indexOf(R),P=b?C.indexOf(b):-1,M=v.layersWithOutsidePointerEventsDisabled.size>0,T=P>=S,N=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=j(e),o=a.useRef(!1),i=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){A("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,r=[...v.branches].some(e=>e.contains(t));T&&!r&&(null==u||u(e),null==p||p(e),e.defaultPrevented||null==m||m())},w),L=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=j(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&A("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...v.branches].some(e=>e.contains(t))&&(null==f||f(e),null==p||p(e),e.defaultPrevented||null==m||m())},w);return!function(e,t=globalThis?.document){let r=j(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{P===v.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))},w),a.useEffect(()=>{if(b)return o&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(i=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(b)),v.layers.add(b),_(),()=>{o&&1===v.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=i)}},[b,w,o,v]),a.useEffect(()=>()=>{b&&(v.layers.delete(b),v.layersWithOutsidePointerEventsDisabled.delete(b),_())},[b,v]),a.useEffect(()=>{let e=()=>x({});return document.addEventListener(O,e),()=>document.removeEventListener(O,e)},[]),(0,d.jsx)(g.sG.div,{...h,ref:E,style:{pointerEvents:M?T?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,L.onFocusCapture),onBlurCapture:s(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,N.onPointerDownCapture)})});function _(){let e=new CustomEvent(O);document.dispatchEvent(e)}function A(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,g.hO)(i,l):i.dispatchEvent(l)}M.displayName="DismissableLayer",a.forwardRef((e,t)=>{let r=a.useContext(k),n=a.useRef(null),o=(0,c.s)(t,n);return a.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,d.jsx)(g.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var T=0;function N(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var L="focusScope.autoFocusOnMount",D="focusScope.autoFocusOnUnmount",I={bubbles:!1,cancelable:!0},F=a.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[u,s]=a.useState(null),f=j(o),p=j(i),m=a.useRef(null),h=(0,c.s)(t,e=>s(e)),v=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(n){let e=function(e){if(v.paused||!u)return;let t=e.target;u.contains(t)?m.current=t:U(m.current,{select:!0})},t=function(e){if(v.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||U(m.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&U(u)});return u&&r.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,u,v.paused]),a.useEffect(()=>{if(u){G.add(v);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(L,I);u.addEventListener(L,f),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(U(n,{select:t}),document.activeElement!==r)return}(z(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&U(u))}return()=>{u.removeEventListener(L,f),setTimeout(()=>{let t=new CustomEvent(D,I);u.addEventListener(D,p),u.dispatchEvent(t),t.defaultPrevented||U(null!=e?e:document.body,{select:!0}),u.removeEventListener(D,p),G.remove(v)},0)}}},[u,f,p,v]);let b=a.useCallback(e=>{if(!r&&!n||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,i]=function(e){let t=z(e);return[W(t,e),W(t.reverse(),e)]}(t);n&&i?e.shiftKey||o!==i?e.shiftKey&&o===n&&(e.preventDefault(),r&&U(i,{select:!0})):(e.preventDefault(),r&&U(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,v.paused]);return(0,d.jsx)(g.sG.div,{tabIndex:-1,...l,ref:h,onKeyDown:b})});function z(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function W(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function U(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}F.displayName="FocusScope";var G=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=B(e,t)).unshift(t)},remove(t){var r;null==(r=(e=B(e,t))[0])||r.resume()}}}();function B(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var K=u[" useId ".trim().toString()]||(()=>void 0),H=0;function $(e){let[t,r]=a.useState(K());return p(()=>{e||r(e=>e??String(H++))},[e]),e||(t?`radix-${t}`:"")}let V=["top","right","bottom","left"],X=Math.min,q=Math.max,Y=Math.round,Z=Math.floor,J=e=>({x:e,y:e}),Q={left:"right",right:"left",bottom:"top",top:"bottom"},ee={start:"end",end:"start"};function et(e,t){return"function"==typeof e?e(t):e}function er(e){return e.split("-")[0]}function en(e){return e.split("-")[1]}function eo(e){return"x"===e?"y":"x"}function ei(e){return"y"===e?"height":"width"}function el(e){return["top","bottom"].includes(er(e))?"y":"x"}function ea(e){return e.replace(/start|end/g,e=>ee[e])}function eu(e){return e.replace(/left|right|bottom|top/g,e=>Q[e])}function es(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ec(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function ed(e,t,r){let n,{reference:o,floating:i}=e,l=el(t),a=eo(el(t)),u=ei(a),s=er(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(s){case"top":n={x:d,y:o.y-i.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-i.width,y:f};break;default:n={x:o.x,y:o.y}}switch(en(t)){case"start":n[a]-=p*(r&&c?-1:1);break;case"end":n[a]+=p*(r&&c?-1:1)}return n}let ef=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:l}=r,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=ed(s,n,u),f=n,p={},m=0;for(let r=0;r<a.length;r++){let{name:i,fn:h}=a[r],{x:g,y:v,data:b,reset:y}=await h({x:c,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,d=null!=v?v:d,p={...p,[i]:{...p[i],...b}},y&&m<=50&&(m++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(s=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:d}=ed(s,f,u)),r=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function ep(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=et(t,e),m=es(p),h=a[f?"floating"===d?"reference":"floating":d],g=ec(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(h)))||r?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),v="floating"===d?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),y=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},w=ec(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:b,strategy:u}):v);return{top:(g.top-w.top+m.top)/y.y,bottom:(w.bottom-g.bottom+m.bottom)/y.y,left:(g.left-w.left+m.left)/y.x,right:(w.right-g.right+m.right)/y.x}}function em(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function eh(e){return V.some(t=>e[t]>=0)}async function eg(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=er(r),a=en(r),u="y"===el(r),s=["left","top"].includes(l)?-1:1,c=i&&u?-1:1,d=et(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),u?{x:p*c,y:f*s}:{x:f*s,y:p*c}}function ev(){return"undefined"!=typeof window}function eb(e){return ex(e)?(e.nodeName||"").toLowerCase():"#document"}function ey(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ew(e){var t;return null==(t=(ex(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ex(e){return!!ev()&&(e instanceof Node||e instanceof ey(e).Node)}function eE(e){return!!ev()&&(e instanceof Element||e instanceof ey(e).Element)}function eC(e){return!!ev()&&(e instanceof HTMLElement||e instanceof ey(e).HTMLElement)}function eR(e){return!!ev()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ey(e).ShadowRoot)}function eS(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=eM(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function eP(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function ej(e){let t=eO(),r=eE(e)?eM(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function eO(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function ek(e){return["html","body","#document"].includes(eb(e))}function eM(e){return ey(e).getComputedStyle(e)}function e_(e){return eE(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eA(e){if("html"===eb(e))return e;let t=e.assignedSlot||e.parentNode||eR(e)&&e.host||ew(e);return eR(t)?t.host:t}function eT(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=eA(t);return ek(r)?t.ownerDocument?t.ownerDocument.body:t.body:eC(r)&&eS(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),l=ey(o);if(i){let e=eN(l);return t.concat(l,l.visualViewport||[],eS(o)?o:[],e&&r?eT(e):[])}return t.concat(o,eT(o,[],r))}function eN(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eL(e){let t=eM(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=eC(e),i=o?e.offsetWidth:r,l=o?e.offsetHeight:n,a=Y(r)!==i||Y(n)!==l;return a&&(r=i,n=l),{width:r,height:n,$:a}}function eD(e){return eE(e)?e:e.contextElement}function eI(e){let t=eD(e);if(!eC(t))return J(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=eL(t),l=(i?Y(r.width):r.width)/n,a=(i?Y(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eF=J(0);function ez(e){let t=ey(e);return eO()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eF}function eW(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),l=eD(e),a=J(1);t&&(n?eE(n)&&(a=eI(n)):a=eI(e));let u=(void 0===(o=r)&&(o=!1),n&&(!o||n===ey(l))&&o)?ez(l):J(0),s=(i.left+u.x)/a.x,c=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=ey(l),t=n&&eE(n)?ey(n):n,r=e,o=eN(r);for(;o&&n&&t!==r;){let e=eI(o),t=o.getBoundingClientRect(),n=eM(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;s*=e.x,c*=e.y,d*=e.x,f*=e.y,s+=i,c+=l,o=eN(r=ey(o))}}return ec({width:d,height:f,x:s,y:c})}function eU(e,t){let r=e_(e).scrollLeft;return t?t.left+r:eW(ew(e)).left+r}function eG(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:eU(e,n)),y:n.top+t.scrollTop}}function eB(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=ey(e),n=ew(e),o=r.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eO();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,r);else if("document"===t)n=function(e){let t=ew(e),r=e_(e),n=e.ownerDocument.body,o=q(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=q(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),l=-r.scrollLeft+eU(e),a=-r.scrollTop;return"rtl"===eM(n).direction&&(l+=q(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:l,y:a}}(ew(e));else if(eE(t))n=function(e,t){let r=eW(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=eC(e)?eI(e):J(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:n*i.y}}(t,r);else{let r=ez(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return ec(n)}function eK(e){return"static"===eM(e).position}function eH(e,t){if(!eC(e)||"fixed"===eM(e).position)return null;if(t)return t(e);let r=e.offsetParent;return ew(e)===r&&(r=r.ownerDocument.body),r}function e$(e,t){let r=ey(e);if(eP(e))return r;if(!eC(e)){let t=eA(e);for(;t&&!ek(t);){if(eE(t)&&!eK(t))return t;t=eA(t)}return r}let n=eH(e,t);for(;n&&["table","td","th"].includes(eb(n))&&eK(n);)n=eH(n,t);return n&&ek(n)&&eK(n)&&!ej(n)?r:n||function(e){let t=eA(e);for(;eC(t)&&!ek(t);){if(ej(t))return t;if(eP(t))break;t=eA(t)}return null}(e)||r}let eV=async function(e){let t=this.getOffsetParent||e$,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=eC(t),o=ew(t),i="fixed"===r,l=eW(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=J(0);if(n||!n&&!i)if(("body"!==eb(t)||eS(o))&&(a=e_(t)),n){let e=eW(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eU(o));i&&!n&&o&&(u.x=eU(o));let s=!o||n||i?J(0):eG(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eX={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,l=ew(n),a=!!t&&eP(t.floating);if(n===l||a&&i)return r;let u={scrollLeft:0,scrollTop:0},s=J(1),c=J(0),d=eC(n);if((d||!d&&!i)&&(("body"!==eb(n)||eS(l))&&(u=e_(n)),eC(n))){let e=eW(n);s=eI(n),c.x=e.x+n.clientLeft,c.y=e.y+n.clientTop}let f=!l||d||i?J(0):eG(l,u,!0);return{width:r.width*s.x,height:r.height*s.y,x:r.x*s.x-u.scrollLeft*s.x+c.x+f.x,y:r.y*s.y-u.scrollTop*s.y+c.y+f.y}},getDocumentElement:ew,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?eP(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=eT(e,[],!1).filter(e=>eE(e)&&"body"!==eb(e)),o=null,i="fixed"===eM(e).position,l=i?eA(e):e;for(;eE(l)&&!ek(l);){let t=eM(l),r=ej(l);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||eS(l)&&!r&&function e(t,r){let n=eA(t);return!(n===r||!eE(n)||ek(n))&&("fixed"===eM(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):o=t,l=eA(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=i[0],a=i.reduce((e,r)=>{let n=eB(t,r,o);return e.top=q(n.top,e.top),e.right=X(n.right,e.right),e.bottom=X(n.bottom,e.bottom),e.left=q(n.left,e.left),e},eB(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:e$,getElementRects:eV,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=eL(e);return{width:t,height:r}},getScale:eI,isElement:eE,isRTL:function(e){return"rtl"===eM(e).direction}};function eq(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eY=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:s,padding:c=0}=et(e,t)||{};if(null==s)return{};let d=es(c),f={x:r,y:n},p=eo(el(o)),m=ei(p),h=await l.getDimensions(s),g="y"===p,v=g?"clientHeight":"clientWidth",b=i.reference[m]+i.reference[p]-f[p]-i.floating[m],y=f[p]-i.reference[p],w=await (null==l.getOffsetParent?void 0:l.getOffsetParent(s)),x=w?w[v]:0;x&&await (null==l.isElement?void 0:l.isElement(w))||(x=a.floating[v]||i.floating[m]);let E=x/2-h[m]/2-1,C=X(d[g?"top":"left"],E),R=X(d[g?"bottom":"right"],E),S=x-h[m]-R,P=x/2-h[m]/2+(b/2-y/2),j=q(C,X(P,S)),O=!u.arrow&&null!=en(o)&&P!==j&&i.reference[m]/2-(P<C?C:R)-h[m]/2<0,k=O?P<C?P-C:P-S:0;return{[p]:f[p]+k,data:{[p]:j,centerOffset:P-j-k,...O&&{alignmentOffset:k}},reset:O}}}),eZ=(e,t,r)=>{let n=new Map,o={platform:eX,...r},i={...o.platform,_c:n};return ef(e,t,{...o,platform:i})};var eJ=r(7509),eQ="undefined"!=typeof document?a.useLayoutEffect:function(){};function e0(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!e0(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!e0(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function e1(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e2(e,t){let r=e1(e);return Math.round(t*r)/r}function e6(e){let t=a.useRef(e);return eQ(()=>{t.current=e}),t}let e7=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?eY({element:r.current,padding:n}).fn(t):{}:r?eY({element:r,padding:n}).fn(t):{}}}),e3=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await eg(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),e5=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...u}=et(e,t),s={x:r,y:n},c=await ep(t,u),d=el(er(o)),f=eo(d),p=s[f],m=s[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+c[e],n=p-c[t];p=q(r,X(p,n))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=m+c[e],n=m-c[t];m=q(r,X(m,n))}let h=a.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-r,y:h.y-n,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),e9=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=et(e,t),c={x:r,y:n},d=el(o),f=eo(d),p=c[f],m=c[d],h=et(a,t),g="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+g.mainAxis,r=i.reference[f]+i.reference[e]-g.mainAxis;p<t?p=t:p>r&&(p=r)}if(s){var v,b;let e="y"===f?"width":"height",t=["top","left"].includes(er(o)),r=i.reference[d]-i.floating[e]+(t&&(null==(v=l.offset)?void 0:v[d])||0)+(t?0:g.crossAxis),n=i.reference[d]+i.reference[e]+(t?0:(null==(b=l.offset)?void 0:b[d])||0)-(t?g.crossAxis:0);m<r?m=r:m>n&&(m=n)}return{[f]:p,[d]:m}}}}(e),options:[e,t]}),e4=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:b=!0,...y}=et(e,t);if(null!=(r=u.arrow)&&r.alignmentOffset)return{};let w=er(a),x=el(c),E=er(c)===c,C=await (null==d.isRTL?void 0:d.isRTL(f.floating)),R=h||(E||!b?[eu(c)]:function(e){let t=eu(e);return[ea(e),t,ea(t)]}(c)),S="none"!==v;!h&&S&&R.push(...function(e,t,r,n){let o=en(e),i=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(er(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(ea)))),i}(c,b,v,C));let P=[c,...R],j=await ep(t,y),O=[],k=(null==(n=u.flip)?void 0:n.overflows)||[];if(p&&O.push(j[w]),m){let e=function(e,t,r){void 0===r&&(r=!1);let n=en(e),o=eo(el(e)),i=ei(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=eu(l)),[l,eu(l)]}(a,s,C);O.push(j[e[0]],j[e[1]])}if(k=[...k,{placement:a,overflows:O}],!O.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=P[e];if(t&&("alignment"!==m||x===el(t)||k.every(e=>e.overflows[0]>0&&el(e.placement)===x)))return{data:{index:e,overflows:k},reset:{placement:t}};let r=null==(i=k.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(g){case"bestFit":{let e=null==(l=k.filter(e=>{if(S){let t=el(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=c}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),e8=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i,{placement:l,rects:a,platform:u,elements:s}=t,{apply:c=()=>{},...d}=et(e,t),f=await ep(t,d),p=er(l),m=en(l),h="y"===el(l),{width:g,height:v}=a.floating;"top"===p||"bottom"===p?(o=p,i=m===(await (null==u.isRTL?void 0:u.isRTL(s.floating))?"start":"end")?"left":"right"):(i=p,o="end"===m?"top":"bottom");let b=v-f.top-f.bottom,y=g-f.left-f.right,w=X(v-f[o],b),x=X(g-f[i],y),E=!t.middlewareData.shift,C=w,R=x;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(R=y),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(C=b),E&&!m){let e=q(f.left,0),t=q(f.right,0),r=q(f.top,0),n=q(f.bottom,0);h?R=g-2*(0!==e||0!==t?e+t:q(f.left,f.right)):C=v-2*(0!==r||0!==n?r+n:q(f.top,f.bottom))}await c({...t,availableWidth:R,availableHeight:C});let S=await u.getDimensions(s.floating);return g!==S.width||v!==S.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),te=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=et(e,t);switch(n){case"referenceHidden":{let e=em(await ep(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:eh(e)}}}case"escaped":{let e=em(await ep(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:eh(e)}}}default:return{}}}}}(e),options:[e,t]}),tt=(e,t)=>({...e7(e),options:[e,t]});var tr=a.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,d.jsx)(g.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tr.displayName="Arrow";var tn="Popper",[to,ti]=f(tn),[tl,ta]=to(tn),tu=e=>{let{__scopePopper:t,children:r}=e,[n,o]=a.useState(null);return(0,d.jsx)(tl,{scope:t,anchor:n,onAnchorChange:o,children:r})};tu.displayName=tn;var ts="PopperAnchor",tc=a.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,i=ta(ts,r),l=a.useRef(null),u=(0,c.s)(t,l);return a.useEffect(()=>{i.onAnchorChange((null==n?void 0:n.current)||l.current)}),n?null:(0,d.jsx)(g.sG.div,{...o,ref:u})});tc.displayName=ts;var td="PopperContent",[tf,tp]=to(td),tm=a.forwardRef((e,t)=>{var r,n,o,i,l,u,s,f;let{__scopePopper:m,side:h="bottom",sideOffset:v=0,align:b="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:E=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:S=!1,updatePositionStrategy:P="optimized",onPlaced:O,...k}=e,M=ta(td,m),[_,A]=a.useState(null),T=(0,c.s)(t,e=>A(e)),[N,L]=a.useState(null),D=function(e){let[t,r]=a.useState(void 0);return p(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(N),I=null!=(s=null==D?void 0:D.width)?s:0,F=null!=(f=null==D?void 0:D.height)?f:0,z="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},W=Array.isArray(E)?E:[E],U=W.length>0,G={padding:z,boundary:W.filter(tb),altBoundary:U},{refs:B,floatingStyles:K,placement:H,isPositioned:$,middlewareData:V}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:o,elements:{reference:i,floating:l}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[d,f]=a.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=a.useState(n);e0(p,n)||m(n);let[h,g]=a.useState(null),[v,b]=a.useState(null),y=a.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),w=a.useCallback(e=>{e!==R.current&&(R.current=e,b(e))},[]),x=i||h,E=l||v,C=a.useRef(null),R=a.useRef(null),S=a.useRef(d),P=null!=s,j=e6(s),O=e6(o),k=e6(c),M=a.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:r,middleware:p};O.current&&(e.platform=O.current),eZ(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};_.current&&!e0(S.current,t)&&(S.current=t,eJ.flushSync(()=>{f(t)}))})},[p,t,r,O,k]);eQ(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let _=a.useRef(!1);eQ(()=>(_.current=!0,()=>{_.current=!1}),[]),eQ(()=>{if(x&&(C.current=x),E&&(R.current=E),x&&E){if(j.current)return j.current(x,E,M);M()}},[x,E,M,j,P]);let A=a.useMemo(()=>({reference:C,floating:R,setReference:y,setFloating:w}),[y,w]),T=a.useMemo(()=>({reference:x,floating:E}),[x,E]),N=a.useMemo(()=>{let e={position:r,left:0,top:0};if(!T.floating)return e;let t=e2(T.floating,d.x),n=e2(T.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+n+"px)",...e1(T.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,u,T.floating,d.x,d.y]);return a.useMemo(()=>({...d,update:M,refs:A,elements:T,floatingStyles:N}),[d,M,A,T,N])}({strategy:"fixed",placement:h+("center"!==b?"-"+b:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:s=!1}=n,c=eD(e),d=i||l?[...c?eT(c):[],...eT(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),l&&e.addEventListener("resize",r)});let f=c&&u?function(e,t){let r,n=null,o=ew(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let s=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=s;if(a||t(),!f||!p)return;let m=Z(d),h=Z(o.clientWidth-(c+f)),g={rootMargin:-m+"px "+-h+"px "+-Z(o.clientHeight-(d+p))+"px "+-Z(c)+"px",threshold:q(0,X(1,u))||1},v=!0;function b(t){let n=t[0].intersectionRatio;if(n!==u){if(!v)return l();n?l(!1,n):r=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==n||eq(s,e.getBoundingClientRect())||l(),v=!1}try{n=new IntersectionObserver(b,{...g,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(b,g)}n.observe(e)}(!0),i}(c,r):null,p=-1,m=null;a&&(m=new ResizeObserver(e=>{let[n]=e;n&&n.target===c&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),r()}),c&&!s&&m.observe(c),m.observe(t));let h=s?eW(e):null;return s&&function t(){let n=eW(e);h&&!eq(h,n)&&r(),h=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",r),l&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=m)||e.disconnect(),m=null,s&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===P})},elements:{reference:M.anchor},middleware:[e3({mainAxis:v+F,alignmentAxis:y}),x&&e5({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?e9():void 0,...G}),x&&e4({...G}),e8({...G,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:l}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),N&&tt({element:N,padding:w}),ty({arrowWidth:I,arrowHeight:F}),S&&te({strategy:"referenceHidden",...G})]}),[Y,J]=tw(H),Q=j(O);p(()=>{$&&(null==Q||Q())},[$,Q]);let ee=null==(r=V.arrow)?void 0:r.x,et=null==(n=V.arrow)?void 0:n.y,er=(null==(o=V.arrow)?void 0:o.centerOffset)!==0,[en,eo]=a.useState();return p(()=>{_&&eo(window.getComputedStyle(_).zIndex)},[_]),(0,d.jsx)("div",{ref:B.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:$?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(i=V.transformOrigin)?void 0:i.x,null==(l=V.transformOrigin)?void 0:l.y].join(" "),...(null==(u=V.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(tf,{scope:m,placedSide:Y,onArrowChange:L,arrowX:ee,arrowY:et,shouldHideArrow:er,children:(0,d.jsx)(g.sG.div,{"data-side":Y,"data-align":J,...k,ref:T,style:{...k.style,animation:$?void 0:"none"}})})})});tm.displayName=td;var th="PopperArrow",tg={top:"bottom",right:"left",bottom:"top",left:"right"},tv=a.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=tp(th,r),i=tg[o.placedSide];return(0,d.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(tr,{...n,ref:t,style:{...n.style,display:"block"}})})});function tb(e){return null!==e}tv.displayName=th;var ty=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null==(r=s.arrow)?void 0:r.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,m]=tw(a),h={start:"0%",center:"50%",end:"100%"}[m],g=(null!=(i=null==(n=s.arrow)?void 0:n.x)?i:0)+d/2,v=(null!=(l=null==(o=s.arrow)?void 0:o.y)?l:0)+f/2,b="",y="";return"bottom"===p?(b=c?h:"".concat(g,"px"),y="".concat(-f,"px")):"top"===p?(b=c?h:"".concat(g,"px"),y="".concat(u.floating.height+f,"px")):"right"===p?(b="".concat(-f,"px"),y=c?h:"".concat(v,"px")):"left"===p&&(b="".concat(u.floating.width+f,"px"),y=c?h:"".concat(v,"px")),{data:{x:b,y}}}});function tw(e){let[t,r="center"]=e.split("-");return[t,r]}var tx=a.forwardRef((e,t)=>{var r,n;let{container:o,...i}=e,[l,u]=a.useState(!1);p(()=>u(!0),[]);let s=o||l&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return s?eJ.createPortal((0,d.jsx)(g.sG.div,{...i,ref:t}),s):null});tx.displayName="Portal";var tE=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,o]=a.useState(),i=a.useRef(null),l=a.useRef(e),u=a.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return a.useEffect(()=>{let e=tC(i.current);u.current="mounted"===s?e:"none"},[s]),p(()=>{let t=i.current,r=l.current;if(r!==e){let n=u.current,o=tC(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),l.current=e}},[e,c]),p(()=>{if(n){var e;let t,r=null!=(e=n.ownerDocument.defaultView)?e:window,o=e=>{let o=tC(i.current).includes(e.animationName);if(e.target===n&&o&&(c("ANIMATION_END"),!l.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},a=e=>{e.target===n&&(u.current=tC(i.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",o),n.addEventListener("animationend",o),()=>{r.clearTimeout(t),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",o),n.removeEventListener("animationend",o)}}c("ANIMATION_END")},[n,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:a.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof r?r({present:n.isPresent}):a.Children.only(r),i=(0,c.s)(n.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof r||n.isPresent?a.cloneElement(o,{ref:i}):null};function tC(e){return(null==e?void 0:e.animationName)||"none"}tE.displayName="Presence";var tR="rovingFocusGroup.onEntryFocus",tS={bubbles:!1,cancelable:!0},tP="RovingFocusGroup",[tj,tO,tk]=x(tP),[tM,t_]=f(tP,[tk]),[tA,tT]=tM(tP),tN=a.forwardRef((e,t)=>(0,d.jsx)(tj.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(tj.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(tL,{...e,ref:t})})}));tN.displayName=tP;var tL=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:o=!1,dir:i,currentTabStopId:l,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:f,onEntryFocus:p,preventScrollOnEntryFocus:m=!1,...v}=e,b=a.useRef(null),y=(0,c.s)(t,b),w=P(i),[x,E]=h({prop:l,defaultProp:null!=u?u:null,onChange:f,caller:tP}),[C,R]=a.useState(!1),S=j(p),O=tO(r),k=a.useRef(!1),[M,_]=a.useState(0);return a.useEffect(()=>{let e=b.current;if(e)return e.addEventListener(tR,S),()=>e.removeEventListener(tR,S)},[S]),(0,d.jsx)(tA,{scope:r,orientation:n,dir:w,loop:o,currentTabStopId:x,onItemFocus:a.useCallback(e=>E(e),[E]),onItemShiftTab:a.useCallback(()=>R(!0),[]),onFocusableItemAdd:a.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>_(e=>e-1),[]),children:(0,d.jsx)(g.sG.div,{tabIndex:C||0===M?-1:0,"data-orientation":n,...v,ref:y,style:{outline:"none",...e.style},onMouseDown:s(e.onMouseDown,()=>{k.current=!0}),onFocus:s(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!C){let t=new CustomEvent(tR,tS);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=O().filter(e=>e.focusable);tz([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),m)}}k.current=!1}),onBlur:s(e.onBlur,()=>R(!1))})})}),tD="RovingFocusGroupItem",tI=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:o=!1,tabStopId:i,children:l,...u}=e,c=$(),f=i||c,p=tT(tD,r),m=p.currentTabStopId===f,h=tO(r),{onFocusableItemAdd:v,onFocusableItemRemove:b,currentTabStopId:y}=p;return a.useEffect(()=>{if(n)return v(),()=>b()},[n,v,b]),(0,d.jsx)(tj.ItemSlot,{scope:r,id:f,focusable:n,active:o,children:(0,d.jsx)(g.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...u,ref:t,onMouseDown:s(e.onMouseDown,e=>{n?p.onItemFocus(f):e.preventDefault()}),onFocus:s(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:s(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tF[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>tz(r))}}),children:"function"==typeof l?l({isCurrentTabStop:m,hasTabStop:null!=y}):l})})});tI.displayName=tD;var tF={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tz(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var tW=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tU=new WeakMap,tG=new WeakMap,tB={},tK=0,tH=function(e){return e&&(e.host||tH(e.parentNode))},t$=function(e,t,r,n){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=tH(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tB[r]||(tB[r]=new WeakMap);var i=tB[r],l=[],a=new Set,u=new Set(o),s=function(e){!e||a.has(e)||(a.add(e),s(e.parentNode))};o.forEach(s);var c=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,u=(tU.get(e)||0)+1,s=(i.get(e)||0)+1;tU.set(e,u),i.set(e,s),l.push(e),1===u&&o&&tG.set(e,!0),1===s&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),tK++,function(){l.forEach(function(e){var t=tU.get(e)-1,o=i.get(e)-1;tU.set(e,t),i.set(e,o),t||(tG.has(e)||e.removeAttribute(n),tG.delete(e)),o||e.removeAttribute(r)}),--tK||(tU=new WeakMap,tU=new WeakMap,tG=new WeakMap,tB={})}},tV=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||tW(e);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),t$(n,o,r,"aria-hidden")):function(){return null}},tX=function(){return(tX=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tq(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var tY=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tZ="width-before-scroll-bar";function tJ(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tQ="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,t0=new WeakMap;function t1(e){return e}var t2=function(e){void 0===e&&(e={});var t,r,n,o,i=(t=null,void 0===r&&(r=t1),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var i=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return i.options=tX({async:!0,ssr:!1},e),i}(),t6=function(){},t7=a.forwardRef(function(e,t){var r,n,o,i,l=a.useRef(null),u=a.useState({onScrollCapture:t6,onWheelCapture:t6,onTouchMoveCapture:t6}),s=u[0],c=u[1],d=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,h=e.enabled,g=e.shards,v=e.sideCar,b=e.noRelative,y=e.noIsolation,w=e.inert,x=e.allowPinchZoom,E=e.as,C=e.gapMode,R=tq(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(r=[l,t],n=function(e){return r.forEach(function(t){return tJ(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,i=o.facade,tQ(function(){var e=t0.get(i);if(e){var t=new Set(e),n=new Set(r),o=i.current;t.forEach(function(e){n.has(e)||tJ(e,null)}),n.forEach(function(e){t.has(e)||tJ(e,o)})}t0.set(i,r)},[r]),i),P=tX(tX({},R),s);return a.createElement(a.Fragment,null,h&&a.createElement(v,{sideCar:t2,removeScrollBar:m,shards:g,noRelative:b,noIsolation:y,inert:w,setCallbacks:c,allowPinchZoom:!!x,lockRef:l,gapMode:C}),d?a.cloneElement(a.Children.only(f),tX(tX({},P),{ref:S})):a.createElement(void 0===E?"div":E,tX({},P,{className:p,ref:S}),f))});t7.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t7.classNames={fullWidth:tZ,zeroRight:tY};var t3=function(e){var t=e.sideCar,r=tq(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return a.createElement(n,tX({},r))};t3.isSideCarExport=!0;var t5=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=l||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t9=function(){var e=t5();return function(t,r){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},t4=function(){var e=t9();return function(t){return e(t.styles,t.dynamic),null}},t8={left:0,top:0,right:0,gap:0},re=function(e){return parseInt(e||"",10)||0},rt=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[re(r),re(n),re(o)]},rr=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t8;var t=rt(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},rn=t4(),ro="data-scroll-locked",ri=function(e,t,r,n){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(a,"px ").concat(n,";\n  }\n  body[").concat(ro,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(a,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tY," {\n    right: ").concat(a,"px ").concat(n,";\n  }\n  \n  .").concat(tZ," {\n    margin-right: ").concat(a,"px ").concat(n,";\n  }\n  \n  .").concat(tY," .").concat(tY," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(tZ," .").concat(tZ," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(ro,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},rl=function(){var e=parseInt(document.body.getAttribute(ro)||"0",10);return isFinite(e)?e:0},ra=function(){a.useEffect(function(){return document.body.setAttribute(ro,(rl()+1).toString()),function(){var e=rl()-1;e<=0?document.body.removeAttribute(ro):document.body.setAttribute(ro,e.toString())}},[])},ru=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;ra();var i=a.useMemo(function(){return rr(o)},[o]);return a.createElement(rn,{styles:ri(i,!t,o,r?"":"!important")})},rs=!1;if("undefined"!=typeof window)try{var rc=Object.defineProperty({},"passive",{get:function(){return rs=!0,!0}});window.addEventListener("test",rc,rc),window.removeEventListener("test",rc,rc)}catch(e){rs=!1}var rd=!!rs&&{passive:!1},rf=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},rp=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),rm(e,n)){var o=rh(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},rm=function(e,t){return"v"===e?rf(t,"overflowY"):rf(t,"overflowX")},rh=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},rg=function(e,t,r,n,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*n,u=r.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var m=rh(e,u),h=m[0],g=m[1]-m[2]-l*h;(h||g)&&rm(e,u)&&(f+=g,p+=h);var v=u.parentNode;u=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},rv=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},rb=function(e){return[e.deltaX,e.deltaY]},ry=function(e){return e&&"current"in e?e.current:e},rw=0,rx=[];let rE=(n=function(e){var t=a.useRef([]),r=a.useRef([0,0]),n=a.useRef(),o=a.useState(rw++)[0],i=a.useState(t4)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ry),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=rv(e),a=r.current,u="deltaX"in e?e.deltaX:a[0]-i[0],s="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=rp(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=rp(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(u||s)&&(n.current=o),!o)return!0;var p=n.current||o;return rg(p,t,e,"h"===p?u:s,!0)},[]),s=a.useCallback(function(e){if(rx.length&&rx[rx.length-1]===i){var r="deltaY"in e?rb(e):rv(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(l.current.shards||[]).map(ry).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){r.current=rv(e),n.current=void 0},[]),f=a.useCallback(function(t){c(t.type,rb(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,rv(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return rx.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,rd),document.addEventListener("touchmove",s,rd),document.addEventListener("touchstart",d,rd),function(){rx=rx.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,rd),document.removeEventListener("touchmove",s,rd),document.removeEventListener("touchstart",d,rd)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(ru,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},t2.useMedium(n),t3);var rC=a.forwardRef(function(e,t){return a.createElement(t7,tX({},e,{ref:t,sideCar:rE}))});rC.classNames=t7.classNames;var rR=["Enter"," "],rS=["ArrowUp","PageDown","End"],rP=["ArrowDown","PageUp","Home",...rS],rj={ltr:[...rR,"ArrowRight"],rtl:[...rR,"ArrowLeft"]},rO={ltr:["ArrowLeft"],rtl:["ArrowRight"]},rk="Menu",[rM,r_,rA]=x(rk),[rT,rN]=f(rk,[rA,ti,t_]),rL=ti(),rD=t_(),[rI,rF]=rT(rk),[rz,rW]=rT(rk),rU=e=>{let{__scopeMenu:t,open:r=!1,children:n,dir:o,onOpenChange:i,modal:l=!0}=e,u=rL(t),[s,c]=a.useState(null),f=a.useRef(!1),p=j(i),m=P(o);return a.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,d.jsx)(tu,{...u,children:(0,d.jsx)(rI,{scope:t,open:r,onOpenChange:p,content:s,onContentChange:c,children:(0,d.jsx)(rz,{scope:t,onClose:a.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:n})})})};rU.displayName=rk;var rG=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=rL(r);return(0,d.jsx)(tc,{...o,...n,ref:t})});rG.displayName="MenuAnchor";var rB="MenuPortal",[rK,rH]=rT(rB,{forceMount:void 0}),r$=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,i=rF(rB,t);return(0,d.jsx)(rK,{scope:t,forceMount:r,children:(0,d.jsx)(tE,{present:r||i.open,children:(0,d.jsx)(tx,{asChild:!0,container:o,children:n})})})};r$.displayName=rB;var rV="MenuContent",[rX,rq]=rT(rV),rY=a.forwardRef((e,t)=>{let r=rH(rV,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=rF(rV,e.__scopeMenu),l=rW(rV,e.__scopeMenu);return(0,d.jsx)(rM.Provider,{scope:e.__scopeMenu,children:(0,d.jsx)(tE,{present:n||i.open,children:(0,d.jsx)(rM.Slot,{scope:e.__scopeMenu,children:l.modal?(0,d.jsx)(rZ,{...o,ref:t}):(0,d.jsx)(rJ,{...o,ref:t})})})})}),rZ=a.forwardRef((e,t)=>{let r=rF(rV,e.__scopeMenu),n=a.useRef(null),o=(0,c.s)(t,n);return a.useEffect(()=>{let e=n.current;if(e)return tV(e)},[]),(0,d.jsx)(r0,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:s(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),rJ=a.forwardRef((e,t)=>{let r=rF(rV,e.__scopeMenu);return(0,d.jsx)(r0,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),rQ=(0,w.TL)("MenuContent.ScrollLock"),r0=a.forwardRef((e,t)=>{let{__scopeMenu:r,loop:n=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,disableOutsidePointerEvents:u,onEntryFocus:f,onEscapeKeyDown:p,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:g,onDismiss:v,disableOutsideScroll:b,...y}=e,w=rF(rV,r),x=rW(rV,r),E=rL(r),C=rD(r),R=r_(r),[S,P]=a.useState(null),j=a.useRef(null),O=(0,c.s)(t,j,w.onContentChange),k=a.useRef(0),_=a.useRef(""),A=a.useRef(0),L=a.useRef(null),D=a.useRef("right"),I=a.useRef(0),z=b?rC:a.Fragment,W=e=>{var t,r;let n=_.current+e,o=R().filter(e=>!e.disabled),i=document.activeElement,l=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,l=(n=Math.max(i,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(l=l.filter(e=>e!==r));let a=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==r?a:void 0}(o.map(e=>e.textValue),n,l),u=null==(r=o.find(e=>e.textValue===a))?void 0:r.ref.current;!function e(t){_.current=t,window.clearTimeout(k.current),""!==t&&(k.current=window.setTimeout(()=>e(""),1e3))}(n),u&&setTimeout(()=>u.focus())};a.useEffect(()=>()=>window.clearTimeout(k.current),[]),a.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:N()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:N()),T++,()=>{1===T&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),T--}},[]);let U=a.useCallback(e=>{var t,r;return D.current===(null==(t=L.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,c=a.x,d=a.y;s>n!=d>n&&r<(c-u)*(n-s)/(d-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(r=L.current)?void 0:r.area)},[]);return(0,d.jsx)(rX,{scope:r,searchRef:_,onItemEnter:a.useCallback(e=>{U(e)&&e.preventDefault()},[U]),onItemLeave:a.useCallback(e=>{var t;U(e)||(null==(t=j.current)||t.focus(),P(null))},[U]),onTriggerLeave:a.useCallback(e=>{U(e)&&e.preventDefault()},[U]),pointerGraceTimerRef:A,onPointerGraceIntentChange:a.useCallback(e=>{L.current=e},[]),children:(0,d.jsx)(z,{...b?{as:rQ,allowPinchZoom:!0}:void 0,children:(0,d.jsx)(F,{asChild:!0,trapped:o,onMountAutoFocus:s(i,e=>{var t;e.preventDefault(),null==(t=j.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:(0,d.jsx)(M,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:p,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:g,onDismiss:v,children:(0,d.jsx)(tN,{asChild:!0,...C,dir:x.dir,orientation:"vertical",loop:n,currentTabStopId:S,onCurrentTabStopIdChange:P,onEntryFocus:s(f,e=>{x.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,d.jsx)(tm,{role:"menu","aria-orientation":"vertical","data-state":nb(w.open),"data-radix-menu-content":"",dir:x.dir,...E,...y,ref:O,style:{outline:"none",...y.style},onKeyDown:s(y.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&W(e.key));let o=j.current;if(e.target!==o||!rP.includes(e.key))return;e.preventDefault();let i=R().filter(e=>!e.disabled).map(e=>e.ref.current);rS.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(i)}),onBlur:s(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(k.current),_.current="")}),onPointerMove:s(e.onPointerMove,nx(e=>{let t=e.target,r=I.current!==e.clientX;e.currentTarget.contains(t)&&r&&(D.current=e.clientX>I.current?"right":"left",I.current=e.clientX)}))})})})})})})});rY.displayName=rV;var r1=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,d.jsx)(g.sG.div,{role:"group",...n,ref:t})});r1.displayName="MenuGroup";var r2=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,d.jsx)(g.sG.div,{...n,ref:t})});r2.displayName="MenuLabel";var r6="MenuItem",r7="menu.itemSelect",r3=a.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:n,...o}=e,i=a.useRef(null),l=rW(r6,e.__scopeMenu),u=rq(r6,e.__scopeMenu),f=(0,c.s)(t,i),p=a.useRef(!1);return(0,d.jsx)(r5,{...o,ref:f,disabled:r,onClick:s(e.onClick,()=>{let e=i.current;if(!r&&e){let t=new CustomEvent(r7,{bubbles:!0,cancelable:!0});e.addEventListener(r7,e=>null==n?void 0:n(e),{once:!0}),(0,g.hO)(e,t),t.defaultPrevented?p.current=!1:l.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),p.current=!0},onPointerUp:s(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:s(e.onKeyDown,e=>{let t=""!==u.searchRef.current;r||t&&" "===e.key||rR.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});r3.displayName=r6;var r5=a.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:n=!1,textValue:o,...i}=e,l=rq(r6,r),u=rD(r),f=a.useRef(null),p=(0,c.s)(t,f),[m,h]=a.useState(!1),[v,b]=a.useState("");return a.useEffect(()=>{let e=f.current;if(e){var t;b((null!=(t=e.textContent)?t:"").trim())}},[i.children]),(0,d.jsx)(rM.ItemSlot,{scope:r,disabled:n,textValue:null!=o?o:v,children:(0,d.jsx)(tI,{asChild:!0,...u,focusable:!n,children:(0,d.jsx)(g.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":n||void 0,"data-disabled":n?"":void 0,...i,ref:p,onPointerMove:s(e.onPointerMove,nx(e=>{n?l.onItemLeave(e):(l.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:s(e.onPointerLeave,nx(e=>l.onItemLeave(e))),onFocus:s(e.onFocus,()=>h(!0)),onBlur:s(e.onBlur,()=>h(!1))})})})}),r9=a.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...o}=e;return(0,d.jsx)(ni,{scope:e.__scopeMenu,checked:r,children:(0,d.jsx)(r3,{role:"menuitemcheckbox","aria-checked":ny(r)?"mixed":r,...o,ref:t,"data-state":nw(r),onSelect:s(o.onSelect,()=>null==n?void 0:n(!!ny(r)||!r),{checkForDefaultPrevented:!1})})})});r9.displayName="MenuCheckboxItem";var r4="MenuRadioGroup",[r8,ne]=rT(r4,{value:void 0,onValueChange:()=>{}}),nt=a.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,i=j(n);return(0,d.jsx)(r8,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,d.jsx)(r1,{...o,ref:t})})});nt.displayName=r4;var nr="MenuRadioItem",nn=a.forwardRef((e,t)=>{let{value:r,...n}=e,o=ne(nr,e.__scopeMenu),i=r===o.value;return(0,d.jsx)(ni,{scope:e.__scopeMenu,checked:i,children:(0,d.jsx)(r3,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":nw(i),onSelect:s(n.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,r)},{checkForDefaultPrevented:!1})})})});nn.displayName=nr;var no="MenuItemIndicator",[ni,nl]=rT(no,{checked:!1}),na=a.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,i=nl(no,r);return(0,d.jsx)(tE,{present:n||ny(i.checked)||!0===i.checked,children:(0,d.jsx)(g.sG.span,{...o,ref:t,"data-state":nw(i.checked)})})});na.displayName=no;var nu=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,d.jsx)(g.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});nu.displayName="MenuSeparator";var ns=a.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=rL(r);return(0,d.jsx)(tv,{...o,...n,ref:t})});ns.displayName="MenuArrow";var nc="MenuSub",[nd,nf]=rT(nc),np=e=>{let{__scopeMenu:t,children:r,open:n=!1,onOpenChange:o}=e,i=rF(nc,t),l=rL(t),[u,s]=a.useState(null),[c,f]=a.useState(null),p=j(o);return a.useEffect(()=>(!1===i.open&&p(!1),()=>p(!1)),[i.open,p]),(0,d.jsx)(tu,{...l,children:(0,d.jsx)(rI,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:f,children:(0,d.jsx)(nd,{scope:t,contentId:$(),triggerId:$(),trigger:u,onTriggerChange:s,children:r})})})};np.displayName=nc;var nm="MenuSubTrigger",nh=a.forwardRef((e,t)=>{let r=rF(nm,e.__scopeMenu),n=rW(nm,e.__scopeMenu),o=nf(nm,e.__scopeMenu),i=rq(nm,e.__scopeMenu),l=a.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:f}=i,p={__scopeMenu:e.__scopeMenu},m=a.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return a.useEffect(()=>m,[m]),a.useEffect(()=>{let e=u.current;return()=>{window.clearTimeout(e),f(null)}},[u,f]),(0,d.jsx)(rG,{asChild:!0,...p,children:(0,d.jsx)(r5,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":o.contentId,"data-state":nb(r.open),...e,ref:(0,c.t)(t,o.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:s(e.onPointerMove,nx(t=>{i.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||l.current||(i.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{r.onOpenChange(!0),m()},100)))})),onPointerLeave:s(e.onPointerLeave,nx(e=>{var t,n;m();let o=null==(t=r.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(n=r.content)?void 0:n.dataset.side,l="right"===t,a=o[l?"left":"right"],s=o[l?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(l?-5:5),y:e.clientY},{x:a,y:o.top},{x:s,y:o.top},{x:s,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(u.current),u.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:s(e.onKeyDown,t=>{let o=""!==i.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&rj[n.dir].includes(t.key)){var l;r.onOpenChange(!0),null==(l=r.content)||l.focus(),t.preventDefault()}})})})});nh.displayName=nm;var ng="MenuSubContent",nv=a.forwardRef((e,t)=>{let r=rH(rV,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=rF(rV,e.__scopeMenu),l=rW(rV,e.__scopeMenu),u=nf(ng,e.__scopeMenu),f=a.useRef(null),p=(0,c.s)(t,f);return(0,d.jsx)(rM.Provider,{scope:e.__scopeMenu,children:(0,d.jsx)(tE,{present:n||i.open,children:(0,d.jsx)(rM.Slot,{scope:e.__scopeMenu,children:(0,d.jsx)(r0,{id:u.contentId,"aria-labelledby":u.triggerId,...o,ref:p,align:"start",side:"rtl"===l.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;l.isUsingKeyboardRef.current&&(null==(t=f.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:s(e.onFocusOutside,e=>{e.target!==u.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:s(e.onEscapeKeyDown,e=>{l.onClose(),e.preventDefault()}),onKeyDown:s(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=rO[l.dir].includes(e.key);if(t&&r){var n;i.onOpenChange(!1),null==(n=u.trigger)||n.focus(),e.preventDefault()}})})})})})});function nb(e){return e?"open":"closed"}function ny(e){return"indeterminate"===e}function nw(e){return ny(e)?"indeterminate":e?"checked":"unchecked"}function nx(e){return t=>"mouse"===t.pointerType?e(t):void 0}nv.displayName=ng;var nE="DropdownMenu",[nC,nR]=f(nE,[rN]),nS=rN(),[nP,nj]=nC(nE),nO=e=>{let{__scopeDropdownMenu:t,children:r,dir:n,open:o,defaultOpen:i,onOpenChange:l,modal:u=!0}=e,s=nS(t),c=a.useRef(null),[f,p]=h({prop:o,defaultProp:null!=i&&i,onChange:l,caller:nE});return(0,d.jsx)(nP,{scope:t,triggerId:$(),triggerRef:c,contentId:$(),open:f,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,d.jsx)(rU,{...s,open:f,onOpenChange:p,dir:n,modal:u,children:r})})};nO.displayName=nE;var nk="DropdownMenuTrigger",nM=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...o}=e,i=nj(nk,r),l=nS(r);return(0,d.jsx)(rG,{asChild:!0,...l,children:(0,d.jsx)(g.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...o,ref:(0,c.t)(t,i.triggerRef),onPointerDown:s(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:s(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});nM.displayName=nk;var n_=e=>{let{__scopeDropdownMenu:t,...r}=e,n=nS(t);return(0,d.jsx)(r$,{...n,...r})};n_.displayName="DropdownMenuPortal";var nA="DropdownMenuContent",nT=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nj(nA,r),i=nS(r),l=a.useRef(!1);return(0,d.jsx)(rY,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...n,ref:t,onCloseAutoFocus:s(e.onCloseAutoFocus,e=>{var t;l.current||null==(t=o.triggerRef.current)||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:s(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!o.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nT.displayName=nA;var nN=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(r1,{...o,...n,ref:t})});nN.displayName="DropdownMenuGroup";var nL=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(r2,{...o,...n,ref:t})});nL.displayName="DropdownMenuLabel";var nD=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(r3,{...o,...n,ref:t})});nD.displayName="DropdownMenuItem";var nI=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(r9,{...o,...n,ref:t})});nI.displayName="DropdownMenuCheckboxItem";var nF=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(nt,{...o,...n,ref:t})});nF.displayName="DropdownMenuRadioGroup";var nz=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(nn,{...o,...n,ref:t})});nz.displayName="DropdownMenuRadioItem";var nW=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(na,{...o,...n,ref:t})});nW.displayName="DropdownMenuItemIndicator";var nU=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(nu,{...o,...n,ref:t})});nU.displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(ns,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var nG=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(nh,{...o,...n,ref:t})});nG.displayName="DropdownMenuSubTrigger";var nB=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=nS(r);return(0,d.jsx)(nv,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nB.displayName="DropdownMenuSubContent";var nK=nO,nH=nM,n$=n_,nV=nT,nX=nN,nq=nL,nY=nD,nZ=nI,nJ=nF,nQ=nz,n0=nW,n1=nU,n2=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:i}=e,l=nS(t),[a,u]=h({prop:n,defaultProp:null!=i&&i,onChange:o,caller:"DropdownMenuSub"});return(0,d.jsx)(np,{...l,open:a,onOpenChange:u,children:r})},n6=nG,n7=nB},7258:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return u}}),r(1611);let n=r(6739),o=r(327),i=["-moz-initial","fill","none","scale-down",void 0];function l(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function u(e,t){var r,u;let s,c,d,{src:f,sizes:p,unoptimized:m=!1,priority:h=!1,loading:g,className:v,quality:b,width:y,height:w,fill:x=!1,style:E,overrideSrc:C,onLoad:R,onLoadingComplete:S,placeholder:P="empty",blurDataURL:j,fetchPriority:O,decoding:k="async",layout:M,objectFit:_,objectPosition:A,lazyBoundary:T,lazyRoot:N,...L}=e,{imgConf:D,showAltText:I,blurComplete:F,defaultLoader:z}=t,W=D||o.imageConfigDefault;if("allSizes"in W)s=W;else{let e=[...W.deviceSizes,...W.imageSizes].sort((e,t)=>e-t),t=W.deviceSizes.sort((e,t)=>e-t),n=null==(r=W.qualities)?void 0:r.sort((e,t)=>e-t);s={...W,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===z)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let U=L.loader||z;delete L.loader,delete L.srcSet;let G="__next_img_default"in U;if(G){if("custom"===s.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=U;U=t=>{let{config:r,...n}=t;return e(n)}}if(M){"fill"===M&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(E={...E,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!p&&(p=t)}let B="",K=a(y),H=a(w);if((u=f)&&"object"==typeof u&&(l(u)||void 0!==u.src)){let e=l(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,d=e.blurHeight,j=j||e.blurDataURL,B=e.src,!x)if(K||H){if(K&&!H){let t=K/e.width;H=Math.round(e.height*t)}else if(!K&&H){let t=H/e.height;K=Math.round(e.width*t)}}else K=e.width,H=e.height}let $=!h&&("lazy"===g||void 0===g);(!(f="string"==typeof f?f:B)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,$=!1),s.unoptimized&&(m=!0),G&&!s.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let V=a(b),X=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:_,objectPosition:A}:{},I?{}:{color:"transparent"},E),q=F||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:K,heightInt:H,blurWidth:c,blurHeight:d,blurDataURL:j||"",objectFit:X.objectFit})+'")':'url("'+P+'")',Y=i.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,Z=q?{backgroundSize:Y,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:q}:{},J=function(e){let{config:t,src:r,unoptimized:n,width:o,quality:i,sizes:l,loader:a}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:u,kind:s}=function(e,t,r){let{deviceSizes:n,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,l),c=u.length-1;return{sizes:l||"w"!==s?l:"100vw",srcSet:u.map((e,n)=>a({config:t,src:r,quality:i,width:e})+" "+("w"===s?e:n+1)+s).join(", "),src:a({config:t,src:r,quality:i,width:u[c]})}}({config:s,src:f,unoptimized:m,width:K,quality:V,sizes:p,loader:U});return{props:{...L,loading:$?"lazy":g,fetchPriority:O,width:K,height:H,decoding:k,className:v,style:{...X,...Z},sizes:J.sizes,srcSet:J.srcSet,src:C||J.src},meta:{unoptimized:m,priority:h,placeholder:P,fill:x}}}},7261:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},useLinkStatus:function(){return b}});let n=r(5999),o=r(4568),i=n._(r(7620)),l=r(5908),a=r(9330),u=r(7533),s=r(7849),c=r(8490),d=r(7720);r(1611);let f=r(3781),p=r(4637),m=r(529);function h(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function g(e){let t,r,n,[l,g]=(0,i.useOptimistic)(f.IDLE_LINK_STATUS),b=(0,i.useRef)(null),{href:y,as:w,children:x,prefetch:E=null,passHref:C,replace:R,shallow:S,scroll:P,onClick:j,onMouseEnter:O,onTouchStart:k,legacyBehavior:M=!1,onNavigate:_,ref:A,unstable_dynamicOnHover:T,...N}=e;t=x,M&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let L=i.default.useContext(a.AppRouterContext),D=!1!==E,I=null===E?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:F,as:z}=i.default.useMemo(()=>{let e=h(y);return{href:e,as:w?h(w):e}},[y,w]);M&&(r=i.default.Children.only(t));let W=M?r&&"object"==typeof r&&r.ref:A,U=i.default.useCallback(e=>(null!==L&&(b.current=(0,f.mountLinkInstance)(e,F,L,I,D,g)),()=>{b.current&&((0,f.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,f.unmountPrefetchableInstance)(e)}),[D,F,L,I,g]),G={ref:(0,s.useMergedRef)(U,W),onClick(e){M||"function"!=typeof j||j(e),M&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,o,l,a){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),i.default.startTransition(()=>{if(a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}(0,m.dispatchNavigateAction)(r||t,o?"replace":"push",null==l||l,n.current)})}}(e,F,z,b,R,P,_))},onMouseEnter(e){M||"function"!=typeof O||O(e),M&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),L&&D&&(0,f.onNavigationIntent)(e.currentTarget,!0===T)},onTouchStart:function(e){M||"function"!=typeof k||k(e),M&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),L&&D&&(0,f.onNavigationIntent)(e.currentTarget,!0===T)}};return(0,c.isAbsoluteUrl)(z)?G.href=z:M&&!C&&("a"!==r.type||"href"in r.props)||(G.href=(0,d.addBasePath)(z)),n=M?i.default.cloneElement(r,G):(0,o.jsx)("a",{...N,...G,children:t}),(0,o.jsx)(v.Provider,{value:l,children:n})}r(6355);let v=(0,i.createContext)(f.IDLE_LINK_STATUS),b=()=>(0,i.useContext)(v);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7849:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(7620);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=i(e,n)),t&&(o.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7911:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8889).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8490:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return v},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return l},getURL:function(){return a},isAbsoluteUrl:function(){return i},isResSent:function(){return s},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return y}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function a(){let{href:e}=window.location,t=l();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&s(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},8667:(e,t,r)=>{var n=r(4338);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},defaultHead:function(){return f}});let o=r(1510),i=r(5999),l=r(4568),a=i._(r(7620)),u=o._(r(6676)),s=r(1352),c=r(5227),d=r(2371);function f(e){void 0===e&&(e=!1);let t=[(0,l.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,l.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(1611);let m=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(f(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let i=!0,l=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){l=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?i=!1:t.add(o.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!l)&&r.has(e)?i=!1:(r.add(e),n[t]=r)}}}return i}}()).reverse().map((e,t)=>{let o=e.key||t;if(n.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:o})})}let g=function(e){let{children:t}=e,r=(0,a.useContext)(s.AmpStateContext),n=(0,a.useContext)(c.HeadManagerContext);return(0,l.jsx)(u.default,{reduceComponentsToState:h,headManager:n,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8889:(e,t,r)=>{r.d(t,{A:()=>u});var n=r(7620);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:r,strokeWidth:u?24*Number(a)/Number(o):a,className:i("lucide",s),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:u,...s}=r;return(0,n.createElement)(a,{ref:l,iconNode:t,className:i("lucide-".concat(o(e)),u),...s})});return r.displayName="".concat(e),r}},9208:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(1510)._(r(7620)).default.createContext(null)},9640:(e,t,r)=>{r.d(t,{s:()=>l,t:()=>i});var n=r(7620);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},9649:(e,t,r)=>{r.d(t,{TL:()=>l});var n=r(7620),o=r(9640),i=r(4568);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var l;let e,a,u=(l=r,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),s=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,o.t)(t,u):u),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,a=n.Children.toArray(o),s=a.find(u);if(s){let e=s.props.children,o=a.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var a=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}}]);
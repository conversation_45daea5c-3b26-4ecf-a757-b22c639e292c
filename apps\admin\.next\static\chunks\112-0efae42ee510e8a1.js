"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[112],{607:(e,t,n)=>{n.d(t,{QP:()=>X});let r=e=>{let t=a(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),o(n,t)||i(e)},getConflictingClassGroupIds:(e,t)=>{let o=n[e]||[];return t&&r[e]?[...o,...r[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),l=r?o(e.slice(1),r):void 0;if(l)return l;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},l=/^\[(.+)\]$/,i=e=>{if(l.test(e)){let t=l.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},a=e=>{let{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return c(Object.entries(e.classGroups),n).forEach(([e,n])=>{u(n,r,e,t)}),r},u=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:s(t,e)).classGroupId=n;return}if("function"==typeof e)return d(e)?void u(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,o])=>{u(o,s(t,e),n,r)})})},s=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},d=e=>e.isThemeGetter,c=(e,t)=>t?e.map(([e,n])=>[e,n.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,n])=>[t+e,n])):e)]):e,g=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,o=(o,l)=>{n.set(o,l),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(o(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):o(e,t)}}},f=e=>{let{separator:t,experimentalParseClassName:n}=e,r=1===t.length,o=t[0],l=t.length,i=e=>{let n,i=[],a=0,u=0;for(let s=0;s<e.length;s++){let d=e[s];if(0===a){if(d===o&&(r||e.slice(s,s+l)===t)){i.push(e.slice(u,s)),u=s+l;continue}if("/"===d){n=s;continue}}"["===d?a++:"]"===d&&a--}let s=0===i.length?e:e.substring(u),d=s.startsWith("!"),c=d?s.substring(1):s;return{modifiers:i,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:n&&n>u?n-u:void 0}};return n?e=>n({className:e,parseClassName:i}):i},p=e=>{if(e.length<=1)return e;let t=[],n=[];return e.forEach(e=>{"["===e[0]?(t.push(...n.sort(),e),n=[]):n.push(e)}),t.push(...n.sort()),t},m=e=>({cache:g(e.cacheSize),parseClassName:f(e),...r(e)}),h=/\s+/,v=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,l=[],i=e.trim().split(h),a="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{modifiers:u,hasImportantModifier:s,baseClassName:d,maybePostfixModifierPosition:c}=n(t),g=!!c,f=r(g?d.substring(0,c):d);if(!f){if(!g||!(f=r(d))){a=t+(a.length>0?" "+a:a);continue}g=!1}let m=p(u).join(":"),h=s?m+"!":m,v=h+f;if(l.includes(v))continue;l.push(v);let b=o(f,g);for(let e=0;e<b.length;++e){let t=b[e];l.push(h+t)}a=t+(a.length>0?" "+a:a)}return a};function b(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=w(e))&&(r&&(r+=" "),r+=t);return r}let w=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=w(e[r]))&&(n&&(n+=" "),n+=t);return n},y=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,C=/^\d+\/\d+$/,R=new Set(["px","full","screen"]),S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,E=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,M=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,F=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,P=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,I=e=>L(e)||R.has(e)||C.test(e),A=e=>W(e,"length",U),L=e=>!!e&&!Number.isNaN(Number(e)),_=e=>W(e,"number",L),D=e=>!!e&&Number.isInteger(Number(e)),k=e=>e.endsWith("%")&&L(e.slice(0,-1)),V=e=>x.test(e),O=e=>S.test(e),T=new Set(["length","size","percentage"]),j=e=>W(e,T,K),N=e=>W(e,"position",K),G=new Set(["image","url"]),z=e=>W(e,G,q),H=e=>W(e,"",$),B=()=>!0,W=(e,t,n)=>{let r=x.exec(e);return!!r&&(r[1]?"string"==typeof t?r[1]===t:t.has(r[1]):n(r[2]))},U=e=>E.test(e)&&!M.test(e),K=()=>!1,$=e=>F.test(e),q=e=>P.test(e);Symbol.toStringTag;let X=function(e,...t){let n,r,o,l=function(a){return r=(n=m(t.reduce((e,t)=>t(e),e()))).cache.get,o=n.cache.set,l=i,i(a)};function i(e){let t=r(e);if(t)return t;let l=v(e,n);return o(e,l),l}return function(){return l(b.apply(null,arguments))}}(()=>{let e=y("colors"),t=y("spacing"),n=y("blur"),r=y("brightness"),o=y("borderColor"),l=y("borderRadius"),i=y("borderSpacing"),a=y("borderWidth"),u=y("contrast"),s=y("grayscale"),d=y("hueRotate"),c=y("invert"),g=y("gap"),f=y("gradientColorStops"),p=y("gradientColorStopPositions"),m=y("inset"),h=y("margin"),v=y("opacity"),b=y("padding"),w=y("saturate"),x=y("scale"),C=y("sepia"),R=y("skew"),S=y("space"),E=y("translate"),M=()=>["auto","contain","none"],F=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto",V,t],T=()=>[V,t],G=()=>["",I,A],W=()=>["auto",L,V],U=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],K=()=>["solid","dashed","dotted","double","none"],$=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],q=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",V],Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[L,V];return{cacheSize:500,separator:":",theme:{colors:[B],spacing:[I,A],blur:["none","",O,V],brightness:Z(),borderColor:[e],borderRadius:["none","","full",O,V],borderSpacing:T(),borderWidth:G(),contrast:Z(),grayscale:X(),hueRotate:Z(),invert:X(),gap:T(),gradientColorStops:[e],gradientColorStopPositions:[k,A],inset:P(),margin:P(),opacity:Z(),padding:T(),saturate:Z(),scale:Z(),sepia:X(),skew:Z(),space:T(),translate:T()},classGroups:{aspect:[{aspect:["auto","square","video",V]}],container:["container"],columns:[{columns:[O]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...U(),V]}],overflow:[{overflow:F()}],"overflow-x":[{"overflow-x":F()}],"overflow-y":[{"overflow-y":F()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",D,V]}],basis:[{basis:P()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",V]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",D,V]}],"grid-cols":[{"grid-cols":[B]}],"col-start-end":[{col:["auto",{span:["full",D,V]},V]}],"col-start":[{"col-start":W()}],"col-end":[{"col-end":W()}],"grid-rows":[{"grid-rows":[B]}],"row-start-end":[{row:["auto",{span:[D,V]},V]}],"row-start":[{"row-start":W()}],"row-end":[{"row-end":W()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",V]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",V]}],gap:[{gap:[g]}],"gap-x":[{"gap-x":[g]}],"gap-y":[{"gap-y":[g]}],"justify-content":[{justify:["normal",...q()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...q(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...q(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",V,t]}],"min-w":[{"min-w":[V,t,"min","max","fit"]}],"max-w":[{"max-w":[V,t,"none","full","min","max","fit","prose",{screen:[O]},O]}],h:[{h:[V,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[V,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[V,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[V,t,"auto","min","max","fit"]}],"font-size":[{text:["base",O,A]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",_]}],"font-family":[{font:[B]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",V]}],"line-clamp":[{"line-clamp":["none",L,_]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",I,V]}],"list-image":[{"list-image":["none",V]}],"list-style-type":[{list:["none","disc","decimal",V]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...K(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",I,A]}],"underline-offset":[{"underline-offset":["auto",I,V]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...U(),N]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",j]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},z]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...K(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:K()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...K()]}],"outline-offset":[{"outline-offset":[I,V]}],"outline-w":[{outline:[I,A]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:G()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[I,A]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",O,H]}],"shadow-color":[{shadow:[B]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...$(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":$()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",O,V]}],grayscale:[{grayscale:[s]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[w]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",V]}],duration:[{duration:Z()}],ease:[{ease:["linear","in","out","in-out",V]}],delay:[{delay:Z()}],animate:[{animate:["none","spin","ping","pulse","bounce",V]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[D,V]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[R]}],"skew-y":[{"skew-y":[R]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",V]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",V]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",V]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[I,A,_]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},615:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(2987);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=r.$,i=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return l(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:a}=t,u=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let l=o(t)||o(r);return i[e][l]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return l(e,u,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...s}[t]):({...a,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},749:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(7620),o=n(7509),l=n(9649),i=n(4568),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,l.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:t,{...l,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},1261:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(8889).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},2987:(e,t,n)=>{n.d(t,{$:()=>r});function r(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var l=t.length;for(n=0;n<l;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}(e))&&(r&&(r+=" "),r+=t);return r}},4762:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(7620),o=n(749),l=n(4568),i=r.forwardRef((e,t)=>(0,l.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var a=i},4931:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(8889).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7167:(e,t,n)=>{n.d(t,{H_:()=>rZ,UC:()=>r$,YJ:()=>rq,q7:()=>rY,VF:()=>r0,JU:()=>rX,ZL:()=>rK,z6:()=>rJ,hN:()=>rQ,bL:()=>rW,wv:()=>r1,Pb:()=>r2,G5:()=>r6,ZP:()=>r9,l9:()=>rU});var r,o,l,i,a=n(7620),u=n.t(a,2);function s(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var d=n(9640),c=n(4568);function g(e,t=[]){let n=[],r=()=>{let t=n.map(e=>a.createContext(e));return function(n){let r=n?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=a.createContext(r),l=n.length;n=[...n,r];let i=t=>{let{scope:n,children:r,...i}=t,u=n?.[e]?.[l]||o,s=a.useMemo(()=>i,Object.values(i));return(0,c.jsx)(u.Provider,{value:s,children:r})};return i.displayName=t+"Provider",[i,function(n,i){let u=i?.[e]?.[l]||o,s=a.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var f=globalThis?.document?a.useLayoutEffect:()=>{},p=u[" useInsertionEffect ".trim().toString()]||f;function m({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,i]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),o=a.useRef(n),l=a.useRef(t);return p(()=>{l.current=t},[t]),a.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,a.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&i.current?.(n)}else l(t)},[u,e,l,i])]}Symbol("RADIX:SYNC_STATE");var h=n(749);function v(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function b(e,t){var n=v(e,t,"get");return n.get?n.get.call(e):n.value}function w(e,t,n){var r=v(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var y=n(9649);function x(e){let t=e+"CollectionProvider",[n,r]=g(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:n}=e,r=a.useRef(null),l=a.useRef(new Map).current;return(0,c.jsx)(o,{scope:t,itemMap:l,collectionRef:r,children:n})};i.displayName=t;let u=e+"CollectionSlot",s=(0,y.TL)(u),f=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(u,n),i=(0,d.s)(t,o.collectionRef);return(0,c.jsx)(s,{ref:i,children:r})});f.displayName=u;let p=e+"CollectionItemSlot",m="data-radix-collection-item",h=(0,y.TL)(p),v=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,i=a.useRef(null),u=(0,d.s)(t,i),s=l(p,n);return a.useEffect(()=>(s.itemMap.set(i,{ref:i,...o}),()=>void s.itemMap.delete(i))),(0,c.jsx)(h,{...{[m]:""},ref:u,children:r})});return v.displayName=p,[{Provider:i,Slot:f,ItemSlot:v},function(t){let n=l(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var C=new WeakMap;function R(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=S(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function S(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap;var E=a.createContext(void 0);function M(e){let t=a.useContext(E);return e||t||"ltr"}function F(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var P="dismissableLayer.update",I=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),A=a.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:g,onInteractOutside:f,onDismiss:p,...m}=e,v=a.useContext(I),[b,w]=a.useState(null),y=null!=(r=null==b?void 0:b.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,x]=a.useState({}),C=(0,d.s)(t,e=>w(e)),R=Array.from(v.layers),[S]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),E=R.indexOf(S),M=b?R.indexOf(b):-1,A=v.layersWithOutsidePointerEventsDisabled.size>0,D=M>=E,k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=F(e),o=a.useRef(!1),l=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){_("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...v.branches].some(e=>e.contains(t));D&&!n&&(null==u||u(e),null==f||f(e),e.defaultPrevented||null==p||p())},y),V=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=F(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&_("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...v.branches].some(e=>e.contains(t))&&(null==g||g(e),null==f||f(e),e.defaultPrevented||null==p||p())},y);return!function(e,t=globalThis?.document){let n=F(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M===v.layers.size-1&&(null==i||i(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},y),a.useEffect(()=>{if(b)return o&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(l=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(b)),v.layers.add(b),L(),()=>{o&&1===v.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=l)}},[b,y,o,v]),a.useEffect(()=>()=>{b&&(v.layers.delete(b),v.layersWithOutsidePointerEventsDisabled.delete(b),L())},[b,v]),a.useEffect(()=>{let e=()=>x({});return document.addEventListener(P,e),()=>document.removeEventListener(P,e)},[]),(0,c.jsx)(h.sG.div,{...m,ref:C,style:{pointerEvents:A?D?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,V.onFocusCapture),onBlurCapture:s(e.onBlurCapture,V.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,k.onPointerDownCapture)})});function L(){let e=new CustomEvent(P);document.dispatchEvent(e)}function _(e,t,n,r){let{discrete:o}=r,l=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&l.addEventListener(e,t,{once:!0}),o?(0,h.hO)(l,i):l.dispatchEvent(i)}A.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(I),r=a.useRef(null),o=(0,d.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(h.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var D=0;function k(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var V="focusScope.autoFocusOnMount",O="focusScope.autoFocusOnUnmount",T={bubbles:!1,cancelable:!0},j=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:l,...i}=e,[u,s]=a.useState(null),g=F(o),f=F(l),p=a.useRef(null),m=(0,d.s)(t,e=>s(e)),v=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(v.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:z(p.current,{select:!0})},t=function(e){if(v.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||z(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&z(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,v.paused]),a.useEffect(()=>{if(u){H.add(v);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(V,T);u.addEventListener(V,g),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(z(r,{select:t}),document.activeElement!==n)return}(N(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&z(u))}return()=>{u.removeEventListener(V,g),setTimeout(()=>{let t=new CustomEvent(O,T);u.addEventListener(O,f),u.dispatchEvent(t),t.defaultPrevented||z(null!=e?e:document.body,{select:!0}),u.removeEventListener(O,f),H.remove(v)},0)}}},[u,g,f,v]);let b=a.useCallback(e=>{if(!n&&!r||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,l]=function(e){let t=N(e);return[G(t,e),G(t.reverse(),e)]}(t);r&&l?e.shiftKey||o!==l?e.shiftKey&&o===r&&(e.preventDefault(),n&&z(l,{select:!0})):(e.preventDefault(),n&&z(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,v.paused]);return(0,c.jsx)(h.sG.div,{tabIndex:-1,...i,ref:m,onKeyDown:b})});function N(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function G(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function z(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}j.displayName="FocusScope";var H=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=B(e,t)).unshift(t)},remove(t){var n;null==(n=(e=B(e,t))[0])||n.resume()}}}();function B(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var W=u[" useId ".trim().toString()]||(()=>void 0),U=0;function K(e){let[t,n]=a.useState(W());return f(()=>{e||n(e=>e??String(U++))},[e]),e||(t?`radix-${t}`:"")}let $=["top","right","bottom","left"],q=Math.min,X=Math.max,Y=Math.round,Z=Math.floor,J=e=>({x:e,y:e}),Q={left:"right",right:"left",bottom:"top",top:"bottom"},ee={start:"end",end:"start"};function et(e,t){return"function"==typeof e?e(t):e}function en(e){return e.split("-")[0]}function er(e){return e.split("-")[1]}function eo(e){return"x"===e?"y":"x"}function el(e){return"y"===e?"height":"width"}function ei(e){return["top","bottom"].includes(en(e))?"y":"x"}function ea(e){return e.replace(/start|end/g,e=>ee[e])}function eu(e){return e.replace(/left|right|bottom|top/g,e=>Q[e])}function es(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ed(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ec(e,t,n){let r,{reference:o,floating:l}=e,i=ei(t),a=eo(ei(t)),u=el(a),s=en(t),d="y"===i,c=o.x+o.width/2-l.width/2,g=o.y+o.height/2-l.height/2,f=o[u]/2-l[u]/2;switch(s){case"top":r={x:c,y:o.y-l.height};break;case"bottom":r={x:c,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:g};break;case"left":r={x:o.x-l.width,y:g};break;default:r={x:o.x,y:o.y}}switch(er(t)){case"start":r[a]-=f*(n&&d?-1:1);break;case"end":r[a]+=f*(n&&d?-1:1)}return r}let eg=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),u=await (null==i.isRTL?void 0:i.isRTL(t)),s=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:c}=ec(s,r,u),g=r,f={},p=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:h,y:v,data:b,reset:w}=await m({x:d,y:c,initialPlacement:r,placement:g,strategy:o,middlewareData:f,rects:s,platform:i,elements:{reference:e,floating:t}});d=null!=h?h:d,c=null!=v?v:c,f={...f,[l]:{...f[l],...b}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(g=w.placement),w.rects&&(s=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:d,y:c}=ec(s,g,u)),n=-1)}return{x:d,y:c,placement:g,strategy:o,middlewareData:f}};async function ef(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:d="viewport",elementContext:c="floating",altBoundary:g=!1,padding:f=0}=et(t,e),p=es(f),m=a[g?"floating"===c?"reference":"floating":c],h=ed(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(m)))||n?m:m.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:s,rootBoundary:d,strategy:u})),v="floating"===c?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),w=await (null==l.isElement?void 0:l.isElement(b))&&await (null==l.getScale?void 0:l.getScale(b))||{x:1,y:1},y=ed(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:b,strategy:u}):v);return{top:(h.top-y.top+p.top)/w.y,bottom:(y.bottom-h.bottom+p.bottom)/w.y,left:(h.left-y.left+p.left)/w.x,right:(y.right-h.right+p.right)/w.x}}function ep(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function em(e){return $.some(t=>e[t]>=0)}async function eh(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=en(n),a=er(n),u="y"===ei(n),s=["left","top"].includes(i)?-1:1,d=l&&u?-1:1,c=et(t,e),{mainAxis:g,crossAxis:f,alignmentAxis:p}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof p&&(f="end"===a?-1*p:p),u?{x:f*d,y:g*s}:{x:g*s,y:f*d}}function ev(){return"undefined"!=typeof window}function eb(e){return ex(e)?(e.nodeName||"").toLowerCase():"#document"}function ew(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ey(e){var t;return null==(t=(ex(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ex(e){return!!ev()&&(e instanceof Node||e instanceof ew(e).Node)}function eC(e){return!!ev()&&(e instanceof Element||e instanceof ew(e).Element)}function eR(e){return!!ev()&&(e instanceof HTMLElement||e instanceof ew(e).HTMLElement)}function eS(e){return!!ev()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ew(e).ShadowRoot)}function eE(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eA(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function eM(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eF(e){let t=eP(),n=eC(e)?eA(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function eP(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eI(e){return["html","body","#document"].includes(eb(e))}function eA(e){return ew(e).getComputedStyle(e)}function eL(e){return eC(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function e_(e){if("html"===eb(e))return e;let t=e.assignedSlot||e.parentNode||eS(e)&&e.host||ey(e);return eS(t)?t.host:t}function eD(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=e_(t);return eI(n)?t.ownerDocument?t.ownerDocument.body:t.body:eR(n)&&eE(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=ew(o);if(l){let e=ek(i);return t.concat(i,i.visualViewport||[],eE(o)?o:[],e&&n?eD(e):[])}return t.concat(o,eD(o,[],n))}function ek(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eV(e){let t=eA(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eR(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=Y(n)!==l||Y(r)!==i;return a&&(n=l,r=i),{width:n,height:r,$:a}}function eO(e){return eC(e)?e:e.contextElement}function eT(e){let t=eO(e);if(!eR(t))return J(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=eV(t),i=(l?Y(n.width):n.width)/r,a=(l?Y(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),a&&Number.isFinite(a)||(a=1),{x:i,y:a}}let ej=J(0);function eN(e){let t=ew(e);return eP()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ej}function eG(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=eO(e),a=J(1);t&&(r?eC(r)&&(a=eT(r)):a=eT(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ew(i))&&o)?eN(i):J(0),s=(l.left+u.x)/a.x,d=(l.top+u.y)/a.y,c=l.width/a.x,g=l.height/a.y;if(i){let e=ew(i),t=r&&eC(r)?ew(r):r,n=e,o=ek(n);for(;o&&r&&t!==n;){let e=eT(o),t=o.getBoundingClientRect(),r=eA(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,c*=e.x,g*=e.y,s+=l,d+=i,o=ek(n=ew(o))}}return ed({width:c,height:g,x:s,y:d})}function ez(e,t){let n=eL(e).scrollLeft;return t?t.left+n:eG(ey(e)).left+n}function eH(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ez(e,r)),y:r.top+t.scrollTop}}function eB(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ew(e),r=ey(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,u=0;if(o){l=o.width,i=o.height;let e=eP();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:l,height:i,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=ey(e),n=eL(e),r=e.ownerDocument.body,o=X(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=X(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),i=-n.scrollLeft+ez(e),a=-n.scrollTop;return"rtl"===eA(r).direction&&(i+=X(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:i,y:a}}(ey(e));else if(eC(t))r=function(e,t){let n=eG(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=eR(e)?eT(e):J(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=eN(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ed(r)}function eW(e){return"static"===eA(e).position}function eU(e,t){if(!eR(e)||"fixed"===eA(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ey(e)===n&&(n=n.ownerDocument.body),n}function eK(e,t){let n=ew(e);if(eM(e))return n;if(!eR(e)){let t=e_(e);for(;t&&!eI(t);){if(eC(t)&&!eW(t))return t;t=e_(t)}return n}let r=eU(e,t);for(;r&&["table","td","th"].includes(eb(r))&&eW(r);)r=eU(r,t);return r&&eI(r)&&eW(r)&&!eF(r)?n:r||function(e){let t=e_(e);for(;eR(t)&&!eI(t);){if(eF(t))return t;if(eM(t))break;t=e_(t)}return null}(e)||n}let e$=async function(e){let t=this.getOffsetParent||eK,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eR(t),o=ey(t),l="fixed"===n,i=eG(e,!0,l,t),a={scrollLeft:0,scrollTop:0},u=J(0);if(r||!r&&!l)if(("body"!==eb(t)||eE(o))&&(a=eL(t)),r){let e=eG(t,!0,l,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ez(o));l&&!r&&o&&(u.x=ez(o));let s=!o||r||l?J(0):eH(o,a);return{x:i.left+a.scrollLeft-u.x-s.x,y:i.top+a.scrollTop-u.y-s.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eq={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=ey(r),a=!!t&&eM(t.floating);if(r===i||a&&l)return n;let u={scrollLeft:0,scrollTop:0},s=J(1),d=J(0),c=eR(r);if((c||!c&&!l)&&(("body"!==eb(r)||eE(i))&&(u=eL(r)),eR(r))){let e=eG(r);s=eT(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let g=!i||c||l?J(0):eH(i,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+g.x,y:n.y*s.y-u.scrollTop*s.y+d.y+g.y}},getDocumentElement:ey,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?eM(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eD(e,[],!1).filter(e=>eC(e)&&"body"!==eb(e)),o=null,l="fixed"===eA(e).position,i=l?e_(e):e;for(;eC(i)&&!eI(i);){let t=eA(i),n=eF(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||eE(i)&&!n&&function e(t,n){let r=e_(t);return!(r===n||!eC(r)||eI(r))&&("fixed"===eA(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=e_(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=l[0],a=l.reduce((e,n)=>{let r=eB(t,n,o);return e.top=X(r.top,e.top),e.right=q(r.right,e.right),e.bottom=q(r.bottom,e.bottom),e.left=X(r.left,e.left),e},eB(t,i,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eK,getElementRects:e$,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eV(e);return{width:t,height:n}},getScale:eT,isElement:eC,isRTL:function(e){return"rtl"===eA(e).direction}};function eX(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eY=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:i,elements:a,middlewareData:u}=t,{element:s,padding:d=0}=et(e,t)||{};if(null==s)return{};let c=es(d),g={x:n,y:r},f=eo(ei(o)),p=el(f),m=await i.getDimensions(s),h="y"===f,v=h?"clientHeight":"clientWidth",b=l.reference[p]+l.reference[f]-g[f]-l.floating[p],w=g[f]-l.reference[f],y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(s)),x=y?y[v]:0;x&&await (null==i.isElement?void 0:i.isElement(y))||(x=a.floating[v]||l.floating[p]);let C=x/2-m[p]/2-1,R=q(c[h?"top":"left"],C),S=q(c[h?"bottom":"right"],C),E=x-m[p]-S,M=x/2-m[p]/2+(b/2-w/2),F=X(R,q(M,E)),P=!u.arrow&&null!=er(o)&&M!==F&&l.reference[p]/2-(M<R?R:S)-m[p]/2<0,I=P?M<R?M-R:M-E:0;return{[f]:g[f]+I,data:{[f]:F,centerOffset:M-F-I,...P&&{alignmentOffset:I}},reset:P}}}),eZ=(e,t,n)=>{let r=new Map,o={platform:eq,...n},l={...o.platform,_c:r};return eg(e,t,{...o,platform:l})};var eJ=n(7509),eQ="undefined"!=typeof document?a.useLayoutEffect:function(){};function e0(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e0(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e0(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e1(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e2(e,t){let n=e1(e);return Math.round(t*n)/n}function e9(e){let t=a.useRef(e);return eQ(()=>{t.current=e}),t}let e6=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eY({element:n.current,padding:r}).fn(t):{}:n?eY({element:n,padding:r}).fn(t):{}}}),e5=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,u=await eh(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:l+u.y,data:{...u,placement:i}}}}}(e),options:[e,t]}),e7=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=et(e,t),s={x:n,y:r},d=await ef(t,u),c=ei(en(o)),g=eo(c),f=s[g],p=s[c];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=f+d[e],r=f-d[t];f=X(n,q(f,r))}if(i){let e="y"===c?"top":"left",t="y"===c?"bottom":"right",n=p+d[e],r=p-d[t];p=X(n,q(p,r))}let m=a.fn({...t,[g]:f,[c]:p});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[g]:l,[c]:i}}}}}}(e),options:[e,t]}),e4=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=et(e,t),d={x:n,y:r},c=ei(o),g=eo(c),f=d[g],p=d[c],m=et(a,t),h="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===g?"height":"width",t=l.reference[g]-l.floating[e]+h.mainAxis,n=l.reference[g]+l.reference[e]-h.mainAxis;f<t?f=t:f>n&&(f=n)}if(s){var v,b;let e="y"===g?"width":"height",t=["top","left"].includes(en(o)),n=l.reference[c]-l.floating[e]+(t&&(null==(v=i.offset)?void 0:v[c])||0)+(t?0:h.crossAxis),r=l.reference[c]+l.reference[e]+(t?0:(null==(b=i.offset)?void 0:b[c])||0)-(t?h.crossAxis:0);p<n?p=n:p>r&&(p=r)}return{[g]:f,[c]:p}}}}(e),options:[e,t]}),e8=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:u,rects:s,initialPlacement:d,platform:c,elements:g}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:b=!0,...w}=et(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let y=en(a),x=ei(d),C=en(d)===d,R=await (null==c.isRTL?void 0:c.isRTL(g.floating)),S=m||(C||!b?[eu(d)]:function(e){let t=eu(e);return[ea(e),t,ea(t)]}(d)),E="none"!==v;!m&&E&&S.push(...function(e,t,n,r){let o=er(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(en(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(ea)))),l}(d,b,v,R));let M=[d,...S],F=await ef(t,w),P=[],I=(null==(r=u.flip)?void 0:r.overflows)||[];if(f&&P.push(F[y]),p){let e=function(e,t,n){void 0===n&&(n=!1);let r=er(e),o=eo(ei(e)),l=el(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=eu(i)),[i,eu(i)]}(a,s,R);P.push(F[e[0]],F[e[1]])}if(I=[...I,{placement:a,overflows:P}],!P.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=M[e];if(t&&("alignment"!==p||x===ei(t)||I.every(e=>e.overflows[0]>0&&ei(e.placement)===x)))return{data:{index:e,overflows:I},reset:{placement:t}};let n=null==(l=I.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(h){case"bestFit":{let e=null==(i=I.filter(e=>{if(E){let t=ei(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=d}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),e3=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:i,rects:a,platform:u,elements:s}=t,{apply:d=()=>{},...c}=et(e,t),g=await ef(t,c),f=en(i),p=er(i),m="y"===ei(i),{width:h,height:v}=a.floating;"top"===f||"bottom"===f?(o=f,l=p===(await (null==u.isRTL?void 0:u.isRTL(s.floating))?"start":"end")?"left":"right"):(l=f,o="end"===p?"top":"bottom");let b=v-g.top-g.bottom,w=h-g.left-g.right,y=q(v-g[o],b),x=q(h-g[l],w),C=!t.middlewareData.shift,R=y,S=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(S=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=b),C&&!p){let e=X(g.left,0),t=X(g.right,0),n=X(g.top,0),r=X(g.bottom,0);m?S=h-2*(0!==e||0!==t?e+t:X(g.left,g.right)):R=v-2*(0!==n||0!==r?n+r:X(g.top,g.bottom))}await d({...t,availableWidth:S,availableHeight:R});let E=await u.getDimensions(s.floating);return h!==E.width||v!==E.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),te=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=et(e,t);switch(r){case"referenceHidden":{let e=ep(await ef(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:em(e)}}}case"escaped":{let e=ep(await ef(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:em(e)}}}default:return{}}}}}(e),options:[e,t]}),tt=(e,t)=>({...e6(e),options:[e,t]});var tn=a.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,c.jsx)(h.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,c.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tn.displayName="Arrow";var tr="Popper",[to,tl]=g(tr),[ti,ta]=to(tr),tu=e=>{let{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return(0,c.jsx)(ti,{scope:t,anchor:r,onAnchorChange:o,children:n})};tu.displayName=tr;var ts="PopperAnchor",td=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,l=ta(ts,n),i=a.useRef(null),u=(0,d.s)(t,i);return a.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||i.current)}),r?null:(0,c.jsx)(h.sG.div,{...o,ref:u})});td.displayName=ts;var tc="PopperContent",[tg,tf]=to(tc),tp=a.forwardRef((e,t)=>{var n,r,o,l,i,u,s,g;let{__scopePopper:p,side:m="bottom",sideOffset:v=0,align:b="center",alignOffset:w=0,arrowPadding:y=0,avoidCollisions:x=!0,collisionBoundary:C=[],collisionPadding:R=0,sticky:S="partial",hideWhenDetached:E=!1,updatePositionStrategy:M="optimized",onPlaced:P,...I}=e,A=ta(tc,p),[L,_]=a.useState(null),D=(0,d.s)(t,e=>_(e)),[k,V]=a.useState(null),O=function(e){let[t,n]=a.useState(void 0);return f(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(k),T=null!=(s=null==O?void 0:O.width)?s:0,j=null!=(g=null==O?void 0:O.height)?g:0,N="number"==typeof R?R:{top:0,right:0,bottom:0,left:0,...R},G=Array.isArray(C)?C:[C],z=G.length>0,H={padding:N,boundary:G.filter(tb),altBoundary:z},{refs:B,floatingStyles:W,placement:U,isPositioned:K,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:l,floating:i}={},transform:u=!0,whileElementsMounted:s,open:d}=e,[c,g]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=a.useState(r);e0(f,r)||p(r);let[m,h]=a.useState(null),[v,b]=a.useState(null),w=a.useCallback(e=>{e!==R.current&&(R.current=e,h(e))},[]),y=a.useCallback(e=>{e!==S.current&&(S.current=e,b(e))},[]),x=l||m,C=i||v,R=a.useRef(null),S=a.useRef(null),E=a.useRef(c),M=null!=s,F=e9(s),P=e9(o),I=e9(d),A=a.useCallback(()=>{if(!R.current||!S.current)return;let e={placement:t,strategy:n,middleware:f};P.current&&(e.platform=P.current),eZ(R.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==I.current};L.current&&!e0(E.current,t)&&(E.current=t,eJ.flushSync(()=>{g(t)}))})},[f,t,n,P,I]);eQ(()=>{!1===d&&E.current.isPositioned&&(E.current.isPositioned=!1,g(e=>({...e,isPositioned:!1})))},[d]);let L=a.useRef(!1);eQ(()=>(L.current=!0,()=>{L.current=!1}),[]),eQ(()=>{if(x&&(R.current=x),C&&(S.current=C),x&&C){if(F.current)return F.current(x,C,A);A()}},[x,C,A,F,M]);let _=a.useMemo(()=>({reference:R,floating:S,setReference:w,setFloating:y}),[w,y]),D=a.useMemo(()=>({reference:x,floating:C}),[x,C]),k=a.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=e2(D.floating,c.x),r=e2(D.floating,c.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...e1(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,c.x,c.y]);return a.useMemo(()=>({...c,update:A,refs:_,elements:D,floatingStyles:k}),[c,A,_,D,k])}({strategy:"fixed",placement:m+("center"!==b?"-"+b:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,d=eO(e),c=l||i?[...d?eD(d):[],...eD(t)]:[];c.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});let g=d&&u?function(e,t){let n,r=null,o=ey(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function i(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),l();let s=e.getBoundingClientRect(),{left:d,top:c,width:g,height:f}=s;if(a||t(),!g||!f)return;let p=Z(c),m=Z(o.clientWidth-(d+g)),h={rootMargin:-p+"px "+-m+"px "+-Z(o.clientHeight-(c+f))+"px "+-Z(d)+"px",threshold:X(0,q(1,u))||1},v=!0;function b(t){let r=t[0].intersectionRatio;if(r!==u){if(!v)return i();r?i(!1,r):n=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==r||eX(s,e.getBoundingClientRect())||i(),v=!1}try{r=new IntersectionObserver(b,{...h,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,h)}r.observe(e)}(!0),l}(d,n):null,f=-1,p=null;a&&(p=new ResizeObserver(e=>{let[r]=e;r&&r.target===d&&p&&(p.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var e;null==(e=p)||e.observe(t)})),n()}),d&&!s&&p.observe(d),p.observe(t));let m=s?eG(e):null;return s&&function t(){let r=eG(e);m&&!eX(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;c.forEach(e=>{l&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==g||g(),null==(e=p)||e.disconnect(),p=null,s&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===M})},elements:{reference:A.anchor},middleware:[e5({mainAxis:v+j,alignmentAxis:w}),x&&e7({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?e4():void 0,...H}),x&&e8({...H}),e3({...H,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),k&&tt({element:k,padding:y}),tw({arrowWidth:T,arrowHeight:j}),E&&te({strategy:"referenceHidden",...H})]}),[Y,J]=ty(U),Q=F(P);f(()=>{K&&(null==Q||Q())},[K,Q]);let ee=null==(n=$.arrow)?void 0:n.x,et=null==(r=$.arrow)?void 0:r.y,en=(null==(o=$.arrow)?void 0:o.centerOffset)!==0,[er,eo]=a.useState();return f(()=>{L&&eo(window.getComputedStyle(L).zIndex)},[L]),(0,c.jsx)("div",{ref:B.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:K?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null==(l=$.transformOrigin)?void 0:l.x,null==(i=$.transformOrigin)?void 0:i.y].join(" "),...(null==(u=$.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,c.jsx)(tg,{scope:p,placedSide:Y,onArrowChange:V,arrowX:ee,arrowY:et,shouldHideArrow:en,children:(0,c.jsx)(h.sG.div,{"data-side":Y,"data-align":J,...I,ref:D,style:{...I.style,animation:K?void 0:"none"}})})})});tp.displayName=tc;var tm="PopperArrow",th={top:"bottom",right:"left",bottom:"top",left:"right"},tv=a.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tf(tm,n),l=th[o.placedSide];return(0,c.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,c.jsx)(tn,{...r,ref:t,style:{...r.style,display:"block"}})})});function tb(e){return null!==e}tv.displayName=tm;var tw=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:u,middlewareData:s}=t,d=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,c=d?0:e.arrowWidth,g=d?0:e.arrowHeight,[f,p]=ty(a),m={start:"0%",center:"50%",end:"100%"}[p],h=(null!=(l=null==(r=s.arrow)?void 0:r.x)?l:0)+c/2,v=(null!=(i=null==(o=s.arrow)?void 0:o.y)?i:0)+g/2,b="",w="";return"bottom"===f?(b=d?m:"".concat(h,"px"),w="".concat(-g,"px")):"top"===f?(b=d?m:"".concat(h,"px"),w="".concat(u.floating.height+g,"px")):"right"===f?(b="".concat(-g,"px"),w=d?m:"".concat(v,"px")):"left"===f&&(b="".concat(u.floating.width+g,"px"),w=d?m:"".concat(v,"px")),{data:{x:b,y:w}}}});function ty(e){let[t,n="center"]=e.split("-");return[t,n]}var tx=a.forwardRef((e,t)=>{var n,r;let{container:o,...l}=e,[i,u]=a.useState(!1);f(()=>u(!0),[]);let s=o||i&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return s?eJ.createPortal((0,c.jsx)(h.sG.div,{...l,ref:t}),s):null});tx.displayName="Portal";var tC=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=a.useState(),l=a.useRef(null),i=a.useRef(e),u=a.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return a.useEffect(()=>{let e=tR(l.current);u.current="mounted"===s?e:"none"},[s]),f(()=>{let t=l.current,n=i.current;if(n!==e){let r=u.current,o=tR(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),f(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=tR(l.current).includes(e.animationName);if(e.target===r&&o&&(d("ANIMATION_END"),!i.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},a=e=>{e.target===r&&(u.current=tR(l.current))};return r.addEventListener("animationstart",a),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",a),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}d("ANIMATION_END")},[r,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:a.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):a.Children.only(n),l=(0,d.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?a.cloneElement(o,{ref:l}):null};function tR(e){return(null==e?void 0:e.animationName)||"none"}tC.displayName="Presence";var tS="rovingFocusGroup.onEntryFocus",tE={bubbles:!1,cancelable:!0},tM="RovingFocusGroup",[tF,tP,tI]=x(tM),[tA,tL]=g(tM,[tI]),[t_,tD]=tA(tM),tk=a.forwardRef((e,t)=>(0,c.jsx)(tF.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(tF.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(tV,{...e,ref:t})})}));tk.displayName=tM;var tV=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:l,currentTabStopId:i,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:g,onEntryFocus:f,preventScrollOnEntryFocus:p=!1,...v}=e,b=a.useRef(null),w=(0,d.s)(t,b),y=M(l),[x,C]=m({prop:i,defaultProp:null!=u?u:null,onChange:g,caller:tM}),[R,S]=a.useState(!1),E=F(f),P=tP(n),I=a.useRef(!1),[A,L]=a.useState(0);return a.useEffect(()=>{let e=b.current;if(e)return e.addEventListener(tS,E),()=>e.removeEventListener(tS,E)},[E]),(0,c.jsx)(t_,{scope:n,orientation:r,dir:y,loop:o,currentTabStopId:x,onItemFocus:a.useCallback(e=>C(e),[C]),onItemShiftTab:a.useCallback(()=>S(!0),[]),onFocusableItemAdd:a.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>L(e=>e-1),[]),children:(0,c.jsx)(h.sG.div,{tabIndex:R||0===A?-1:0,"data-orientation":r,...v,ref:w,style:{outline:"none",...e.style},onMouseDown:s(e.onMouseDown,()=>{I.current=!0}),onFocus:s(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!R){let t=new CustomEvent(tS,tE);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);tN([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),p)}}I.current=!1}),onBlur:s(e.onBlur,()=>S(!1))})})}),tO="RovingFocusGroupItem",tT=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:l,children:i,...u}=e,d=K(),g=l||d,f=tD(tO,n),p=f.currentTabStopId===g,m=tP(n),{onFocusableItemAdd:v,onFocusableItemRemove:b,currentTabStopId:w}=f;return a.useEffect(()=>{if(r)return v(),()=>b()},[r,v,b]),(0,c.jsx)(tF.ItemSlot,{scope:n,id:g,focusable:r,active:o,children:(0,c.jsx)(h.sG.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...u,ref:t,onMouseDown:s(e.onMouseDown,e=>{r?f.onItemFocus(g):e.preventDefault()}),onFocus:s(e.onFocus,()=>f.onItemFocus(g)),onKeyDown:s(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tj[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>tN(n))}}),children:"function"==typeof i?i({isCurrentTabStop:p,hasTabStop:null!=w}):i})})});tT.displayName=tO;var tj={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tN(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var tG=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tz=new WeakMap,tH=new WeakMap,tB={},tW=0,tU=function(e){return e&&(e.host||tU(e.parentNode))},tK=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tU(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tB[n]||(tB[n]=new WeakMap);var l=tB[n],i=[],a=new Set,u=new Set(o),s=function(e){!e||a.has(e)||(a.add(e),s(e.parentNode))};o.forEach(s);var d=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))d(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tz.get(e)||0)+1,s=(l.get(e)||0)+1;tz.set(e,u),l.set(e,s),i.push(e),1===u&&o&&tH.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return d(t),a.clear(),tW++,function(){i.forEach(function(e){var t=tz.get(e)-1,o=l.get(e)-1;tz.set(e,t),l.set(e,o),t||(tH.has(e)||e.removeAttribute(r),tH.delete(e)),o||e.removeAttribute(n)}),--tW||(tz=new WeakMap,tz=new WeakMap,tH=new WeakMap,tB={})}},t$=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||tG(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tK(r,o,n,"aria-hidden")):function(){return null}},tq=function(){return(tq=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tX(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tY=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tZ="width-before-scroll-bar";function tJ(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tQ="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,t0=new WeakMap;function t1(e){return e}var t2=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=t1),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var l=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(l)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return l.options=tq({async:!0,ssr:!1},e),l}(),t9=function(){},t6=a.forwardRef(function(e,t){var n,r,o,l,i=a.useRef(null),u=a.useState({onScrollCapture:t9,onWheelCapture:t9,onTouchMoveCapture:t9}),s=u[0],d=u[1],c=e.forwardProps,g=e.children,f=e.className,p=e.removeScrollBar,m=e.enabled,h=e.shards,v=e.sideCar,b=e.noRelative,w=e.noIsolation,y=e.inert,x=e.allowPinchZoom,C=e.as,R=e.gapMode,S=tX(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=(n=[i,t],r=function(e){return n.forEach(function(t){return tJ(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,tQ(function(){var e=t0.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||tJ(e,null)}),r.forEach(function(e){t.has(e)||tJ(e,o)})}t0.set(l,n)},[n]),l),M=tq(tq({},S),s);return a.createElement(a.Fragment,null,m&&a.createElement(v,{sideCar:t2,removeScrollBar:p,shards:h,noRelative:b,noIsolation:w,inert:y,setCallbacks:d,allowPinchZoom:!!x,lockRef:i,gapMode:R}),c?a.cloneElement(a.Children.only(g),tq(tq({},M),{ref:E})):a.createElement(void 0===C?"div":C,tq({},M,{className:f,ref:E}),g))});t6.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t6.classNames={fullWidth:tZ,zeroRight:tY};var t5=function(e){var t=e.sideCar,n=tX(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,tq({},n))};t5.isSideCarExport=!0;var t7=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t4=function(){var e=t7();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},t8=function(){var e=t4();return function(t){return e(t.styles,t.dynamic),null}},t3={left:0,top:0,right:0,gap:0},ne=function(e){return parseInt(e||"",10)||0},nt=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[ne(n),ne(r),ne(o)]},nn=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t3;var t=nt(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},nr=t8(),no="data-scroll-locked",nl=function(e,t,n,r){var o=e.left,l=e.top,i=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(no,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tY," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tZ," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tY," .").concat(tY," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tZ," .").concat(tZ," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(no,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},ni=function(){var e=parseInt(document.body.getAttribute(no)||"0",10);return isFinite(e)?e:0},na=function(){a.useEffect(function(){return document.body.setAttribute(no,(ni()+1).toString()),function(){var e=ni()-1;e<=0?document.body.removeAttribute(no):document.body.setAttribute(no,e.toString())}},[])},nu=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;na();var l=a.useMemo(function(){return nn(o)},[o]);return a.createElement(nr,{styles:nl(l,!t,o,n?"":"!important")})},ns=!1;if("undefined"!=typeof window)try{var nd=Object.defineProperty({},"passive",{get:function(){return ns=!0,!0}});window.addEventListener("test",nd,nd),window.removeEventListener("test",nd,nd)}catch(e){ns=!1}var nc=!!ns&&{passive:!1},ng=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},nf=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),np(e,r)){var o=nm(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},np=function(e,t){return"v"===e?ng(t,"overflowY"):ng(t,"overflowX")},nm=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nh=function(e,t,n,r,o){var l,i=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),a=i*r,u=n.target,s=t.contains(u),d=!1,c=a>0,g=0,f=0;do{if(!u)break;var p=nm(e,u),m=p[0],h=p[1]-p[2]-i*m;(m||h)&&np(e,u)&&(g+=h,f+=m);var v=u.parentNode;u=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return c&&(o&&1>Math.abs(g)||!o&&a>g)?d=!0:!c&&(o&&1>Math.abs(f)||!o&&-a>f)&&(d=!0),d},nv=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},nb=function(e){return[e.deltaX,e.deltaY]},nw=function(e){return e&&"current"in e?e.current:e},ny=0,nx=[];let nC=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(ny++)[0],l=a.useState(t8)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,l=t.length;o<l;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nw),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,l=nv(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-l[0],s="deltaY"in e?e.deltaY:a[1]-l[1],d=e.target,c=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===c&&"range"===d.type)return!1;var g=nf(c,d);if(!g)return!0;if(g?o=c:(o="v"===c?"h":"v",g=nf(c,d)),!g)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var f=r.current||o;return nh(f,t,e,"h"===f?u:s,!0)},[]),s=a.useCallback(function(e){if(nx.length&&nx[nx.length-1]===l){var n="deltaY"in e?nb(e):nv(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(nw).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=a.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),c=a.useCallback(function(e){n.current=nv(e),r.current=void 0},[]),g=a.useCallback(function(t){d(t.type,nb(t),t.target,u(t,e.lockRef.current))},[]),f=a.useCallback(function(t){d(t.type,nv(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return nx.push(l),e.setCallbacks({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:f}),document.addEventListener("wheel",s,nc),document.addEventListener("touchmove",s,nc),document.addEventListener("touchstart",c,nc),function(){nx=nx.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,nc),document.removeEventListener("touchmove",s,nc),document.removeEventListener("touchstart",c,nc)}},[]);var p=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?a.createElement(nu,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},t2.useMedium(r),t5);var nR=a.forwardRef(function(e,t){return a.createElement(t6,tq({},e,{ref:t,sideCar:nC}))});nR.classNames=t6.classNames;var nS=["Enter"," "],nE=["ArrowUp","PageDown","End"],nM=["ArrowDown","PageUp","Home",...nE],nF={ltr:[...nS,"ArrowRight"],rtl:[...nS,"ArrowLeft"]},nP={ltr:["ArrowLeft"],rtl:["ArrowRight"]},nI="Menu",[nA,nL,n_]=x(nI),[nD,nk]=g(nI,[n_,tl,tL]),nV=tl(),nO=tL(),[nT,nj]=nD(nI),[nN,nG]=nD(nI),nz=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:l,modal:i=!0}=e,u=nV(t),[s,d]=a.useState(null),g=a.useRef(!1),f=F(l),p=M(o);return a.useEffect(()=>{let e=()=>{g.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>g.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,c.jsx)(tu,{...u,children:(0,c.jsx)(nT,{scope:t,open:n,onOpenChange:f,content:s,onContentChange:d,children:(0,c.jsx)(nN,{scope:t,onClose:a.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:g,dir:p,modal:i,children:r})})})};nz.displayName=nI;var nH=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nV(n);return(0,c.jsx)(td,{...o,...r,ref:t})});nH.displayName="MenuAnchor";var nB="MenuPortal",[nW,nU]=nD(nB,{forceMount:void 0}),nK=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,l=nj(nB,t);return(0,c.jsx)(nW,{scope:t,forceMount:n,children:(0,c.jsx)(tC,{present:n||l.open,children:(0,c.jsx)(tx,{asChild:!0,container:o,children:r})})})};nK.displayName=nB;var n$="MenuContent",[nq,nX]=nD(n$),nY=a.forwardRef((e,t)=>{let n=nU(n$,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,l=nj(n$,e.__scopeMenu),i=nG(n$,e.__scopeMenu);return(0,c.jsx)(nA.Provider,{scope:e.__scopeMenu,children:(0,c.jsx)(tC,{present:r||l.open,children:(0,c.jsx)(nA.Slot,{scope:e.__scopeMenu,children:i.modal?(0,c.jsx)(nZ,{...o,ref:t}):(0,c.jsx)(nJ,{...o,ref:t})})})})}),nZ=a.forwardRef((e,t)=>{let n=nj(n$,e.__scopeMenu),r=a.useRef(null),o=(0,d.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return t$(e)},[]),(0,c.jsx)(n0,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:s(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),nJ=a.forwardRef((e,t)=>{let n=nj(n$,e.__scopeMenu);return(0,c.jsx)(n0,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),nQ=(0,y.TL)("MenuContent.ScrollLock"),n0=a.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,disableOutsidePointerEvents:u,onEntryFocus:g,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:m,onInteractOutside:h,onDismiss:v,disableOutsideScroll:b,...w}=e,y=nj(n$,n),x=nG(n$,n),C=nV(n),R=nO(n),S=nL(n),[E,M]=a.useState(null),F=a.useRef(null),P=(0,d.s)(t,F,y.onContentChange),I=a.useRef(0),L=a.useRef(""),_=a.useRef(0),V=a.useRef(null),O=a.useRef("right"),T=a.useRef(0),N=b?nR:a.Fragment,G=e=>{var t,n;let r=L.current+e,o=S().filter(e=>!e.disabled),l=document.activeElement,i=null==(t=o.find(e=>e.ref.current===l))?void 0:t.textValue,a=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,i=(r=Math.max(l,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(o.map(e=>e.textValue),r,i),u=null==(n=o.find(e=>e.textValue===a))?void 0:n.ref.current;!function e(t){L.current=t,window.clearTimeout(I.current),""!==t&&(I.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};a.useEffect(()=>()=>window.clearTimeout(I.current),[]),a.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:k()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:k()),D++,()=>{1===D&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),D--}},[]);let z=a.useCallback(e=>{var t,n;return O.current===(null==(t=V.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],u=i.x,s=i.y,d=a.x,c=a.y;s>r!=c>r&&n<(d-u)*(r-s)/(c-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=V.current)?void 0:n.area)},[]);return(0,c.jsx)(nq,{scope:n,searchRef:L,onItemEnter:a.useCallback(e=>{z(e)&&e.preventDefault()},[z]),onItemLeave:a.useCallback(e=>{var t;z(e)||(null==(t=F.current)||t.focus(),M(null))},[z]),onTriggerLeave:a.useCallback(e=>{z(e)&&e.preventDefault()},[z]),pointerGraceTimerRef:_,onPointerGraceIntentChange:a.useCallback(e=>{V.current=e},[]),children:(0,c.jsx)(N,{...b?{as:nQ,allowPinchZoom:!0}:void 0,children:(0,c.jsx)(j,{asChild:!0,trapped:o,onMountAutoFocus:s(l,e=>{var t;e.preventDefault(),null==(t=F.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:(0,c.jsx)(A,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:m,onInteractOutside:h,onDismiss:v,children:(0,c.jsx)(tk,{asChild:!0,...R,dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:E,onCurrentTabStopIdChange:M,onEntryFocus:s(g,e=>{x.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,c.jsx)(tp,{role:"menu","aria-orientation":"vertical","data-state":rb(y.open),"data-radix-menu-content":"",dir:x.dir,...C,...w,ref:P,style:{outline:"none",...w.style},onKeyDown:s(w.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&G(e.key));let o=F.current;if(e.target!==o||!nM.includes(e.key))return;e.preventDefault();let l=S().filter(e=>!e.disabled).map(e=>e.ref.current);nE.includes(e.key)&&l.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(l)}),onBlur:s(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(I.current),L.current="")}),onPointerMove:s(e.onPointerMove,rx(e=>{let t=e.target,n=T.current!==e.clientX;e.currentTarget.contains(t)&&n&&(O.current=e.clientX>T.current?"right":"left",T.current=e.clientX)}))})})})})})})});nY.displayName=n$;var n1=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,c.jsx)(h.sG.div,{role:"group",...r,ref:t})});n1.displayName="MenuGroup";var n2=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,c.jsx)(h.sG.div,{...r,ref:t})});n2.displayName="MenuLabel";var n9="MenuItem",n6="menu.itemSelect",n5=a.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,l=a.useRef(null),i=nG(n9,e.__scopeMenu),u=nX(n9,e.__scopeMenu),g=(0,d.s)(t,l),f=a.useRef(!1);return(0,c.jsx)(n7,{...o,ref:g,disabled:n,onClick:s(e.onClick,()=>{let e=l.current;if(!n&&e){let t=new CustomEvent(n6,{bubbles:!0,cancelable:!0});e.addEventListener(n6,e=>null==r?void 0:r(e),{once:!0}),(0,h.hO)(e,t),t.defaultPrevented?f.current=!1:i.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),f.current=!0},onPointerUp:s(e.onPointerUp,e=>{var t;f.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:s(e.onKeyDown,e=>{let t=""!==u.searchRef.current;n||t&&" "===e.key||nS.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});n5.displayName=n9;var n7=a.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...l}=e,i=nX(n9,n),u=nO(n),g=a.useRef(null),f=(0,d.s)(t,g),[p,m]=a.useState(!1),[v,b]=a.useState("");return a.useEffect(()=>{let e=g.current;if(e){var t;b((null!=(t=e.textContent)?t:"").trim())}},[l.children]),(0,c.jsx)(nA.ItemSlot,{scope:n,disabled:r,textValue:null!=o?o:v,children:(0,c.jsx)(tT,{asChild:!0,...u,focusable:!r,children:(0,c.jsx)(h.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...l,ref:f,onPointerMove:s(e.onPointerMove,rx(e=>{r?i.onItemLeave(e):(i.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:s(e.onPointerLeave,rx(e=>i.onItemLeave(e))),onFocus:s(e.onFocus,()=>m(!0)),onBlur:s(e.onBlur,()=>m(!1))})})})}),n4=a.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,c.jsx)(rl,{scope:e.__scopeMenu,checked:n,children:(0,c.jsx)(n5,{role:"menuitemcheckbox","aria-checked":rw(n)?"mixed":n,...o,ref:t,"data-state":ry(n),onSelect:s(o.onSelect,()=>null==r?void 0:r(!!rw(n)||!n),{checkForDefaultPrevented:!1})})})});n4.displayName="MenuCheckboxItem";var n8="MenuRadioGroup",[n3,re]=nD(n8,{value:void 0,onValueChange:()=>{}}),rt=a.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,l=F(r);return(0,c.jsx)(n3,{scope:e.__scopeMenu,value:n,onValueChange:l,children:(0,c.jsx)(n1,{...o,ref:t})})});rt.displayName=n8;var rn="MenuRadioItem",rr=a.forwardRef((e,t)=>{let{value:n,...r}=e,o=re(rn,e.__scopeMenu),l=n===o.value;return(0,c.jsx)(rl,{scope:e.__scopeMenu,checked:l,children:(0,c.jsx)(n5,{role:"menuitemradio","aria-checked":l,...r,ref:t,"data-state":ry(l),onSelect:s(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});rr.displayName=rn;var ro="MenuItemIndicator",[rl,ri]=nD(ro,{checked:!1}),ra=a.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,l=ri(ro,n);return(0,c.jsx)(tC,{present:r||rw(l.checked)||!0===l.checked,children:(0,c.jsx)(h.sG.span,{...o,ref:t,"data-state":ry(l.checked)})})});ra.displayName=ro;var ru=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,c.jsx)(h.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ru.displayName="MenuSeparator";var rs=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nV(n);return(0,c.jsx)(tv,{...o,...r,ref:t})});rs.displayName="MenuArrow";var rd="MenuSub",[rc,rg]=nD(rd),rf=e=>{let{__scopeMenu:t,children:n,open:r=!1,onOpenChange:o}=e,l=nj(rd,t),i=nV(t),[u,s]=a.useState(null),[d,g]=a.useState(null),f=F(o);return a.useEffect(()=>(!1===l.open&&f(!1),()=>f(!1)),[l.open,f]),(0,c.jsx)(tu,{...i,children:(0,c.jsx)(nT,{scope:t,open:r,onOpenChange:f,content:d,onContentChange:g,children:(0,c.jsx)(rc,{scope:t,contentId:K(),triggerId:K(),trigger:u,onTriggerChange:s,children:n})})})};rf.displayName=rd;var rp="MenuSubTrigger",rm=a.forwardRef((e,t)=>{let n=nj(rp,e.__scopeMenu),r=nG(rp,e.__scopeMenu),o=rg(rp,e.__scopeMenu),l=nX(rp,e.__scopeMenu),i=a.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:g}=l,f={__scopeMenu:e.__scopeMenu},p=a.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return a.useEffect(()=>p,[p]),a.useEffect(()=>{let e=u.current;return()=>{window.clearTimeout(e),g(null)}},[u,g]),(0,c.jsx)(nH,{asChild:!0,...f,children:(0,c.jsx)(n7,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":rb(n.open),...e,ref:(0,d.t)(t,o.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:s(e.onPointerMove,rx(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||i.current||(l.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:s(e.onPointerLeave,rx(e=>{var t,r;p();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,i="right"===t,a=o[i?"left":"right"],s=o[i?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:a,y:o.top},{x:s,y:o.top},{x:s,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(u.current),u.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:s(e.onKeyDown,t=>{let o=""!==l.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&nF[r.dir].includes(t.key)){var i;n.onOpenChange(!0),null==(i=n.content)||i.focus(),t.preventDefault()}})})})});rm.displayName=rp;var rh="MenuSubContent",rv=a.forwardRef((e,t)=>{let n=nU(n$,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,l=nj(n$,e.__scopeMenu),i=nG(n$,e.__scopeMenu),u=rg(rh,e.__scopeMenu),g=a.useRef(null),f=(0,d.s)(t,g);return(0,c.jsx)(nA.Provider,{scope:e.__scopeMenu,children:(0,c.jsx)(tC,{present:r||l.open,children:(0,c.jsx)(nA.Slot,{scope:e.__scopeMenu,children:(0,c.jsx)(n0,{id:u.contentId,"aria-labelledby":u.triggerId,...o,ref:f,align:"start",side:"rtl"===i.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;i.isUsingKeyboardRef.current&&(null==(t=g.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:s(e.onFocusOutside,e=>{e.target!==u.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:s(e.onEscapeKeyDown,e=>{i.onClose(),e.preventDefault()}),onKeyDown:s(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=nP[i.dir].includes(e.key);if(t&&n){var r;l.onOpenChange(!1),null==(r=u.trigger)||r.focus(),e.preventDefault()}})})})})})});function rb(e){return e?"open":"closed"}function rw(e){return"indeterminate"===e}function ry(e){return rw(e)?"indeterminate":e?"checked":"unchecked"}function rx(e){return t=>"mouse"===t.pointerType?e(t):void 0}rv.displayName=rh;var rC="DropdownMenu",[rR,rS]=g(rC,[nk]),rE=nk(),[rM,rF]=rR(rC),rP=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:l,onOpenChange:i,modal:u=!0}=e,s=rE(t),d=a.useRef(null),[g,f]=m({prop:o,defaultProp:null!=l&&l,onChange:i,caller:rC});return(0,c.jsx)(rM,{scope:t,triggerId:K(),triggerRef:d,contentId:K(),open:g,onOpenChange:f,onOpenToggle:a.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,c.jsx)(nz,{...s,open:g,onOpenChange:f,dir:r,modal:u,children:n})})};rP.displayName=rC;var rI="DropdownMenuTrigger",rA=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,l=rF(rI,n),i=rE(n);return(0,c.jsx)(nH,{asChild:!0,...i,children:(0,c.jsx)(h.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,d.t)(t,l.triggerRef),onPointerDown:s(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:s(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});rA.displayName=rI;var rL=e=>{let{__scopeDropdownMenu:t,...n}=e,r=rE(t);return(0,c.jsx)(nK,{...r,...n})};rL.displayName="DropdownMenuPortal";var r_="DropdownMenuContent",rD=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rF(r_,n),l=rE(n),i=a.useRef(!1);return(0,c.jsx)(nY,{id:o.contentId,"aria-labelledby":o.triggerId,...l,...r,ref:t,onCloseAutoFocus:s(e.onCloseAutoFocus,e=>{var t;i.current||null==(t=o.triggerRef.current)||t.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:s(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rD.displayName=r_;var rk=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(n1,{...o,...r,ref:t})});rk.displayName="DropdownMenuGroup";var rV=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(n2,{...o,...r,ref:t})});rV.displayName="DropdownMenuLabel";var rO=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(n5,{...o,...r,ref:t})});rO.displayName="DropdownMenuItem";var rT=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(n4,{...o,...r,ref:t})});rT.displayName="DropdownMenuCheckboxItem";var rj=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(rt,{...o,...r,ref:t})});rj.displayName="DropdownMenuRadioGroup";var rN=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(rr,{...o,...r,ref:t})});rN.displayName="DropdownMenuRadioItem";var rG=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(ra,{...o,...r,ref:t})});rG.displayName="DropdownMenuItemIndicator";var rz=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(ru,{...o,...r,ref:t})});rz.displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(rs,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var rH=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(rm,{...o,...r,ref:t})});rH.displayName="DropdownMenuSubTrigger";var rB=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rE(n);return(0,c.jsx)(rv,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rB.displayName="DropdownMenuSubContent";var rW=rP,rU=rA,rK=rL,r$=rD,rq=rk,rX=rV,rY=rO,rZ=rT,rJ=rj,rQ=rN,r0=rG,r1=rz,r2=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:l}=e,i=rE(t),[a,u]=m({prop:r,defaultProp:null!=l&&l,onChange:o,caller:"DropdownMenuSub"});return(0,c.jsx)(rf,{...i,open:a,onOpenChange:u,children:n})},r9=rH,r6=rB},7657:(e,t,n)=>{n.d(t,{Kv:()=>l,N4:()=>i});var r=n(7620),o=n(8175);function l(e,t){var n,o,l;return e?"function"==typeof(o=n=e)&&(()=>{let e=Object.getPrototypeOf(o);return e.prototype&&e.prototype.isReactComponent})()||"function"==typeof n||"object"==typeof(l=n)&&"symbol"==typeof l.$$typeof&&["react.memo","react.forward_ref"].includes(l.$$typeof.description)?r.createElement(e,t):e:null}function i(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=r.useState(()=>({current:(0,o.ZR)(t)})),[l,i]=r.useState(()=>n.current.initialState);return n.current.setOptions(t=>({...t,...e,state:{...l,...e.state},onStateChange:t=>{i(t),null==e.onStateChange||e.onStateChange(t)}})),n.current}},7911:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(8889).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8175:(e,t,n)=>{function r(e,t){return"function"==typeof e?e(t):e}function o(e,t){return n=>{t.setState(t=>({...t,[e]:r(n,t[e])}))}}function l(e){return e instanceof Function}n.d(t,{HT:()=>W,ZR:()=>B,h5:()=>$,hM:()=>U,kW:()=>K});function i(e,t,n){let r,o=[];return l=>{let i,a;n.key&&n.debug&&(i=Date.now());let u=e(l);if(!(u.length!==o.length||u.some((e,t)=>o[t]!==e)))return r;if(o=u,n.key&&n.debug&&(a=Date.now()),r=t(...u),null==n||null==n.onChange||n.onChange(r),n.key&&n.debug&&null!=n&&n.debug()){let e=Math.round((Date.now()-i)*100)/100,t=Math.round((Date.now()-a)*100)/100,r=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${o(t,5)} /${o(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*r,120))}deg 100% 31%);`,null==n?void 0:n.key)}return r}}function a(e,t,n,r){return{debug:()=>{var n;return null!=(n=null==e?void 0:e.debugAll)?n:e[t]},key:!1,onChange:r}}let u="debugHeaders";function s(e,t,n){var r;let o={id:null!=(r=n.id)?r:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=n=>{n.subHeaders&&n.subHeaders.length&&n.subHeaders.map(t),e.push(n)};return t(o),e},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(o,e)}),o}function d(e,t,n,r){var o,l;let i=0,a=function(e,t){void 0===t&&(t=1),i=Math.max(i,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var n;null!=(n=e.columns)&&n.length&&a(e.columns,t+1)},0)};a(e);let u=[],d=(e,t)=>{let o={depth:t,id:[r,`${t}`].filter(Boolean).join("_"),headers:[]},l=[];e.forEach(e=>{let i,a=[...l].reverse()[0],u=e.column.depth===o.depth,d=!1;if(u&&e.column.parent?i=e.column.parent:(i=e.column,d=!0),a&&(null==a?void 0:a.column)===i)a.subHeaders.push(e);else{let o=s(n,i,{id:[r,t,i.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:d,placeholderId:d?`${l.filter(e=>e.column===i).length}`:void 0,depth:t,index:l.length});o.subHeaders.push(e),l.push(o)}o.headers.push(e),e.headerGroup=o}),u.push(o),t>0&&d(l,t-1)};d(t.map((e,t)=>s(n,e,{depth:i,index:t})),i-1),u.reverse();let c=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,n=0,r=[0];return e.subHeaders&&e.subHeaders.length?(r=[],c(e.subHeaders).forEach(e=>{let{colSpan:n,rowSpan:o}=e;t+=n,r.push(o)})):t=1,n+=Math.min(...r),e.colSpan=t,e.rowSpan=n,{colSpan:t,rowSpan:n}});return c(null!=(o=null==(l=u[0])?void 0:l.headers)?o:[]),u}let c=(e,t,n,r,o,l,u)=>{let s={id:t,index:r,original:n,depth:o,parentId:u,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(s._valuesCache.hasOwnProperty(t))return s._valuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return s._valuesCache[t]=n.accessorFn(s.original,r),s._valuesCache[t]},getUniqueValues:t=>{if(s._uniqueValuesCache.hasOwnProperty(t))return s._uniqueValuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return n.columnDef.getUniqueValues?s._uniqueValuesCache[t]=n.columnDef.getUniqueValues(s.original,r):s._uniqueValuesCache[t]=[s.getValue(t)],s._uniqueValuesCache[t]},renderValue:t=>{var n;return null!=(n=s.getValue(t))?n:e.options.renderFallbackValue},subRows:null!=l?l:[],getLeafRows:()=>(function(e,t){let n=[],r=e=>{e.forEach(e=>{n.push(e);let o=t(e);null!=o&&o.length&&r(o)})};return r(e),n})(s.subRows,e=>e.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let e=[],t=s;for(;;){let n=t.getParentRow();if(!n)break;e.push(n),t=n}return e.reverse()},getAllCells:i(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,n,r){let o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(r),renderValue:()=>{var t;return null!=(t=o.getValue())?t:e.options.renderFallbackValue},getContext:i(()=>[e,n,t,o],(e,t,n,r)=>({table:e,column:t,row:n,cell:r,getValue:r.getValue,renderValue:r.renderValue}),a(e.options,"debugCells","cell.getContext"))};return e._features.forEach(r=>{null==r.createCell||r.createCell(o,n,t,e)},{}),o})(e,s,t,t.id)),a(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:i(()=>[s.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),a(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let n=e._features[t];null==n||null==n.createRow||n.createRow(s,e)}return s},g=(e,t,n)=>{var r,o;let l=null==n||null==(r=n.toString())?void 0:r.toLowerCase();return!!(null==(o=e.getValue(t))||null==(o=o.toString())||null==(o=o.toLowerCase())?void 0:o.includes(l))};g.autoRemove=e=>C(e);let f=(e,t,n)=>{var r;return!!(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.includes(n))};f.autoRemove=e=>C(e);let p=(e,t,n)=>{var r;return(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.toLowerCase())===(null==n?void 0:n.toLowerCase())};p.autoRemove=e=>C(e);let m=(e,t,n)=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)};m.autoRemove=e=>C(e);let h=(e,t,n)=>!n.some(n=>{var r;return!(null!=(r=e.getValue(t))&&r.includes(n))});h.autoRemove=e=>C(e)||!(null!=e&&e.length);let v=(e,t,n)=>n.some(n=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)});v.autoRemove=e=>C(e)||!(null!=e&&e.length);let b=(e,t,n)=>e.getValue(t)===n;b.autoRemove=e=>C(e);let w=(e,t,n)=>e.getValue(t)==n;w.autoRemove=e=>C(e);let y=(e,t,n)=>{let[r,o]=n,l=e.getValue(t);return l>=r&&l<=o};y.resolveFilterValue=e=>{let[t,n]=e,r="number"!=typeof t?parseFloat(t):t,o="number"!=typeof n?parseFloat(n):n,l=null===t||Number.isNaN(r)?-1/0:r,i=null===n||Number.isNaN(o)?1/0:o;if(l>i){let e=l;l=i,i=e}return[l,i]},y.autoRemove=e=>C(e)||C(e[0])&&C(e[1]);let x={includesString:g,includesStringSensitive:f,equalsString:p,arrIncludes:m,arrIncludesAll:h,arrIncludesSome:v,equals:b,weakEquals:w,inNumberRange:y};function C(e){return null==e||""===e}function R(e,t,n){return!!e&&!!e.autoRemove&&e.autoRemove(t,n)||void 0===t||"string"==typeof t&&!t}let S={sum:(e,t,n)=>n.reduce((t,n)=>{let r=n.getValue(e);return t+("number"==typeof r?r:0)},0),min:(e,t,n)=>{let r;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(r>n||void 0===r&&n>=n)&&(r=n)}),r},max:(e,t,n)=>{let r;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(r<n||void 0===r&&n>=n)&&(r=n)}),r},extent:(e,t,n)=>{let r,o;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(void 0===r?n>=n&&(r=o=n):(r>n&&(r=n),o<n&&(o=n)))}),[r,o]},mean:(e,t)=>{let n=0,r=0;if(t.forEach(t=>{let o=t.getValue(e);null!=o&&(o*=1)>=o&&(++n,r+=o)}),n)return r/n},median:(e,t)=>{if(!t.length)return;let n=t.map(t=>t.getValue(e));if(!function(e){return Array.isArray(e)&&e.every(e=>"number"==typeof e)}(n))return;if(1===n.length)return n[0];let r=Math.floor(n.length/2),o=n.sort((e,t)=>e-t);return n.length%2!=0?o[r]:(o[r-1]+o[r])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},E=()=>({left:[],right:[]}),M={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},F=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),P=null;function I(e){return"touchstart"===e.type}function A(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let L=()=>({pageIndex:0,pageSize:10}),_=()=>({top:[],bottom:[]}),D=(e,t,n,r,o)=>{var l;let i=o.getRow(t,!0);n?(i.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),i.getCanSelect()&&(e[t]=!0)):delete e[t],r&&null!=(l=i.subRows)&&l.length&&i.getCanSelectSubRows()&&i.subRows.forEach(t=>D(e,t.id,n,r,o))};function k(e,t){let n=e.getState().rowSelection,r=[],o={},l=function(e,t){return e.map(e=>{var t;let i=V(e,n);if(i&&(r.push(e),o[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:l(e.subRows)}),i)return e}).filter(Boolean)};return{rows:l(t.rows),flatRows:r,rowsById:o}}function V(e,t){var n;return null!=(n=t[e.id])&&n}function O(e,t,n){var r;if(!(null!=(r=e.subRows)&&r.length))return!1;let o=!0,l=!1;return e.subRows.forEach(e=>{if((!l||o)&&(e.getCanSelect()&&(V(e,t)?l=!0:o=!1),e.subRows&&e.subRows.length)){let n=O(e,t);"all"===n?l=!0:("some"===n&&(l=!0),o=!1)}}),o?"all":!!l&&"some"}let T=/([0-9]+)/gm;function j(e,t){return e===t?0:e>t?1:-1}function N(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function G(e,t){let n=e.split(T).filter(Boolean),r=t.split(T).filter(Boolean);for(;n.length&&r.length;){let e=n.shift(),t=r.shift(),o=parseInt(e,10),l=parseInt(t,10),i=[o,l].sort();if(isNaN(i[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(i[1]))return isNaN(o)?-1:1;if(o>l)return 1;if(l>o)return -1}return n.length-r.length}let z={alphanumeric:(e,t,n)=>G(N(e.getValue(n)).toLowerCase(),N(t.getValue(n)).toLowerCase()),alphanumericCaseSensitive:(e,t,n)=>G(N(e.getValue(n)),N(t.getValue(n))),text:(e,t,n)=>j(N(e.getValue(n)).toLowerCase(),N(t.getValue(n)).toLowerCase()),textCaseSensitive:(e,t,n)=>j(N(e.getValue(n)),N(t.getValue(n))),datetime:(e,t,n)=>{let r=e.getValue(n),o=t.getValue(n);return r>o?1:r<o?-1:0},basic:(e,t,n)=>j(e.getValue(n),t.getValue(n))},H=[{createTable:e=>{e.getHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>{var l,i;let a=null!=(l=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?l:[],u=null!=(i=null==o?void 0:o.map(e=>n.find(t=>t.id===e)).filter(Boolean))?i:[];return d(t,[...a,...n.filter(e=>!(null!=r&&r.includes(e.id))&&!(null!=o&&o.includes(e.id))),...u],e)},a(e.options,u,"getHeaderGroups")),e.getCenterHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>d(t,n=n.filter(e=>!(null!=r&&r.includes(e.id))&&!(null!=o&&o.includes(e.id))),e,"center"),a(e.options,u,"getCenterHeaderGroups")),e.getLeftHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,r)=>{var o;return d(t,null!=(o=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"left")},a(e.options,u,"getLeftHeaderGroups")),e.getRightHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,r)=>{var o;return d(t,null!=(o=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"right")},a(e.options,u,"getRightHeaderGroups")),e.getFooterGroups=i(()=>[e.getHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getFooterGroups")),e.getLeftFooterGroups=i(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getLeftFooterGroups")),e.getCenterFooterGroups=i(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getCenterFooterGroups")),e.getRightFooterGroups=i(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getRightFooterGroups")),e.getFlatHeaders=i(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getFlatHeaders")),e.getLeftFlatHeaders=i(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getLeftFlatHeaders")),e.getCenterFlatHeaders=i(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getCenterFlatHeaders")),e.getRightFlatHeaders=i(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getRightFlatHeaders")),e.getCenterLeafHeaders=i(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,u,"getCenterLeafHeaders")),e.getLeftLeafHeaders=i(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,u,"getLeftLeafHeaders")),e.getRightLeafHeaders=i(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,u,"getRightLeafHeaders")),e.getLeafHeaders=i(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,n)=>{var r,o,l,i,a,u;return[...null!=(r=null==(o=e[0])?void 0:o.headers)?r:[],...null!=(l=null==(i=t[0])?void 0:i.headers)?l:[],...null!=(a=null==(u=n[0])?void 0:u.headers)?a:[]].map(e=>e.getLeafHeaders()).flat()},a(e.options,u,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:o("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=n?n:!e.getIsVisible()}))},e.getIsVisible=()=>{var n,r;let o=e.columns;return null==(n=o.length?o.some(e=>e.getIsVisible()):null==(r=t.getState().columnVisibility)?void 0:r[e.id])||n},e.getCanHide=()=>{var n,r;return(null==(n=e.columnDef.enableHiding)||n)&&(null==(r=t.options.enableHiding)||r)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=i(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),a(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=i(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,n)=>[...e,...t,...n],a(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,n)=>i(()=>[n(),n().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),a(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:null!=(n=e.initialState.columnVisibility)?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=null!=(n=t)?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,n)=>({...e,[n.id]:t||!(null!=n.getCanHide&&n.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible(null==(n=t.target)?void 0:n.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:o("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=i(e=>[A(t,e)],t=>t.findIndex(t=>t.id===e.id),a(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var r;return(null==(r=A(t,n)[0])?void 0:r.id)===e.id},e.getIsLastColumn=n=>{var r;let o=A(t,n);return(null==(r=o[o.length-1])?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:null!=(n=e.initialState.columnOrder)?n:[])},e._getOrderColumnsFn=i(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,n)=>r=>{let o=[];if(null!=e&&e.length){let t=[...e],n=[...r];for(;n.length&&t.length;){let e=t.shift(),r=n.findIndex(t=>t.id===e);r>-1&&o.push(n.splice(r,1)[0])}o=[...o,...n]}else o=r;return function(e,t,n){if(!(null!=t&&t.length)||!n)return e;let r=e.filter(e=>!t.includes(e.id));return"remove"===n?r:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...r]}(o,t,n)},a(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:E(),...e}),getDefaultOptions:e=>({onColumnPinningChange:o("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{let r=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,o,l,i,a,u;return"right"===n?{left:(null!=(l=null==e?void 0:e.left)?l:[]).filter(e=>!(null!=r&&r.includes(e))),right:[...(null!=(i=null==e?void 0:e.right)?i:[]).filter(e=>!(null!=r&&r.includes(e))),...r]}:"left"===n?{left:[...(null!=(a=null==e?void 0:e.left)?a:[]).filter(e=>!(null!=r&&r.includes(e))),...r],right:(null!=(u=null==e?void 0:e.right)?u:[]).filter(e=>!(null!=r&&r.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=r&&r.includes(e))),right:(null!=(o=null==e?void 0:e.right)?o:[]).filter(e=>!(null!=r&&r.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var n,r,o;return(null==(n=e.columnDef.enablePinning)||n)&&(null==(r=null!=(o=t.options.enableColumnPinning)?o:t.options.enablePinning)||r)}),e.getIsPinned=()=>{let n=e.getLeafColumns().map(e=>e.id),{left:r,right:o}=t.getState().columnPinning,l=n.some(e=>null==r?void 0:r.includes(e)),i=n.some(e=>null==o?void 0:o.includes(e));return l?"left":!!i&&"right"},e.getPinnedIndex=()=>{var n,r;let o=e.getIsPinned();return o?null!=(n=null==(r=t.getState().columnPinning)||null==(r=r[o])?void 0:r.indexOf(e.id))?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,n)=>{let r=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!r.includes(e.column.id))},a(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),a(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),a(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,r;return e.setColumnPinning(t?E():null!=(n=null==(r=e.initialState)?void 0:r.columnPinning)?n:E())},e.getIsSomeColumnsPinned=t=>{var n,r,o;let l=e.getState().columnPinning;return t?!!(null==(n=l[t])?void 0:n.length):!!((null==(r=l.left)?void 0:r.length)||(null==(o=l.right)?void 0:o.length))},e.getLeftLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),a(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),a(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,n)=>{let r=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!r.includes(e.id))},a(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:o("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"string"==typeof r?x.includesString:"number"==typeof r?x.inNumberRange:"boolean"==typeof r||null!==r&&"object"==typeof r?x.equals:Array.isArray(r)?x.arrIncludes:x.weakEquals},e.getFilterFn=()=>{var n,r;return l(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(n=null==(r=t.options.filterFns)?void 0:r[e.columnDef.filterFn])?n:x[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,r,o;return(null==(n=e.columnDef.enableColumnFilter)||n)&&(null==(r=t.options.enableColumnFilters)||r)&&(null==(o=t.options.enableFilters)||o)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return null==(n=t.getState().columnFilters)||null==(n=n.find(t=>t.id===e.id))?void 0:n.value},e.getFilterIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().columnFilters)?void 0:r.findIndex(t=>t.id===e.id))?n:-1},e.setFilterValue=n=>{t.setColumnFilters(t=>{var o,l;let i=e.getFilterFn(),a=null==t?void 0:t.find(t=>t.id===e.id),u=r(n,a?a.value:void 0);if(R(i,u,e))return null!=(o=null==t?void 0:t.filter(t=>t.id!==e.id))?o:[];let s={id:e.id,value:u};return a?null!=(l=null==t?void 0:t.map(t=>t.id===e.id?s:t))?l:[]:null!=t&&t.length?[...t,s]:[s]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let n=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var o;return null==(o=r(t,e))?void 0:o.filter(e=>{let t=n.find(t=>t.id===e.id);return!(t&&R(t.getFilterFn(),e.value,t))&&!0})})},e.resetColumnFilters=t=>{var n,r;e.setColumnFilters(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.columnFilters)?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:o("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;let r=null==(n=e.getCoreRowModel().flatRows[0])||null==(n=n._getAllCellsByColumnId()[t.id])?void 0:n.getValue();return"string"==typeof r||"number"==typeof r}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,r,o,l;return(null==(n=e.columnDef.enableGlobalFilter)||n)&&(null==(r=t.options.enableGlobalFilter)||r)&&(null==(o=t.options.enableFilters)||o)&&(null==(l=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||l)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>x.includesString,e.getGlobalFilterFn=()=>{var t,n;let{globalFilterFn:r}=e.options;return l(r)?r:"auto"===r?e.getGlobalAutoFilterFn():null!=(t=null==(n=e.options.filterFns)?void 0:n[r])?t:x[r]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:o("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let n=t.getFilteredRowModel().flatRows.slice(10),r=!1;for(let t of n){let n=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(n))return z.datetime;if("string"==typeof n&&(r=!0,n.split(T).length>1))return z.alphanumeric}return r?z.text:z.basic},e.getAutoSortDir=()=>{let n=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==n?void 0:n.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var n,r;if(!e)throw Error();return l(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(n=null==(r=t.options.sortingFns)?void 0:r[e.columnDef.sortingFn])?n:z[e.columnDef.sortingFn]},e.toggleSorting=(n,r)=>{let o=e.getNextSortingOrder(),l=null!=n;t.setSorting(i=>{let a,u=null==i?void 0:i.find(t=>t.id===e.id),s=null==i?void 0:i.findIndex(t=>t.id===e.id),d=[],c=l?n:"desc"===o;if("toggle"!=(a=null!=i&&i.length&&e.getCanMultiSort()&&r?u?"toggle":"add":null!=i&&i.length&&s!==i.length-1?"replace":u?"toggle":"replace")||l||o||(a="remove"),"add"===a){var g;(d=[...i,{id:e.id,desc:c}]).splice(0,d.length-(null!=(g=t.options.maxMultiSortColCount)?g:Number.MAX_SAFE_INTEGER))}else d="toggle"===a?i.map(t=>t.id===e.id?{...t,desc:c}:t):"remove"===a?i.filter(t=>t.id!==e.id):[{id:e.id,desc:c}];return d})},e.getFirstSortDir=()=>{var n,r;return(null!=(n=null!=(r=e.columnDef.sortDescFirst)?r:t.options.sortDescFirst)?n:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=n=>{var r,o;let l=e.getFirstSortDir(),i=e.getIsSorted();return i?(i===l||null!=(r=t.options.enableSortingRemoval)&&!r||!!n&&null!=(o=t.options.enableMultiRemove)&&!o)&&("desc"===i?"asc":"desc"):l},e.getCanSort=()=>{var n,r;return(null==(n=e.columnDef.enableSorting)||n)&&(null==(r=t.options.enableSorting)||r)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,r;return null!=(n=null!=(r=e.columnDef.enableMultiSort)?r:t.options.enableMultiSort)?n:!!e.accessorFn},e.getIsSorted=()=>{var n;let r=null==(n=t.getState().sorting)?void 0:n.find(t=>t.id===e.id);return!!r&&(r.desc?"desc":"asc")},e.getSortIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().sorting)?void 0:r.findIndex(t=>t.id===e.id))?n:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let n=e.getCanSort();return r=>{n&&(null==r.persist||r.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(r))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,r;e.setSorting(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.sorting)?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return null!=(t=null==(n=e.getValue())||null==n.toString?void 0:n.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:o("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var n,r;return(null==(n=e.columnDef.enableGrouping)||n)&&(null==(r=t.options.enableGrouping)||r)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"number"==typeof r?S.sum:"[object Date]"===Object.prototype.toString.call(r)?S.extent:void 0},e.getAggregationFn=()=>{var n,r;if(!e)throw Error();return l(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(n=null==(r=t.options.aggregationFns)?void 0:r[e.columnDef.aggregationFn])?n:S[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,r;e.setGrouping(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.grouping)?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];let r=t.getColumn(n);return null!=r&&r.columnDef.getGroupingValue?(e._groupingValuesCache[n]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,r)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=n.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:o("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var r,o;if(!t)return void e._queue(()=>{t=!0});if(null!=(r=null!=(o=e.options.autoResetAll)?o:e.options.autoResetExpanded)?r:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var n,r;e.setExpanded(t?{}:null!=(n=null==(r=e.initialState)?void 0:r.expanded)?n:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let n=e.split(".");t=Math.max(t,n.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(r=>{var o;let l=!0===r||!!(null!=r&&r[e.id]),i={};if(!0===r?Object.keys(t.getRowModel().rowsById).forEach(e=>{i[e]=!0}):i=r,n=null!=(o=n)?o:!l,!l&&n)return{...i,[e.id]:!0};if(l&&!n){let{[e.id]:t,...n}=i;return n}return r})},e.getIsExpanded=()=>{var n;let r=t.getState().expanded;return!!(null!=(n=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?n:!0===r||(null==r?void 0:r[e.id]))},e.getCanExpand=()=>{var n,r,o;return null!=(n=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?n:(null==(r=t.options.enableExpanding)||r)&&!!(null!=(o=e.subRows)&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,r=e;for(;n&&r.parentId;)n=(r=t.getRow(r.parentId,!0)).getIsExpanded();return n},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{...L(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:o("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var r,o;if(!t)return void e._queue(()=>{t=!0});if(null!=(r=null!=(o=e.options.autoResetAll)?o:e.options.autoResetPageIndex)?r:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>r(t,e)),e.resetPagination=t=>{var n;e.setPagination(t?L():null!=(n=e.initialState.pagination)?n:L())},e.setPageIndex=t=>{e.setPagination(n=>{let o=r(t,n.pageIndex);return o=Math.max(0,Math.min(o,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...n,pageIndex:o}})},e.resetPageIndex=t=>{var n,r;e.setPageIndex(t?0:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageIndex)?n:0)},e.resetPageSize=t=>{var n,r;e.setPageSize(t?10:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageSize)?n:10)},e.setPageSize=t=>{e.setPagination(e=>{let n=Math.max(1,r(t,e.pageSize)),o=Math.floor(e.pageSize*e.pageIndex/n);return{...e,pageIndex:o,pageSize:n}})},e.setPageCount=t=>e.setPagination(n=>{var o;let l=r(t,null!=(o=e.options.pageCount)?o:-1);return"number"==typeof l&&(l=Math.max(-1,l)),{...n,pageCount:l}}),e.getPageOptions=i(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},a(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,n=e.getPageCount();return -1===n||0!==n&&t<n-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:_(),...e}),getDefaultOptions:e=>({onRowPinningChange:o("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,r,o)=>{let l=r?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],i=new Set([...o?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],e.id,...l]);t.setRowPinning(e=>{var t,r,o,l,a,u;return"bottom"===n?{top:(null!=(o=null==e?void 0:e.top)?o:[]).filter(e=>!(null!=i&&i.has(e))),bottom:[...(null!=(l=null==e?void 0:e.bottom)?l:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)]}:"top"===n?{top:[...(null!=(a=null==e?void 0:e.top)?a:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)],bottom:(null!=(u=null==e?void 0:e.bottom)?u:[]).filter(e=>!(null!=i&&i.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=i&&i.has(e))),bottom:(null!=(r=null==e?void 0:e.bottom)?r:[]).filter(e=>!(null!=i&&i.has(e)))}})},e.getCanPin=()=>{var n;let{enableRowPinning:r,enablePinning:o}=t.options;return"function"==typeof r?r(e):null==(n=null!=r?r:o)||n},e.getIsPinned=()=>{let n=[e.id],{top:r,bottom:o}=t.getState().rowPinning,l=n.some(e=>null==r?void 0:r.includes(e)),i=n.some(e=>null==o?void 0:o.includes(e));return l?"top":!!i&&"bottom"},e.getPinnedIndex=()=>{var n,r;let o=e.getIsPinned();if(!o)return -1;let l=null==(n="top"===o?t.getTopRows():t.getBottomRows())?void 0:n.map(e=>{let{id:t}=e;return t});return null!=(r=null==l?void 0:l.indexOf(e.id))?r:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,r;return e.setRowPinning(t?_():null!=(n=null==(r=e.initialState)?void 0:r.rowPinning)?n:_())},e.getIsSomeRowsPinned=t=>{var n,r,o;let l=e.getState().rowPinning;return t?!!(null==(n=l[t])?void 0:n.length):!!((null==(r=l.top)?void 0:r.length)||(null==(o=l.bottom)?void 0:o.length))},e._getPinnedRows=(t,n,r)=>{var o;return(null==(o=e.options.keepPinnedRows)||o?(null!=n?n:[]).map(t=>{let n=e.getRow(t,!0);return n.getIsAllParentsExpanded()?n:null}):(null!=n?n:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:r}))},e.getTopRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),a(e.options,"debugRows","getTopRows")),e.getBottomRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),a(e.options,"debugRows","getBottomRows")),e.getCenterRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,n)=>{let r=new Set([...null!=t?t:[],...null!=n?n:[]]);return e.filter(e=>!r.has(e.id))},a(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:o("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:null!=(n=e.initialState.rowSelection)?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let r={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(e=>{e.getCanSelect()&&(r[e.id]=!0)}):o.forEach(e=>{delete r[e.id]}),r})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{let r=void 0!==t?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(t=>{D(o,t.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=i(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?k(e,n):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=i(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?k(e,n):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=i(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?k(e,n):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState(),r=!!(t.length&&Object.keys(n).length);return r&&t.some(e=>e.getCanSelect()&&!n[e.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:n}=e.getState(),r=!!t.length;return r&&t.some(e=>!n[e.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var t;let n=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,r)=>{let o=e.getIsSelected();t.setRowSelection(l=>{var i;if(n=void 0!==n?n:!o,e.getCanSelect()&&o===n)return l;let a={...l};return D(a,e.id,n,null==(i=null==r?void 0:r.selectChildren)||i,t),a})},e.getIsSelected=()=>{let{rowSelection:n}=t.getState();return V(e,n)},e.getIsSomeSelected=()=>{let{rowSelection:n}=t.getState();return"some"===O(e,n)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:n}=t.getState();return"all"===O(e,n)},e.getCanSelect=()=>{var n;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(n=t.options.enableRowSelection)||n},e.getCanSelectSubRows=()=>{var n;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(n=t.options.enableSubRowSelection)||n},e.getCanMultiSelect=()=>{var n;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(n=t.options.enableMultiRowSelection)||n},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return n=>{var r;t&&e.toggleSelected(null==(r=n.target)?void 0:r.checked)}}}},{getDefaultColumnDef:()=>M,getInitialState:e=>({columnSizing:{},columnSizingInfo:F(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:o("columnSizing",e),onColumnSizingInfoChange:o("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,r,o;let l=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(n=e.columnDef.minSize)?n:M.minSize,null!=(r=null!=l?l:e.columnDef.size)?r:M.size),null!=(o=e.columnDef.maxSize)?o:M.maxSize)},e.getStart=i(e=>[e,A(t,e),t.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),a(t.options,"debugColumns","getStart")),e.getAfter=i(e=>[e,A(t,e),t.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),a(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:n,...r}=t;return r})},e.getCanResize=()=>{var n,r;return(null==(n=e.columnDef.enableResizing)||n)&&(null==(r=t.options.enableColumnResizing)||r)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,n=e=>{if(e.subHeaders.length)e.subHeaders.forEach(n);else{var r;t+=null!=(r=e.column.getSize())?r:0}};return n(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=n=>{let r=t.getColumn(e.column.id),o=null==r?void 0:r.getCanResize();return l=>{if(!r||!o||(null==l.persist||l.persist(),I(l)&&l.touches&&l.touches.length>1))return;let i=e.getSize(),a=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[r.id,r.getSize()]],u=I(l)?Math.round(l.touches[0].clientX):l.clientX,s={},d=(e,n)=>{"number"==typeof n&&(t.setColumnSizingInfo(e=>{var r,o;let l="rtl"===t.options.columnResizeDirection?-1:1,i=(n-(null!=(r=null==e?void 0:e.startOffset)?r:0))*l,a=Math.max(i/(null!=(o=null==e?void 0:e.startSize)?o:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,n]=e;s[t]=Math.round(100*Math.max(n+n*a,0))/100}),{...e,deltaOffset:i,deltaPercentage:a}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...s})))},c=e=>d("move",e),g=e=>{d("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},f=n||("undefined"!=typeof document?document:null),p={moveHandler:e=>c(e.clientX),upHandler:e=>{null==f||f.removeEventListener("mousemove",p.moveHandler),null==f||f.removeEventListener("mouseup",p.upHandler),g(e.clientX)}},m={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),c(e.touches[0].clientX),!1),upHandler:e=>{var t;null==f||f.removeEventListener("touchmove",m.moveHandler),null==f||f.removeEventListener("touchend",m.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),g(null==(t=e.touches[0])?void 0:t.clientX)}},h=!!function(){if("boolean"==typeof P)return P;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return P=e}()&&{passive:!1};I(l)?(null==f||f.addEventListener("touchmove",m.moveHandler,h),null==f||f.addEventListener("touchend",m.upHandler,h)):(null==f||f.addEventListener("mousemove",p.moveHandler,h),null==f||f.addEventListener("mouseup",p.upHandler,h)),t.setColumnSizingInfo(e=>({...e,startOffset:u,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:a,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:null!=(n=e.initialState.columnSizing)?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?F():null!=(n=e.initialState.columnSizingInfo)?n:F())},e.getTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getLeftHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getCenterHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getRightHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function B(e){var t,n;let o=[...H,...null!=(t=e._features)?t:[]],l={_features:o},u=l._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(l)),{}),s=e=>l.options.mergeOptions?l.options.mergeOptions(u,e):{...u,...e},d={...null!=(n=e.initialState)?n:{}};l._features.forEach(e=>{var t;d=null!=(t=null==e.getInitialState?void 0:e.getInitialState(d))?t:d});let c=[],g=!1,f={_features:o,options:{...u,...e},initialState:d,_queue:e=>{c.push(e),g||(g=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();g=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{l.setState(l.initialState)},setOptions:e=>{let t=r(e,l.options);l.options=s(t)},getState:()=>l.options.state,setState:e=>{null==l.options.onStateChange||l.options.onStateChange(e)},_getRowId:(e,t,n)=>{var r;return null!=(r=null==l.options.getRowId?void 0:l.options.getRowId(e,t,n))?r:`${n?[n.id,t].join("."):t}`},getCoreRowModel:()=>(l._getCoreRowModel||(l._getCoreRowModel=l.options.getCoreRowModel(l)),l._getCoreRowModel()),getRowModel:()=>l.getPaginationRowModel(),getRow:(e,t)=>{let n=(t?l.getPrePaginationRowModel():l.getRowModel()).rowsById[e];if(!n&&!(n=l.getCoreRowModel().rowsById[e]))throw Error();return n},_getDefaultColumnDef:i(()=>[l.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,n;return null!=(t=null==(n=e.renderValue())||null==n.toString?void 0:n.toString())?t:null},...l._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},a(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>l.options.columns,getAllColumns:i(()=>[l._getColumnDefs()],e=>{let t=function(e,n,r){return void 0===r&&(r=0),e.map(e=>{let o=function(e,t,n,r){var o,l;let u,s={...e._getDefaultColumnDef(),...t},d=s.accessorKey,c=null!=(o=null!=(l=s.id)?l:d?"function"==typeof String.prototype.replaceAll?d.replaceAll(".","_"):d.replace(/\./g,"_"):void 0)?o:"string"==typeof s.header?s.header:void 0;if(s.accessorFn?u=s.accessorFn:d&&(u=d.includes(".")?e=>{let t=e;for(let e of d.split(".")){var n;t=null==(n=t)?void 0:n[e]}return t}:e=>e[s.accessorKey]),!c)throw Error();let g={id:`${String(c)}`,accessorFn:u,parent:r,depth:n,columnDef:s,columns:[],getFlatColumns:i(()=>[!0],()=>{var e;return[g,...null==(e=g.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},a(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:i(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=g.columns)&&t.length?e(g.columns.flatMap(e=>e.getLeafColumns())):[g]},a(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(g,e);return g}(l,e,r,n);return o.columns=e.columns?t(e.columns,o,r+1):[],o})};return t(e)},a(e,"debugColumns","getAllColumns")),getAllFlatColumns:i(()=>[l.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),a(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:i(()=>[l.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),a(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:i(()=>[l.getAllColumns(),l._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),a(e,"debugColumns","getAllLeafColumns")),getColumn:e=>l._getAllFlatColumnsById()[e]};Object.assign(l,f);for(let e=0;e<l._features.length;e++){let t=l._features[e];null==t||null==t.createTable||t.createTable(l)}return l}function W(){return e=>i(()=>[e.options.data],t=>{let n={rows:[],flatRows:[],rowsById:{}},r=function(t,o,l){void 0===o&&(o=0);let i=[];for(let u=0;u<t.length;u++){let s=c(e,e._getRowId(t[u],u,l),t[u],u,o,void 0,null==l?void 0:l.id);if(n.flatRows.push(s),n.rowsById[s.id]=s,i.push(s),e.options.getSubRows){var a;s.originalSubRows=e.options.getSubRows(t[u],u),null!=(a=s.originalSubRows)&&a.length&&(s.subRows=r(s.originalSubRows,o+1,s))}}return i};return n.rows=r(t),n},a(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function U(){return e=>i(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,n,r)=>{var o,l,i;let a,u;if(!t.rows.length||!(null!=n&&n.length)&&!r){for(let e=0;e<t.flatRows.length;e++)t.flatRows[e].columnFilters={},t.flatRows[e].columnFiltersMeta={};return t}let s=[],d=[];(null!=n?n:[]).forEach(t=>{var n;let r=e.getColumn(t.id);if(!r)return;let o=r.getFilterFn();o&&s.push({id:t.id,filterFn:o,resolvedValue:null!=(n=null==o.resolveFilterValue?void 0:o.resolveFilterValue(t.value))?n:t.value})});let g=(null!=n?n:[]).map(e=>e.id),f=e.getGlobalFilterFn(),p=e.getAllLeafColumns().filter(e=>e.getCanGlobalFilter());r&&f&&p.length&&(g.push("__global__"),p.forEach(e=>{var t;d.push({id:e.id,filterFn:f,resolvedValue:null!=(t=null==f.resolveFilterValue?void 0:f.resolveFilterValue(r))?t:r})}));for(let e=0;e<t.flatRows.length;e++){let n=t.flatRows[e];if(n.columnFilters={},s.length)for(let e=0;e<s.length;e++){let t=(a=s[e]).id;n.columnFilters[t]=a.filterFn(n,t,a.resolvedValue,e=>{n.columnFiltersMeta[t]=e})}if(d.length){for(let e=0;e<d.length;e++){let t=(u=d[e]).id;if(u.filterFn(n,t,u.resolvedValue,e=>{n.columnFiltersMeta[t]=e})){n.columnFilters.__global__=!0;break}}!0!==n.columnFilters.__global__&&(n.columnFilters.__global__=!1)}}return o=t.rows,l=e=>{for(let t=0;t<g.length;t++)if(!1===e.columnFilters[g[t]])return!1;return!0},(i=e).options.filterFromLeafRows?function(e,t,n){var r;let o=[],l={},i=null!=(r=n.options.maxLeafRowFilterDepth)?r:100,a=function(e,r){void 0===r&&(r=0);let u=[];for(let d=0;d<e.length;d++){var s;let g=e[d],f=c(n,g.id,g.original,g.index,g.depth,void 0,g.parentId);if(f.columnFilters=g.columnFilters,null!=(s=g.subRows)&&s.length&&r<i){if(f.subRows=a(g.subRows,r+1),t(g=f)&&!f.subRows.length||t(g)||f.subRows.length){u.push(g),l[g.id]=g,o.push(g);continue}}else t(g=f)&&(u.push(g),l[g.id]=g,o.push(g))}return u};return{rows:a(e),flatRows:o,rowsById:l}}(o,l,i):function(e,t,n){var r;let o=[],l={},i=null!=(r=n.options.maxLeafRowFilterDepth)?r:100,a=function(e,r){void 0===r&&(r=0);let u=[];for(let d=0;d<e.length;d++){let g=e[d];if(t(g)){var s;if(null!=(s=g.subRows)&&s.length&&r<i){let e=c(n,g.id,g.original,g.index,g.depth,void 0,g.parentId);e.subRows=a(g.subRows,r+1),g=e}u.push(g),o.push(g),l[g.id]=g}}return u};return{rows:a(e),flatRows:o,rowsById:l}}(o,l,i)},a(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function K(e){return e=>i(()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded],(t,n)=>{let r;if(!n.rows.length)return n;let{pageSize:o,pageIndex:l}=t,{rows:i,flatRows:a,rowsById:u}=n,s=o*l;i=i.slice(s,s+o),(r=e.options.paginateExpandedRows?{rows:i,flatRows:a,rowsById:u}:function(e){let t=[],n=e=>{var r;t.push(e),null!=(r=e.subRows)&&r.length&&e.getIsExpanded()&&e.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}({rows:i,flatRows:a,rowsById:u})).flatRows=[];let d=e=>{r.flatRows.push(e),e.subRows.length&&e.subRows.forEach(d)};return r.rows.forEach(d),r},a(e.options,"debugTable","getPaginationRowModel"))}function $(){return e=>i(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(null!=t&&t.length))return n;let r=e.getState().sorting,o=[],l=r.filter(t=>{var n;return null==(n=e.getColumn(t.id))?void 0:n.getCanSort()}),i={};l.forEach(t=>{let n=e.getColumn(t.id);n&&(i[t.id]={sortUndefined:n.columnDef.sortUndefined,invertSorting:n.columnDef.invertSorting,sortingFn:n.getSortingFn()})});let a=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let r=0;r<l.length;r+=1){var n;let o=l[r],a=i[o.id],u=a.sortUndefined,s=null!=(n=null==o?void 0:o.desc)&&n,d=0;if(u){let n=e.getValue(o.id),r=t.getValue(o.id),l=void 0===n,i=void 0===r;if(l||i){if("first"===u)return l?-1:1;if("last"===u)return l?1:-1;d=l&&i?0:l?u:-u}}if(0===d&&(d=a.sortingFn(e,t,o.id)),0!==d)return s&&(d*=-1),a.invertSorting&&(d*=-1),d}return e.index-t.index}),t.forEach(e=>{var t;o.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=a(e.subRows))}),t};return{rows:a(n.rows),flatRows:o,rowsById:n.rowsById}},a(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}},8889:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(7620);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:u,className:s="",children:d,iconNode:c,...g}=e;return(0,r.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:n,strokeWidth:u?24*Number(a)/Number(o):a,className:l("lucide",s),...g},[...c.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:u,...s}=n;return(0,r.createElement)(a,{ref:i,iconNode:t,className:l("lucide-".concat(o(e)),u),...s})});return n.displayName="".concat(e),n}},9640:(e,t,n)=>{n.d(t,{s:()=>i,t:()=>l});var r=n(7620);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function i(...e){return r.useCallback(l(...e),e)}},9649:(e,t,n)=>{n.d(t,{DX:()=>a,TL:()=>i});var r=n(7620),o=n(9640),l=n(4568);function i(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...l}=e;if(r.isValidElement(n)){var i;let e,a,u=(i=n,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,a=r.Children.toArray(o),u=a.find(s);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var a=i("Slot"),u=Symbol("radix.slottable");function s(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}}]);
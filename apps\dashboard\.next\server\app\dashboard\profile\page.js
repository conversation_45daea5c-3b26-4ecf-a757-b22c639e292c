(()=>{var e={};e.id=399,e.ids=[399],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},955:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>t});let t=(0,a(3952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monetizr\\\\apps\\\\dashboard\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\profile\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3575:(e,r,a)=>{Promise.resolve().then(a.bind(a,955))},3839:(e,r,a)=>{Promise.resolve().then(a.bind(a,3881))},3873:e=>{"use strict";e.exports=require("path")},3881:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>p});var t=a(3486),s=a(159),n=a(5626),o=a(5519),i=a(1507),d=a(4665);let l=i.Ik({name:i.Yj().min(1,{message:"Nama tidak boleh kosong."}),bio:i.Yj().optional()});function p(){let[e,r]=(0,s.useState)(null),{register:a,handleSubmit:i,formState:{errors:p,isSubmitting:c},reset:m}=(0,n.mN)({resolver:(0,o.u)(l)}),u=async e=>{try{if(!(await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Gagal memperbarui profil");r(e),alert("Profil berhasil diperbarui!")}catch(e){console.error(e),alert("Terjadi kesalahan saat memperbarui profil")}};return e?(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)(d.Zp,{className:"w-full max-w-md",children:(0,t.jsxs)("form",{onSubmit:i(u),children:[(0,t.jsx)(d.aR,{children:(0,t.jsx)(d.ZB,{children:"Profil Pengguna"})}),(0,t.jsxs)(d.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.JU,{htmlFor:"name",children:"Nama"}),(0,t.jsx)(d.pd,{id:"name",placeholder:"Nama Anda",...a("name")}),p.name&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:p.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.JU,{htmlFor:"bio",children:"Bio"}),(0,t.jsx)(d.pd,{id:"bio",placeholder:"Tentang Anda",...a("bio")})]})]}),(0,t.jsx)(d.wL,{children:(0,t.jsx)(d.$n,{type:"submit",disabled:c,children:c?"Menyimpan...":"Simpan Perubahan"})})]})})}):(0,t.jsx)("div",{children:"Memuat..."})}},7245:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>l});var t=a(4332),s=a(8819),n=a(7851),o=a.n(n),i=a(7540),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(r,d);let l={children:["",{children:["dashboard",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,955)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\profile\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,2341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\profile\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/profile/page",pathname:"/dashboard/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9699:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});var t=a(1253);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[191,77,253,330,405],()=>a(7245));module.exports=t})();
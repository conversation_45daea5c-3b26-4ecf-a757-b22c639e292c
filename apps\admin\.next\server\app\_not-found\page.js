(()=>{var e={};e.id=492,e.ids=[492],e.modules={685:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var n=t(8828);function s({children:e}){return(0,n.jsx)("html",{lang:"id",children:(0,n.jsx)("body",{className:"bg-gray-50 dark:bg-gray-900",children:(0,n.jsx)("div",{className:"admin-wrapper",children:e})})})}t(2843),t(1365)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2449:()=>{},2721:()=>{},2843:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3782:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,385,23)),Promise.resolve().then(t.t.bind(t,3737,23)),Promise.resolve().then(t.t.bind(t,6081,23)),Promise.resolve().then(t.t.bind(t,1904,23)),Promise.resolve().then(t.t.bind(t,5856,23)),Promise.resolve().then(t.t.bind(t,5492,23)),Promise.resolve().then(t.t.bind(t,9082,23)),Promise.resolve().then(t.t.bind(t,5812,23))},3873:e=>{"use strict";e.exports=require("path")},4030:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,9355,23)),Promise.resolve().then(t.t.bind(t,4439,23)),Promise.resolve().then(t.t.bind(t,7851,23)),Promise.resolve().then(t.t.bind(t,4730,23)),Promise.resolve().then(t.t.bind(t,9774,23)),Promise.resolve().then(t.t.bind(t,3170,23)),Promise.resolve().then(t.t.bind(t,968,23)),Promise.resolve().then(t.t.bind(t,8298,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9476:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>m,tree:()=>l});var n=t(4332),s=t(8819),o=t(7851),i=t.n(o),d=t(7540),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);t.d(r,a);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,9033,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,2341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],p={require:t,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[191,787],()=>t(9476));module.exports=n})();
(()=>{var e={};e.id=662,e.ids=[662],e.modules={628:(e,t,n)=>{"use strict";let r;n.d(t,{KanbanBoard:()=>th});var l,i,a,o,s,u,d,c,h,f,g=n(3486),p=n(159),v=n.n(p),m=n(2358);let b="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function y(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function w(e){return"nodeType"in e}function x(e){var t,n;return e?y(e)?e:w(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function D(e){let{Document:t}=x(e);return e instanceof t}function E(e){return!y(e)&&e instanceof x(e).HTMLElement}function C(e){return e instanceof x(e).SVGElement}function S(e){return e?y(e)?e.document:w(e)?D(e)?e:E(e)||C(e)?e.ownerDocument:document:document:document}let M=b?p.useLayoutEffect:p.useEffect;function R(e){let t=(0,p.useRef)(e);return M(()=>{t.current=e}),(0,p.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function k(e,t){void 0===t&&(t=[e]);let n=(0,p.useRef)(e);return M(()=>{n.current!==e&&(n.current=e)},t),n}function T(e,t){let n=(0,p.useRef)();return(0,p.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function O(e){let t=R(e),n=(0,p.useRef)(null),r=(0,p.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,r]}function j(e){let t=(0,p.useRef)();return(0,p.useEffect)(()=>{t.current=e},[e]),t.current}let P={};function L(e,t){return(0,p.useMemo)(()=>{if(t)return t;let n=null==P[e]?0:P[e]+1;return P[e]=n,e+"-"+n},[e,t])}function N(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let A=N(1),I=N(-1);function B(e){if(!e)return!1;let{KeyboardEvent:t}=x(e.target);return t&&e instanceof t}function z(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=x(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let K=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[K.Translate.toString(e),K.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),U="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",F={display:"none"};function _(e){let{id:t,value:n}=e;return v().createElement("div",{id:t,style:F},n)}function W(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return v().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let X=(0,p.createContext)(null),q={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},Y={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function H(e){let{announcements:t=Y,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=q}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,p.useState)("");return{announce:(0,p.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=L("DndLiveRegion"),[s,u]=(0,p.useState)(!1);(0,p.useEffect)(()=>{u(!0)},[]);var d=(0,p.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let c=(0,p.useContext)(X);if((0,p.useEffect)(()=>{if(!c)throw Error("useDndMonitor must be used within a children of <DndContext>");return c(d)},[d,c]),!s)return null;let h=v().createElement(v().Fragment,null,v().createElement(_,{id:r,value:l.draggable}),v().createElement(W,{id:o,announcement:a}));return n?(0,m.createPortal)(h,n):h}function G(){}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(l||(l={}));let V=Object.freeze({x:0,y:0});function J(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function Z(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function Q(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let $=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=Q(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=function(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}(Q(r),l);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(J)},ee=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,s=(l-r)*(i-n);return Number((s/(a+o-s)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(Z)};function et(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:V}let en=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1),er={ignoreTransform:!1};function el(e,t){void 0===t&&(t=er);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=x(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,s=e.left-a-(1-l)*parseFloat(n),u=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),d=l?e.width/l:e.width,c=i?e.height/i:e.height;return{width:d,height:c,top:u,right:s+d,bottom:u+c,left:s}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:s}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:s}}function ei(e){return el(e,{ignoreTransform:!0})}function ea(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if(D(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!E(l)||C(l)||n.includes(l))return n;let a=x(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=x(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=x(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function eo(e){let[t]=ea(e,1);return null!=t?t:null}function es(e){return b&&e?y(e)?e:w(e)?D(e)||e===S(e).scrollingElement?window:E(e)?e:null:null:null}function eu(e){return y(e)?e.scrollX:e.scrollLeft}function ed(e){return y(e)?e.scrollY:e.scrollTop}function ec(e){return{x:eu(e),y:ed(e)}}function eh(e){return!!b&&!!e&&e===document.scrollingElement}function ef(e){let t={x:0,y:0},n=eh(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let eg={x:.2,y:.2};function ep(e){return e.reduce((e,t)=>A(e,ec(t)),V)}let ev=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+eu(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+ed(t),0)}]];class em{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=ea(t),r=ep(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,ev))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class eb{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function ey(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function ew(e){e.preventDefault()}function ex(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let eD={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},eE=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class eC{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new eb(S(t)),this.windowListeners=new eb(x(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=el),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);eo(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(V)}handleKeyDown(e){if(B(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=eD,coordinateGetter:i=eE,scrollBehavior:a="smooth"}=r,{code:s}=e;if(l.end.includes(s))return void this.handleEnd(e);if(l.cancel.includes(s))return void this.handleCancel(e);let{collisionRect:u}=n.current,d=u?{x:u.left,y:u.top}:V;this.referenceCoordinates||(this.referenceCoordinates=d);let c=i(e,{active:t,context:n.current,currentCoordinates:d});if(c){let t=I(c,d),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:s,isLeft:u,isBottom:d,maxScroll:h,minScroll:f}=ef(n),g=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(l===o.Right?g.right-g.width/2:g.right,Math.max(l===o.Right?g.left:g.left+g.width/2,c.x)),y:Math.min(l===o.Down?g.bottom-g.height/2:g.bottom,Math.max(l===o.Down?g.top:g.top+g.height/2,c.y))},v=l===o.Right&&!s||l===o.Left&&!u,m=l===o.Down&&!d||l===o.Up&&!i;if(v&&p.x!==c.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=h.x||l===o.Left&&e>=f.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-h.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(m&&p.y!==c.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=h.y||l===o.Up&&e>=f.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-h.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,A(I(c,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eS(e){return!!(e&&"distance"in e)}function eM(e){return!!(e&&"delay"in e)}eC.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=eD,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class eR{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=x(e);return e instanceof t?e:S(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=S(i),this.documentListeners=new eb(this.document),this.listeners=new eb(n),this.windowListeners=new eb(x(i)),this.initialCoordinates=null!=(r=z(l))?r:V,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,ew),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,ew),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(eM(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eS(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,ex,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=z(e))?t:V,s=I(r,o);if(!n&&a){if(eS(a)){if(null!=a.tolerance&&ey(s,a.tolerance))return this.handleCancel();if(ey(s,a.distance))return this.handleStart()}return eM(a)&&ey(s,a.tolerance)?this.handleCancel():void this.handlePending(a,s)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ek={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class eT extends eR{constructor(e){let{event:t}=e;super(e,ek,S(t.target))}}eT.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let eO={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(s||(s={}));class ej extends eR{constructor(e){super(e,eO,S(e.event.target))}}ej.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==s.RightClick&&(null==r||r({event:n}),!0)}}];let eP={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class eL extends eR{constructor(e){super(e,eP)}static setup(){return window.addEventListener(eP.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(eP.move.name,e)};function e(){}}}eL.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(u||(u={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(d||(d={}));let eN={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(c||(c={})),(h||(h={})).Optimized="optimized";let eA=new Map;function eI(e,t){return T(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function eB(e){let{callback:t,disabled:n}=e,r=R(t),l=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,p.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function ez(e){return new em(el(e),e)}function eK(e,t,n){void 0===t&&(t=ez);let[r,l]=(0,p.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=R(t),l=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,p.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=eB({callback:i});return M(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let eU=[];function eF(e,t){void 0===t&&(t=[]);let n=(0,p.useRef)(null);return(0,p.useEffect)(()=>{n.current=null},t),(0,p.useEffect)(()=>{let t=e!==V;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?I(e,n.current):V}function e_(e){return(0,p.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eW=[],eX=[{sensor:eT,options:{}},{sensor:eC,options:{}}],eq={current:{}},eY={draggable:{measure:ei},droppable:{measure:ei,strategy:c.WhileDragging,frequency:h.Optimized},dragOverlay:{measure:el}};class eH extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eG={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eH,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:G},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eY,measureDroppableContainers:G,windowRect:null,measuringScheduled:!1},eV={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:G,draggableNodes:new Map,over:null,measureDroppableContainers:G},eJ=(0,p.createContext)(eV),eZ=(0,p.createContext)(eG);function eQ(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eH}}}function e$(e,t){switch(t.type){case l.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case l.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case l.DragEnd:case l.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case l.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new eH(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case l.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new eH(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case l.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new eH(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function e0(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,p.useContext)(eJ),i=j(r),a=j(null==n?void 0:n.id);return(0,p.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!B(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=e.matches(U)?e:e.querySelector(U);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}let e1=(0,p.createContext)({...V,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(f||(f={}));let e2=(0,p.memo)(function(e){var t,n,r,a,o,s;let{id:h,accessibility:g,autoScroll:y=!0,children:w,sensors:D=eX,collisionDetection:C=ee,measuring:S,modifiers:R,...P}=e,[N,I]=(0,p.useReducer)(e$,void 0,eQ),[B,K]=function(){let[e]=(0,p.useState)(()=>new Set),t=(0,p.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,p.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[U,F]=(0,p.useState)(f.Uninitialized),_=U===f.Initialized,{draggable:{active:W,nodes:q,translate:Y},droppable:{containers:G}}=N,J=null!=W?q.get(W):null,Z=(0,p.useRef)({initial:null,translated:null}),Q=(0,p.useMemo)(()=>{var e;return null!=W?{id:W,data:null!=(e=null==J?void 0:J.data)?e:eq,rect:Z}:null},[W,J]),$=(0,p.useRef)(null),[er,ei]=(0,p.useState)(null),[eu,ed]=(0,p.useState)(null),ev=k(P,Object.values(P)),eb=L("DndDescribedBy",h),ey=(0,p.useMemo)(()=>G.getEnabled(),[G]),ew=(0,p.useMemo)(()=>({draggable:{...eY.draggable,...null==S?void 0:S.draggable},droppable:{...eY.droppable,...null==S?void 0:S.droppable},dragOverlay:{...eY.dragOverlay,...null==S?void 0:S.dragOverlay}}),[null==S?void 0:S.draggable,null==S?void 0:S.droppable,null==S?void 0:S.dragOverlay]),{droppableRects:ex,measureDroppableContainers:eD,measuringScheduled:eE}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,p.useState)(null),{frequency:o,measure:s,strategy:u}=l,d=(0,p.useRef)(e),h=function(){switch(u){case c.Always:return!1;case c.BeforeDragging:return n;default:return!n}}(),f=k(h),g=(0,p.useCallback)(function(e){void 0===e&&(e=[]),f.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[f]),v=(0,p.useRef)(null),m=T(t=>{if(h&&!n)return eA;if(!t||t===eA||d.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new em(s(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,h,s]);return(0,p.useEffect)(()=>{d.current=e},[e]),(0,p.useEffect)(()=>{h||g()},[n,h]),(0,p.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,p.useEffect)(()=>{h||"number"!=typeof o||null!==v.current||(v.current=setTimeout(()=>{g(),v.current=null},o))},[o,h,g,...r]),{droppableRects:m,measureDroppableContainers:g,measuringScheduled:null!=i}}(ey,{dragging:_,dependencies:[Y.x,Y.y],config:ew.droppable}),eC=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return T(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(q,W),eS=(0,p.useMemo)(()=>eu?z(eu):null,[eu]),eM=function(){let e=(null==er?void 0:er.autoScrollEnabled)===!1,t="object"==typeof y?!1===y.enabled:!1===y,n=_&&!e&&!t;return"object"==typeof y?{...y,enabled:n}:{enabled:n}}(),eR=eI(eC,ew.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,p.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;M(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=et(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=eo(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=W?q.get(W):null,config:eM.layoutShiftCompensation,initialRect:eR,measure:ew.draggable.measure});let ek=eK(eC,ew.draggable.measure,eR),eT=eK(eC?eC.parentElement:null),eO=(0,p.useRef)({activatorEvent:null,active:null,activeNode:eC,collisionRect:null,collisions:null,droppableRects:ex,draggableNodes:q,draggingNode:null,draggingNodeRect:null,droppableContainers:G,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ej=G.getNodeFor(null==(t=eO.current.over)?void 0:t.id),eP=function(e){let{measure:t}=e,[n,r]=(0,p.useState)(null),l=eB({callback:(0,p.useCallback)(e=>{for(let{target:n}of e)if(E(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),[i,a]=O((0,p.useCallback)(e=>{let n=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return E(t)?t:e}(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]));return(0,p.useMemo)(()=>({nodeRef:i,rect:n,setRef:a}),[n,i,a])}({measure:ew.dragOverlay.measure}),eL=null!=(n=eP.nodeRef.current)?n:eC,ez=_?null!=(r=eP.rect)?r:ek:null,eH=!!(eP.nodeRef.current&&eP.rect),eG=function(e){let t=eI(e);return et(e,t)}(eH?null:ek),eV=e_(eL?x(eL):null),e2=function(e){let t=(0,p.useRef)(e),n=T(n=>e?n&&n!==eU&&e&&t.current&&e.parentNode===t.current.parentNode?n:ea(e):eU,[e]);return(0,p.useEffect)(()=>{t.current=e},[e]),n}(_?null!=ej?ej:eC:null),e9=function(e,t){void 0===t&&(t=el);let[n]=e,r=e_(n?x(n):null),[l,i]=(0,p.useState)(eW);function a(){i(()=>e.length?e.map(e=>eh(e)?r:new em(t(e),e)):eW)}let o=eB({callback:a});return M(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(e2),e5=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}(R,{transform:{x:Y.x-eG.x,y:Y.y-eG.y,scaleX:1,scaleY:1},activatorEvent:eu,active:Q,activeNodeRect:ek,containerNodeRect:eT,draggingNodeRect:ez,over:eO.current.over,overlayNodeRect:eP.rect,scrollableAncestors:e2,scrollableAncestorRects:e9,windowRect:eV}),e6=eS?A(eS,Y):null,e3=function(e){let[t,n]=(0,p.useState)(null),r=(0,p.useRef)(e),l=(0,p.useCallback)(e=>{let t=es(e.target);t&&n(e=>e?(e.set(t,ec(t)),new Map(e)):null)},[]);return(0,p.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=es(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,ec(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=es(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,p.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>A(e,t),V):ep(e):V,[e,t])}(e2),e8=eF(e3),e4=eF(e3,[ek]),e7=A(e5,e8),te=ez?en(ez,e5):null,tt=Q&&te?C({active:Q,collisionRect:te,droppableRects:ex,droppableContainers:ey,pointerCoordinates:e6}):null,tn=function(e,t){if(!e||0===e.length)return null;let[n]=e;return n.id}(tt,"id"),[tr,tl]=(0,p.useState)(null),ti=(o=eH?e5:A(e5,e4),s=null!=(a=null==tr?void 0:tr.rect)?a:null,{...o,scaleX:s&&ek?s.width/ek.width:1,scaleY:s&&ek?s.height/ek.height:1}),ta=(0,p.useRef)(null),to=(0,p.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==$.current)return;let i=q.get($.current);if(!i)return;let a=e.nativeEvent,o=new n({active:$.current,activeNode:i,event:a,options:r,context:eO,onAbort(e){if(!q.get(e))return;let{onDragAbort:t}=ev.current,n={id:e};null==t||t(n),B({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!q.get(e))return;let{onDragPending:l}=ev.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),B({type:"onDragPending",event:i})},onStart(e){let t=$.current;if(null==t)return;let n=q.get(t);if(!n)return;let{onDragStart:r}=ev.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:Z}};(0,m.unstable_batchedUpdates)(()=>{null==r||r(i),F(f.Initializing),I({type:l.DragStart,initialCoordinates:e,active:t}),B({type:"onDragStart",event:i}),ei(ta.current),ed(a)})},onMove(e){I({type:l.DragMove,coordinates:e})},onEnd:s(l.DragEnd),onCancel:s(l.DragCancel)});function s(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=eO.current,o=null;if(t&&i){let{cancelDrop:s}=ev.current;o={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===l.DragEnd&&"function"==typeof s&&await Promise.resolve(s(o))&&(e=l.DragCancel)}$.current=null,(0,m.unstable_batchedUpdates)(()=>{I({type:e}),F(f.Uninitialized),tl(null),ei(null),ed(null),ta.current=null;let t=e===l.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=ev.current[t];null==e||e(o),B({type:t,event:o})}})}}ta.current=o},[q]),ts=(0,p.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=q.get(r);null!==$.current||!i||l.dndKit||l.defaultPrevented||!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},$.current=r,to(n,t))},[q,to]),tu=(0,p.useMemo)(()=>D.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:ts(e.handler,t)}))]},[]),[D,ts]);(0,p.useEffect)(()=>{if(!b)return;let e=D.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},D.map(e=>{let{sensor:t}=e;return t})),M(()=>{ek&&U===f.Initializing&&F(f.Initialized)},[ek,U]),(0,p.useEffect)(()=>{let{onDragMove:e}=ev.current,{active:t,activatorEvent:n,collisions:r,over:l}=eO.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e7.x,y:e7.y},over:l};(0,m.unstable_batchedUpdates)(()=>{null==e||e(i),B({type:"onDragMove",event:i})})},[e7.x,e7.y]),(0,p.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=eO.current;if(!e||null==$.current||!t||!l)return;let{onDragOver:i}=ev.current,a=r.get(tn),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,s={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,m.unstable_batchedUpdates)(()=>{tl(o),null==i||i(s),B({type:"onDragOver",event:s})})},[tn]),M(()=>{eO.current={activatorEvent:eu,active:Q,activeNode:eC,collisionRect:te,collisions:tt,droppableRects:ex,draggableNodes:q,draggingNode:eL,draggingNodeRect:ez,droppableContainers:G,over:tr,scrollableAncestors:e2,scrollAdjustedTranslate:e7},Z.current={initial:ez,translated:te}},[Q,eC,tt,te,q,eL,ez,ex,G,tr,e2,e7]),function(e){let{acceleration:t,activator:n=u.Pointer,canScroll:r,draggingRect:l,enabled:a,interval:o=5,order:s=d.TreeOrder,pointerCoordinates:c,scrollableAncestors:h,scrollableAncestorRects:f,delta:g,threshold:v}=e,m=function(e){let{delta:t,disabled:n}=e,r=j(t);return T(e=>{if(n||!r||!e)return eN;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===l.x,[i.Forward]:e.x[i.Forward]||1===l.x},y:{[i.Backward]:e.y[i.Backward]||-1===l.y,[i.Forward]:e.y[i.Forward]||1===l.y}}},[n,t,r])}({delta:g,disabled:!a}),[b,y]=function(){let e=(0,p.useRef)(null);return[(0,p.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,p.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}(),w=(0,p.useRef)({x:0,y:0}),x=(0,p.useRef)({x:0,y:0}),D=(0,p.useMemo)(()=>{switch(n){case u.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case u.DraggableRect:return l}},[n,l,c]),E=(0,p.useRef)(null),C=(0,p.useCallback)(()=>{let e=E.current;if(!e)return;let t=w.current.x*x.current.x,n=w.current.y*x.current.y;e.scrollBy(t,n)},[]),S=(0,p.useMemo)(()=>s===d.TreeOrder?[...h].reverse():h,[s,h]);(0,p.useEffect)(()=>{if(!a||!h.length||!D)return void y();for(let e of S){if((null==r?void 0:r(e))===!1)continue;let n=f[h.indexOf(e)];if(!n)continue;let{direction:l,speed:a}=function(e,t,n,r,l){let{top:a,left:o,right:s,bottom:u}=n;void 0===r&&(r=10),void 0===l&&(l=eg);let{isTop:d,isBottom:c,isLeft:h,isRight:f}=ef(e),g={x:0,y:0},p={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!d&&a<=t.top+v.height?(g.y=i.Backward,p.y=r*Math.abs((t.top+v.height-a)/v.height)):!c&&u>=t.bottom-v.height&&(g.y=i.Forward,p.y=r*Math.abs((t.bottom-v.height-u)/v.height)),!f&&s>=t.right-v.width?(g.x=i.Forward,p.x=r*Math.abs((t.right-v.width-s)/v.width)):!h&&o<=t.left+v.width&&(g.x=i.Backward,p.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:g,speed:p}}(e,n,D,t,v);for(let e of["x","y"])m[e][l[e]]||(a[e]=0,l[e]=0);if(a.x>0||a.y>0){y(),E.current=e,b(C,o),w.current=a,x.current=l;return}}w.current={x:0,y:0},x.current={x:0,y:0},y()},[t,C,r,y,a,o,JSON.stringify(D),JSON.stringify(m),b,h,S,f,JSON.stringify(v)])}({...eM,delta:Y,draggingRect:te,pointerCoordinates:e6,scrollableAncestors:e2,scrollableAncestorRects:e9});let td=(0,p.useMemo)(()=>({active:Q,activeNode:eC,activeNodeRect:ek,activatorEvent:eu,collisions:tt,containerNodeRect:eT,dragOverlay:eP,draggableNodes:q,droppableContainers:G,droppableRects:ex,over:tr,measureDroppableContainers:eD,scrollableAncestors:e2,scrollableAncestorRects:e9,measuringConfiguration:ew,measuringScheduled:eE,windowRect:eV}),[Q,eC,ek,eu,tt,eT,eP,q,G,ex,tr,eD,e2,e9,ew,eE,eV]),tc=(0,p.useMemo)(()=>({activatorEvent:eu,activators:tu,active:Q,activeNodeRect:ek,ariaDescribedById:{draggable:eb},dispatch:I,draggableNodes:q,over:tr,measureDroppableContainers:eD}),[eu,tu,Q,ek,I,eb,q,tr,eD]);return v().createElement(X.Provider,{value:K},v().createElement(eJ.Provider,{value:tc},v().createElement(eZ.Provider,{value:td},v().createElement(e1.Provider,{value:ti},w)),v().createElement(e0,{disabled:(null==g?void 0:g.restoreFocus)===!1})),v().createElement(H,{...g,hiddenTextDescribedById:eb}))}),e9=(0,p.createContext)(null),e5="button",e6={timeout:25};r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}};function e3(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function e8(e){return null!==e&&e>=0}let e4=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=e3(t,r,n),a=t[l],o=i[l];return o&&a?{x:o.left-a.left,y:o.top-a.top,scaleX:o.width/a.width,scaleY:o.height/a.height}:null},e7="Sortable",te=v().createContext({activeIndex:-1,containerId:e7,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:e4,disabled:{draggable:!1,droppable:!1}});function tt(e){let{children:t,id:n,items:r,strategy:l=e4,disabled:i=!1}=e,{active:a,dragOverlay:o,droppableRects:s,over:u,measureDroppableContainers:d}=(0,p.useContext)(eZ),c=L(e7,n),h=null!==o.rect,f=(0,p.useMemo)(()=>r.map(e=>"object"==typeof e&&"id"in e?e.id:e),[r]),g=null!=a,m=a?f.indexOf(a.id):-1,b=u?f.indexOf(u.id):-1,y=(0,p.useRef)(f),w=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(f,y.current),x=-1!==b&&-1===m||w,D="boolean"==typeof i?{draggable:i,droppable:i}:i;M(()=>{w&&g&&d(f)},[w,f,g,d]),(0,p.useEffect)(()=>{y.current=f},[f]);let E=(0,p.useMemo)(()=>({activeIndex:m,containerId:c,disabled:D,disableTransforms:x,items:f,overIndex:b,useDragOverlay:h,sortedRects:f.reduce((e,t,n)=>{let r=s.get(t);return r&&(e[n]=r),e},Array(f.length)),strategy:l}),[m,c,D.draggable,D.droppable,x,f,b,s,h,l]);return v().createElement(te.Provider,{value:E},t)}let tn=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return e3(n,r,l).indexOf(t)},tr=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:s,transition:u}=e;return!!u&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===s)},tl={duration:200,easing:"ease"},ti="transform",ta=K.Transition.toString({property:ti,duration:0,easing:"linear"}),to={roleDescription:"sortable"};o.Down,o.Right,o.Up,o.Left;var ts=n(4665);let tu=[{id:"todo",title:"To Do",tasks:[{id:"1",title:"Desain Halaman Utama",priority:"High",assignee:"A"}]},{id:"inprogress",title:"In Progress",tasks:[{id:"2",title:"Kembangkan API Auth",priority:"High",assignee:"B"}]},{id:"review",title:"Review",tasks:[]},{id:"done",title:"Done",tasks:[{id:"3",title:"Setup Proyek Next.js",priority:"Medium",assignee:"C"}]}];function td({task:e}){let{attributes:t,listeners:n,setNodeRef:r,transform:i,transition:a}=function(e){var t,n,r,i;let{animateLayoutChanges:a=tr,attributes:o,disabled:s,data:u,getNewIndex:d=tn,id:c,strategy:h,resizeObserverConfig:f,transition:g=tl}=e,{items:v,containerId:m,activeIndex:b,disabled:y,disableTransforms:w,sortedRects:x,overIndex:D,useDragOverlay:E,strategy:C}=(0,p.useContext)(te),S=(t=s,n=y,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(r=null==t?void 0:t.draggable)?r:n.draggable,droppable:null!=(i=null==t?void 0:t.droppable)?i:n.droppable}),R=v.indexOf(c),T=(0,p.useMemo)(()=>({sortable:{containerId:m,index:R,items:v},...u}),[m,u,R,v]),j=(0,p.useMemo)(()=>v.slice(v.indexOf(c)),[v,c]),{rect:P,node:N,isOver:A,setNodeRef:I}=function(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=L("Droppable"),{active:o,dispatch:s,over:u,measureDroppableContainers:d}=(0,p.useContext)(eJ),c=(0,p.useRef)({disabled:n}),h=(0,p.useRef)(!1),f=(0,p.useRef)(null),g=(0,p.useRef)(null),{disabled:v,updateMeasurementsFor:m,timeout:b}={...e6,...i},y=k(null!=m?m:r),w=eB({callback:(0,p.useCallback)(()=>{if(!h.current){h.current=!0;return}null!=g.current&&clearTimeout(g.current),g.current=setTimeout(()=>{d(Array.isArray(y.current)?y.current:[y.current]),g.current=null},b)},[b]),disabled:v||!o}),[x,D]=O((0,p.useCallback)((e,t)=>{w&&(t&&(w.unobserve(t),h.current=!1),e&&w.observe(e))},[w])),E=k(t);return(0,p.useEffect)(()=>{w&&x.current&&(w.disconnect(),h.current=!1,w.observe(x.current))},[x,w]),(0,p.useEffect)(()=>(s({type:l.RegisterDroppable,element:{id:r,key:a,disabled:n,node:x,rect:f,data:E}}),()=>s({type:l.UnregisterDroppable,key:a,id:r})),[r]),(0,p.useEffect)(()=>{n!==c.current.disabled&&(s({type:l.SetDroppableDisabled,id:r,key:a,disabled:n}),c.current.disabled=n)},[r,a,n,s]),{active:o,rect:f,isOver:(null==u?void 0:u.id)===r,node:x,over:u,setNodeRef:D}}({id:c,data:T,disabled:S.droppable,resizeObserverConfig:{updateMeasurementsFor:j,...f}}),{active:z,activatorEvent:U,activeNodeRect:F,attributes:_,setNodeRef:W,listeners:X,isDragging:q,over:Y,setActivatorNodeRef:H,transform:G}=function(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=L("Draggable"),{activators:a,activatorEvent:o,active:s,activeNodeRect:u,ariaDescribedById:d,draggableNodes:c,over:h}=(0,p.useContext)(eJ),{role:f=e5,roleDescription:g="draggable",tabIndex:v=0}=null!=l?l:{},m=(null==s?void 0:s.id)===t,b=(0,p.useContext)(m?e1:e9),[y,w]=O(),[x,D]=O(),E=(0,p.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),C=k(n);return M(()=>(c.set(t,{id:t,key:i,node:y,activatorNode:x,data:C}),()=>{let e=c.get(t);e&&e.key===i&&c.delete(t)}),[c,t]),{active:s,activatorEvent:o,activeNodeRect:u,attributes:(0,p.useMemo)(()=>({role:f,tabIndex:v,"aria-disabled":r,"aria-pressed":!!m&&f===e5||void 0,"aria-roledescription":g,"aria-describedby":d.draggable}),[r,f,v,m,g,d.draggable]),isDragging:m,listeners:r?void 0:E,node:y,over:h,setNodeRef:w,setActivatorNodeRef:D,transform:b}}({id:c,data:T,attributes:{...to,...o},disabled:S.draggable}),V=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,p.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}(I,W),J=!!z,Z=J&&!w&&e8(b)&&e8(D),Q=!E&&q,$=Q&&Z?G:null,ee=Z?null!=$?$:(null!=h?h:C)({rects:x,activeNodeRect:F,activeIndex:b,overIndex:D,index:R}):null,et=e8(b)&&e8(D)?d({id:c,items:v,activeIndex:b,overIndex:D}):R,en=null==z?void 0:z.id,er=(0,p.useRef)({activeId:en,items:v,newIndex:et,containerId:m}),ei=v!==er.current.items,ea=a({active:z,containerId:m,isDragging:q,isSorting:J,id:c,index:R,items:v,newIndex:er.current.newIndex,previousItems:er.current.items,previousContainerId:er.current.containerId,transition:g,wasDragging:null!=er.current.activeId}),eo=function(e){let{disabled:t,index:n,node:r,rect:l}=e,[i,a]=(0,p.useState)(null),o=(0,p.useRef)(n);return M(()=>{if(!t&&n!==o.current&&r.current){let e=l.current;if(e){let t=el(r.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&a(n)}}n!==o.current&&(o.current=n)},[t,n,r,l]),(0,p.useEffect)(()=>{i&&a(null)},[i]),i}({disabled:!ea,index:R,node:N,rect:P});return(0,p.useEffect)(()=>{J&&er.current.newIndex!==et&&(er.current.newIndex=et),m!==er.current.containerId&&(er.current.containerId=m),v!==er.current.items&&(er.current.items=v)},[J,et,m,v]),(0,p.useEffect)(()=>{if(en===er.current.activeId)return;if(en&&!er.current.activeId){er.current.activeId=en;return}let e=setTimeout(()=>{er.current.activeId=en},50);return()=>clearTimeout(e)},[en]),{active:z,activeIndex:b,attributes:_,data:T,rect:P,index:R,newIndex:et,items:v,isOver:A,isSorting:J,isDragging:q,listeners:X,node:N,overIndex:D,over:Y,setNodeRef:V,setActivatorNodeRef:H,setDroppableNodeRef:I,setDraggableNodeRef:W,transform:null!=eo?eo:ee,transition:eo||ei&&er.current.newIndex===R?ta:(!Q||B(U))&&g&&(J||ea)?K.Transition.toString({...g,property:ti}):void 0}}({id:e.id}),o={transform:K.Transform.toString(i),transition:a};return(0,g.jsx)("div",{ref:r,style:o,...t,...n,children:(0,g.jsx)(ts.Zp,{className:"mb-2",children:(0,g.jsxs)(ts.Wu,{className:"p-3",children:[(0,g.jsx)("p",{children:e.title}),(0,g.jsxs)("div",{className:"flex items-center justify-between mt-2 text-xs",children:[(0,g.jsx)("span",{children:e.priority}),(0,g.jsx)("span",{className:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center",children:e.assignee})]})]})})})}function tc({column:e}){return(0,g.jsx)("div",{className:"w-72 flex-shrink-0",children:(0,g.jsxs)(ts.Zp,{className:"bg-gray-100 dark:bg-gray-800",children:[(0,g.jsx)(ts.aR,{children:(0,g.jsx)(ts.ZB,{children:e.title})}),(0,g.jsx)(ts.Wu,{children:(0,g.jsx)(tt,{items:e.tasks.map(e=>e.id),children:e.tasks.map(e=>(0,g.jsx)(td,{task:e},e.id))})})]})})}function th(){let[e,t]=(0,p.useState)(tu);return(0,g.jsx)(e2,{collisionDetection:$,onDragEnd:function(e){let{active:t,over:n}=e;n&&t.id!==n.id&&console.log("Task moved",{active:t,over:n})},children:(0,g.jsx)("div",{className:"flex gap-4 overflow-x-auto p-1",children:e.map(e=>(0,g.jsx)(tc,{column:e},e.id))})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1376:(e,t,n)=>{Promise.resolve().then(n.bind(n,628))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6670:(e,t,n)=>{"use strict";n.d(t,{KanbanBoard:()=>r});let r=(0,n(3952).registerClientReference)(function(){throw Error("Attempted to call KanbanBoard() from the server but KanbanBoard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\components\\kanban-board.tsx","KanbanBoard")},8995:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>h,tree:()=>u});var r=n(4332),l=n(8819),i=n(7851),a=n.n(i),o=n(7540),s={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>o[e]);n.d(t,s);let u={children:["",{children:["dashboard",{children:["projects",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,9946)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\projects\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,2341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\dashboard\\projects\\[id]\\page.tsx"],c={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/dashboard/projects/[id]/page",pathname:"/dashboard/projects/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9675:(e,t,n)=>{Promise.resolve().then(n.bind(n,6670))},9699:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l});var r=n(1253);let l=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},9946:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a,generateStaticParams:()=>i});var r=n(8828),l=n(6670);async function i(){return[{id:"1"},{id:"2"},{id:"3"}]}async function a({params:e}){let{id:t}=await e;return(0,r.jsx)(l.KanbanBoard,{})}n(1365)}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[191,77,253,405],()=>n(8995));module.exports=r})();
(()=>{var e={};e.id=974,e.ids=[974],e.modules={187:(e,r,o)=>{"use strict";o.r(r),o.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=o(4332),n=o(8819),s=o(7851),a=o.n(s),i=o(7540),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);o.d(r,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,9707)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(o.bind(o,685)),"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,2341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\dashboard\\src\\app\\page.tsx"],p={require:o,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5623:(e,r,o)=>{Promise.resolve().then(o.bind(o,2660)),Promise.resolve().then(o.bind(o,479))},5733:(e,r,o)=>{"use strict";o.r(r),o.d(r,{Label:()=>t});let t=(0,o(3952).registerClientReference)(function(){throw Error("Attempted to call Label() from the server but Label is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\label.tsx","Label")},7230:(e,r,o)=>{"use strict";o.r(r),o.d(r,{DropdownMenu:()=>n,DropdownMenuCheckboxItem:()=>l,DropdownMenuContent:()=>a,DropdownMenuGroup:()=>p,DropdownMenuItem:()=>i,DropdownMenuLabel:()=>c,DropdownMenuPortal:()=>u,DropdownMenuRadioGroup:()=>v,DropdownMenuRadioItem:()=>d,DropdownMenuSeparator:()=>m,DropdownMenuShortcut:()=>f,DropdownMenuSub:()=>b,DropdownMenuSubContent:()=>g,DropdownMenuSubTrigger:()=>h,DropdownMenuTrigger:()=>s});var t=o(3952);let n=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenu() from the server but DropdownMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenu"),s=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuTrigger() from the server but DropdownMenuTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuTrigger"),a=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuContent() from the server but DropdownMenuContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuContent"),i=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuItem() from the server but DropdownMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuItem"),l=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuCheckboxItem() from the server but DropdownMenuCheckboxItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuCheckboxItem"),d=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuRadioItem() from the server but DropdownMenuRadioItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuRadioItem"),c=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuLabel() from the server but DropdownMenuLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuLabel"),p=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuGroup() from the server but DropdownMenuGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuGroup"),u=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuPortal() from the server but DropdownMenuPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuPortal"),m=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuSeparator() from the server but DropdownMenuSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuSeparator"),f=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuShortcut() from the server but DropdownMenuShortcut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuShortcut"),b=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuSub() from the server but DropdownMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuSub"),g=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuSubContent() from the server but DropdownMenuSubContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuSubContent"),h=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuSubTrigger() from the server but DropdownMenuSubTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuSubTrigger"),v=(0,t.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuRadioGroup() from the server but DropdownMenuRadioGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\packages\\ui\\components\\dropdown-menu.tsx","DropdownMenuRadioGroup")},7375:(e,r,o)=>{Promise.resolve().then(o.bind(o,7230)),Promise.resolve().then(o.bind(o,5733))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9699:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>n});var t=o(1253);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},9707:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>eu});var t=o(8828),n=o(1365);function s(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var a=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:o,...t}=e;if(n.isValidElement(o)){var a;let e,i,l=(a=o,(i=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(i=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,r){let o={...r};for(let t in r){let n=e[t],s=r[t];/^on[A-Z]/.test(t)?n&&s?o[t]=(...e)=>{let r=s(...e);return n(...e),r}:n&&(o[t]=n):"style"===t?o[t]={...n,...s}:"className"===t&&(o[t]=[n,s].filter(Boolean).join(" "))}return{...e,...o}}(t,o.props);return o.type!==n.Fragment&&(d.ref=r?function(...e){return r=>{let o=!1,t=e.map(e=>{let t=s(e,r);return o||"function"!=typeof t||(o=!0),t});if(o)return()=>{for(let r=0;r<t.length;r++){let o=t[r];"function"==typeof o?o():s(e[r],null)}}}}(r,l):l),n.cloneElement(o,d)}return n.Children.count(o)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),o=n.forwardRef((e,o)=>{let{children:s,...a}=e,i=n.Children.toArray(s),d=i.find(l);if(d){let e=d.props.children,s=i.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,t.jsx)(r,{...a,ref:o,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,t.jsx)(r,{...a,ref:o,children:s})});return o.displayName=`${e}.Slot`,o}("Slot"),i=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}function d(){for(var e,r,o=0,t="",n=arguments.length;o<n;o++)(e=arguments[o])&&(r=function e(r){var o,t,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var s=r.length;for(o=0;o<s;o++)r[o]&&(t=e(r[o]))&&(n&&(n+=" "),n+=t)}else for(t in r)r[t]&&(n&&(n+=" "),n+=t);return n}(e))&&(t&&(t+=" "),t+=r);return t}let c=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,p=e=>{let r=b(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:t}=e;return{getClassGroupId:e=>{let o=e.split("-");return""===o[0]&&1!==o.length&&o.shift(),u(o,r)||f(e)},getConflictingClassGroupIds:(e,r)=>{let n=o[e]||[];return r&&t[e]?[...n,...t[e]]:n}}},u=(e,r)=>{if(0===e.length)return r.classGroupId;let o=e[0],t=r.nextPart.get(o),n=t?u(e.slice(1),t):void 0;if(n)return n;if(0===r.validators.length)return;let s=e.join("-");return r.validators.find(({validator:e})=>e(s))?.classGroupId},m=/^\[(.+)\]$/,f=e=>{if(m.test(e)){let r=m.exec(e)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}},b=e=>{let{theme:r,prefix:o}=e,t={nextPart:new Map,validators:[]};return x(Object.entries(e.classGroups),o).forEach(([e,o])=>{g(o,t,e,r)}),t},g=(e,r,o,t)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:h(r,e)).classGroupId=o;return}if("function"==typeof e)return v(e)?void g(e(t),r,o,t):void r.validators.push({validator:e,classGroupId:o});Object.entries(e).forEach(([e,n])=>{g(n,h(r,e),o,t)})})},h=(e,r)=>{let o=e;return r.split("-").forEach(e=>{o.nextPart.has(e)||o.nextPart.set(e,{nextPart:new Map,validators:[]}),o=o.nextPart.get(e)}),o},v=e=>e.isThemeGetter,x=(e,r)=>r?e.map(([e,o])=>[e,o.map(e=>"string"==typeof e?r+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,o])=>[r+e,o])):e)]):e,w=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,o=new Map,t=new Map,n=(n,s)=>{o.set(n,s),++r>e&&(r=0,t=o,o=new Map)};return{get(e){let r=o.get(e);return void 0!==r?r:void 0!==(r=t.get(e))?(n(e,r),r):void 0},set(e,r){o.has(e)?o.set(e,r):n(e,r)}}},y=e=>{let{separator:r,experimentalParseClassName:o}=e,t=1===r.length,n=r[0],s=r.length,a=e=>{let o,a=[],i=0,l=0;for(let d=0;d<e.length;d++){let c=e[d];if(0===i){if(c===n&&(t||e.slice(d,d+s)===r)){a.push(e.slice(l,d)),l=d+s;continue}if("/"===c){o=d;continue}}"["===c?i++:"]"===c&&i--}let d=0===a.length?e:e.substring(l),c=d.startsWith("!"),p=c?d.substring(1):d;return{modifiers:a,hasImportantModifier:c,baseClassName:p,maybePostfixModifierPosition:o&&o>l?o-l:void 0}};return o?e=>o({className:e,parseClassName:a}):a},C=e=>{if(e.length<=1)return e;let r=[],o=[];return e.forEach(e=>{"["===e[0]?(r.push(...o.sort(),e),o=[]):o.push(e)}),r.push(...o.sort()),r},k=e=>({cache:w(e.cacheSize),parseClassName:y(e),...p(e)}),j=/\s+/,D=(e,r)=>{let{parseClassName:o,getClassGroupId:t,getConflictingClassGroupIds:n}=r,s=[],a=e.trim().split(j),i="";for(let e=a.length-1;e>=0;e-=1){let r=a[e],{modifiers:l,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:p}=o(r),u=!!p,m=t(u?c.substring(0,p):c);if(!m){if(!u||!(m=t(c))){i=r+(i.length>0?" "+i:i);continue}u=!1}let f=C(l).join(":"),b=d?f+"!":f,g=b+m;if(s.includes(g))continue;s.push(g);let h=n(m,u);for(let e=0;e<h.length;++e){let r=h[e];s.push(b+r)}i=r+(i.length>0?" "+i:i)}return i};function M(){let e,r,o=0,t="";for(;o<arguments.length;)(e=arguments[o++])&&(r=z(e))&&(t&&(t+=" "),t+=r);return t}let z=e=>{let r;if("string"==typeof e)return e;let o="";for(let t=0;t<e.length;t++)e[t]&&(r=z(e[t]))&&(o&&(o+=" "),o+=r);return o},N=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},R=/^\[(?:([a-z-]+):)?(.+)\]$/i,P=/^\d+\/\d+$/,S=new Set(["px","full","screen"]),I=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,E=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,A=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,T=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,G=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,_=e=>O(e)||S.has(e)||P.test(e),U=e=>Q(e,"length",Y),O=e=>!!e&&!Number.isNaN(Number(e)),q=e=>Q(e,"number",O),$=e=>!!e&&Number.isInteger(Number(e)),L=e=>e.endsWith("%")&&O(e.slice(0,-1)),W=e=>R.test(e),V=e=>I.test(e),B=new Set(["length","size","percentage"]),F=e=>Q(e,B,ee),H=e=>Q(e,"position",ee),J=new Set(["image","url"]),K=e=>Q(e,J,eo),X=e=>Q(e,"",er),Z=()=>!0,Q=(e,r,o)=>{let t=R.exec(e);return!!t&&(t[1]?"string"==typeof r?t[1]===r:r.has(t[1]):o(t[2]))},Y=e=>E.test(e)&&!A.test(e),ee=()=>!1,er=e=>T.test(e),eo=e=>G.test(e);Symbol.toStringTag;let et=function(e,...r){let o,t,n,s=function(i){return t=(o=k(r.reduce((e,r)=>r(e),e()))).cache.get,n=o.cache.set,s=a,a(i)};function a(e){let r=t(e);if(r)return r;let s=D(e,o);return n(e,s),s}return function(){return s(M.apply(null,arguments))}}(()=>{let e=N("colors"),r=N("spacing"),o=N("blur"),t=N("brightness"),n=N("borderColor"),s=N("borderRadius"),a=N("borderSpacing"),i=N("borderWidth"),l=N("contrast"),d=N("grayscale"),c=N("hueRotate"),p=N("invert"),u=N("gap"),m=N("gradientColorStops"),f=N("gradientColorStopPositions"),b=N("inset"),g=N("margin"),h=N("opacity"),v=N("padding"),x=N("saturate"),w=N("scale"),y=N("sepia"),C=N("skew"),k=N("space"),j=N("translate"),D=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],z=()=>["auto",W,r],R=()=>[W,r],P=()=>["",_,U],S=()=>["auto",O,W],I=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],E=()=>["solid","dashed","dotted","double","none"],A=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],T=()=>["start","end","center","between","around","evenly","stretch"],G=()=>["","0",W],B=()=>["auto","avoid","all","avoid-page","page","left","right","column"],J=()=>[O,W];return{cacheSize:500,separator:":",theme:{colors:[Z],spacing:[_,U],blur:["none","",V,W],brightness:J(),borderColor:[e],borderRadius:["none","","full",V,W],borderSpacing:R(),borderWidth:P(),contrast:J(),grayscale:G(),hueRotate:J(),invert:G(),gap:R(),gradientColorStops:[e],gradientColorStopPositions:[L,U],inset:z(),margin:z(),opacity:J(),padding:R(),saturate:J(),scale:J(),sepia:G(),skew:J(),space:R(),translate:R()},classGroups:{aspect:[{aspect:["auto","square","video",W]}],container:["container"],columns:[{columns:[V]}],"break-after":[{"break-after":B()}],"break-before":[{"break-before":B()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...I(),W]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:D()}],"overscroll-x":[{"overscroll-x":D()}],"overscroll-y":[{"overscroll-y":D()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[b]}],"inset-x":[{"inset-x":[b]}],"inset-y":[{"inset-y":[b]}],start:[{start:[b]}],end:[{end:[b]}],top:[{top:[b]}],right:[{right:[b]}],bottom:[{bottom:[b]}],left:[{left:[b]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",$,W]}],basis:[{basis:z()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",W]}],grow:[{grow:G()}],shrink:[{shrink:G()}],order:[{order:["first","last","none",$,W]}],"grid-cols":[{"grid-cols":[Z]}],"col-start-end":[{col:["auto",{span:["full",$,W]},W]}],"col-start":[{"col-start":S()}],"col-end":[{"col-end":S()}],"grid-rows":[{"grid-rows":[Z]}],"row-start-end":[{row:["auto",{span:[$,W]},W]}],"row-start":[{"row-start":S()}],"row-end":[{"row-end":S()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",W]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",W]}],gap:[{gap:[u]}],"gap-x":[{"gap-x":[u]}],"gap-y":[{"gap-y":[u]}],"justify-content":[{justify:["normal",...T()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...T(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...T(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",W,r]}],"min-w":[{"min-w":[W,r,"min","max","fit"]}],"max-w":[{"max-w":[W,r,"none","full","min","max","fit","prose",{screen:[V]},V]}],h:[{h:[W,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[W,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[W,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[W,r,"auto","min","max","fit"]}],"font-size":[{text:["base",V,U]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",q]}],"font-family":[{font:[Z]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",W]}],"line-clamp":[{"line-clamp":["none",O,q]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",_,W]}],"list-image":[{"list-image":["none",W]}],"list-style-type":[{list:["none","disc","decimal",W]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...E(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",_,U]}],"underline-offset":[{"underline-offset":["auto",_,W]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",W]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",W]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...I(),H]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",F]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},K]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...E(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:E()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...E()]}],"outline-offset":[{"outline-offset":[_,W]}],"outline-w":[{outline:[_,U]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:P()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[_,U]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",V,X]}],"shadow-color":[{shadow:[Z]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...A(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":A()}],filter:[{filter:["","none"]}],blur:[{blur:[o]}],brightness:[{brightness:[t]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",V,W]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[p]}],saturate:[{saturate:[x]}],sepia:[{sepia:[y]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[o]}],"backdrop-brightness":[{"backdrop-brightness":[t]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[y]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",W]}],duration:[{duration:J()}],ease:[{ease:["linear","in","out","in-out",W]}],delay:[{delay:J()}],animate:[{animate:["none","spin","ping","pulse","bounce",W]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[$,W]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",W]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",W]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",W]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[_,U,q]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function en(...e){return et(d(e))}let es=((e,r)=>o=>{var t;if((null==r?void 0:r.variants)==null)return d(e,null==o?void 0:o.class,null==o?void 0:o.className);let{variants:n,defaultVariants:s}=r,a=Object.keys(n).map(e=>{let r=null==o?void 0:o[e],t=null==s?void 0:s[e];if(null===r)return null;let a=c(r)||c(t);return n[e][a]}),i=o&&Object.entries(o).reduce((e,r)=>{let[o,t]=r;return void 0===t||(e[o]=t),e},{});return d(e,a,null==r||null==(t=r.compoundVariants)?void 0:t.reduce((e,r)=>{let{class:o,className:t,...n}=r;return Object.entries(n).every(e=>{let[r,o]=e;return Array.isArray(o)?o.includes({...s,...i}[r]):({...s,...i})[r]===o})?[...e,o,t]:e},[]),null==o?void 0:o.class,null==o?void 0:o.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});n.forwardRef(({className:e,variant:r,size:o,asChild:n=!1,...s},i)=>(0,t.jsx)(n?a:"button",{className:en(es({variant:r,size:o,className:e})),ref:i,...s})).displayName="Button",n.forwardRef(({className:e,type:r,...o},n)=>(0,t.jsx)("input",{type:r,className:en("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...o})).displayName="Input",o(5733);let ea=n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:en("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));ea.displayName="Card";let ei=n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:en("flex flex-col space-y-1.5 p-6",e),...r}));ei.displayName="CardHeader";let el=n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("h3",{ref:o,className:en("text-2xl font-semibold leading-none tracking-tight",e),...r}));el.displayName="CardTitle",n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("p",{ref:o,className:en("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let ed=n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:en("p-6 pt-0",e),...r}));ed.displayName="CardContent",n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:en("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter",o(7230),n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:o,className:en("w-full caption-bottom text-sm",e),...r})})).displayName="Table",n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("thead",{ref:o,className:en("[&_tr]:border-b",e),...r})).displayName="TableHeader",n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("tbody",{ref:o,className:en("[&_tr:last-child]:border-0",e),...r})).displayName="TableBody",n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("tfoot",{ref:o,className:en("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...r})).displayName="TableFooter",n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("tr",{ref:o,className:en("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...r})).displayName="TableRow",n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("th",{ref:o,className:en("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...r})).displayName="TableHead",n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("td",{ref:o,className:en("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...r})).displayName="TableCell",n.forwardRef(({className:e,...r},o)=>(0,t.jsx)("caption",{ref:o,className:en("mt-4 text-sm text-muted-foreground",e),...r})).displayName="TableCaption";let ec=[{title:"Proyek Aktif",value:"12",change:"+2",changeType:"increase"},{title:"Tugas Selesai",value:"86",change:"+10",changeType:"increase"},{title:"Akan Jatuh Tempo",value:"8",change:"-1",changeType:"decrease"},{title:"Aktivitas Tim",value:"32",change:"+5",changeType:"increase"}];function ep(){return(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:ec.map(e=>(0,t.jsxs)(ea,{children:[(0,t.jsx)(ei,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(el,{className:"text-sm font-medium",children:e.title})}),(0,t.jsxs)(ed,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:e.value}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.change," dari minggu lalu"]})]})]},e.title))})}function eu(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(ep,{}),(0,t.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-7",children:[(0,t.jsxs)("div",{className:"col-span-4 bg-white dark:bg-gray-900 p-4 rounded-lg shadow",children:[(0,t.jsx)("h3",{className:"font-semibold",children:"Grafik Aktivitas Tim"}),(0,t.jsx)("div",{className:"h-80 mt-4 bg-gray-200 dark:bg-gray-800 rounded-md flex items-center justify-center",children:"[Placeholder untuk Grafik]"})]}),(0,t.jsxs)("div",{className:"col-span-3 bg-white dark:bg-gray-900 p-4 rounded-lg shadow",children:[(0,t.jsx)("h3",{className:"font-semibold",children:"Proyek Terbaru"}),(0,t.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,t.jsx)("div",{children:"Proyek A"}),(0,t.jsx)("div",{children:"Proyek B"}),(0,t.jsx)("div",{children:"Proyek C"})]})]})]})]})}}};var r=require("../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[191,77,253,405],()=>o(187));module.exports=t})();
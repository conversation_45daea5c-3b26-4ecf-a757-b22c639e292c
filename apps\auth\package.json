{"name": "auth", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "react-hook-form": "^7.52.0", "@hookform/resolvers": "^3.9.0", "zod": "^3.23.8", "@monetizr/ui": "*", "@monetizr/db": "*", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3"}, "devDependencies": {"@monetizr/config": "0.1.0", "tsconfig": "0.1.0", "typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/jsonwebtoken": "^9.0.6", "@types/bcryptjs": "^2.4.6", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}
(()=>{var e={};e.id=612,e.ids=[612],e.modules={408:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3252:(e,t,a)=>{"use strict";a.d(t,{Er:()=>d,wU:()=>l,QQ:()=>o,BE:()=>u}),a(424),a(5208);let r=`
-- Users table
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  password_hash TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('creator', 'promoter', 'admin')),
  bio TEXT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Bank accounts table
CREATE TABLE IF NOT EXISTS bank_accounts (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  bank_name TEXT NOT NULL,
  account_holder_name TEXT NOT NULL,
  account_number TEXT NOT NULL,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Campaigns table
CREATE TABLE IF NOT EXISTS campaigns (
  id TEXT PRIMARY KEY,
  creator_id TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  budget REAL NOT NULL,
  price_per_view REAL NOT NULL,
  requirements TEXT,
  material_url TEXT,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed')),
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  expires_at TEXT,
  FOREIGN KEY (creator_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Promotions table
CREATE TABLE IF NOT EXISTS promotions (
  id TEXT PRIMARY KEY,
  campaign_id TEXT NOT NULL,
  promoter_id TEXT NOT NULL,
  tracking_link TEXT UNIQUE NOT NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'rejected')),
  views_count INTEGER NOT NULL DEFAULT 0,
  earnings REAL NOT NULL DEFAULT 0,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (campaign_id) REFERENCES campaigns (id) ON DELETE CASCADE,
  FOREIGN KEY (promoter_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'earning', 'payment')),
  amount REAL NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
  description TEXT NOT NULL,
  reference_id TEXT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Wallets table
CREATE TABLE IF NOT EXISTS wallets (
  id TEXT PRIMARY KEY,
  user_id TEXT UNIQUE NOT NULL,
  balance REAL NOT NULL DEFAULT 0,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users (email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users (role);
CREATE INDEX IF NOT EXISTS idx_campaigns_creator ON campaigns (creator_id);
CREATE INDEX IF NOT EXISTS idx_campaigns_status ON campaigns (status);
CREATE INDEX IF NOT EXISTS idx_promotions_campaign ON promotions (campaign_id);
CREATE INDEX IF NOT EXISTS idx_promotions_promoter ON promotions (promoter_id);
CREATE INDEX IF NOT EXISTS idx_transactions_user ON transactions (user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions (type);
`;function s(){return new Date().toISOString()}class i{constructor(){this.data=new Map,this.data.set("users",[]),this.data.set("bank_accounts",[]),this.data.set("campaigns",[]),this.data.set("promotions",[]),this.data.set("transactions",[]),this.data.set("wallets",[])}prepare(e){return{bind:(...t)=>({first:async()=>e.includes("SELECT")&&e.includes("users")&&e.includes("email")&&(this.data.get("users")||[]).find(e=>e.email===t[0])||null,all:async()=>e.includes("SELECT")&&e.includes("users")?{results:this.data.get("users")||[]}:{results:[]},run:async()=>{if(e.includes("INSERT INTO users")){let e=this.data.get("users")||[],a={id:t[0],email:t[1],name:t[2],password_hash:t[3],role:t[4],bio:t[5]||null,created_at:s(),updated_at:s(),is_active:!0};return e.push(a),this.data.set("users",e),{success:!0,meta:{changes:1}}}return{success:!0,meta:{changes:0}}}})}}async exec(e){return{success:!0}}}let n=new i;async function d(e){return btoa(e)}async function u(e,t){return btoa(e)===t}let o={async create(e){let t=crypto.randomUUID(),a=s(),r={...e,id:t,created_at:a,updated_at:a};return await n.prepare(`
      INSERT INTO users (id, email, name, password_hash, role, bio, created_at, updated_at, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(r.id,r.email,r.name,r.password_hash,r.role,r.bio,r.created_at,r.updated_at,r.is_active).run(),r},findByEmail:async e=>await n.prepare(`
      SELECT * FROM users WHERE email = ? AND is_active = TRUE
    `).bind(e).first(),findById:async e=>await n.prepare(`
      SELECT * FROM users WHERE id = ? AND is_active = TRUE
    `).bind(e).first(),async update(e,t){let a=Object.keys(t).map(e=>`${e} = ?`).join(", "),r=Object.values(t);return r.push(s()),r.push(e),(await n.prepare(`
      UPDATE users SET ${a}, updated_at = ? WHERE id = ?
    `).bind(...r).run()).success}};async function l(){try{await n.exec(r),console.log("Database schema initialized successfully")}catch(e){throw console.error("Failed to initialize database schema:",e),e}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3946:(e,t,a)=>{"use strict";let r;a.r(t),a.d(t,{patchFetch:()=>eK,routeModule:()=>eD,serverHooks:()=>ez,workAsyncStorage:()=>e$,workUnitAsyncStorage:()=>eX});var s,i,n,d,u={};a.r(u),a.d(u,{POST:()=>eM});var o=a(8106),l=a(8819),c=a(2050),h=a(4235),p=a(3252);!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let a of e)t[a]=a;return t},e.getValidEnumValues=t=>{let a=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),r={};for(let e of a)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.push(a);return t},e.find=(e,t)=>{for(let a of e)if(t(a))return a},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(s||(s={})),(i||(i={})).mergeShapes=(e,t)=>({...e,...t});let m=s.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),f=e=>{switch(typeof e){case"undefined":return m.undefined;case"string":return m.string;case"number":return Number.isNaN(e)?m.nan:m.number;case"boolean":return m.boolean;case"function":return m.function;case"bigint":return m.bigint;case"symbol":return m.symbol;case"object":if(Array.isArray(e))return m.array;if(null===e)return m.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return m.promise;if("undefined"!=typeof Map&&e instanceof Map)return m.map;if("undefined"!=typeof Set&&e instanceof Set)return m.set;if("undefined"!=typeof Date&&e instanceof Date)return m.date;return m.object;default:return m.unknown}},_=s.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class y extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},a={_errors:[]},r=e=>{for(let s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(r);else if("invalid_return_type"===s.code)r(s.returnTypeError);else if("invalid_arguments"===s.code)r(s.argumentsError);else if(0===s.path.length)a._errors.push(t(s));else{let e=a,r=0;for(;r<s.path.length;){let a=s.path[r];r===s.path.length-1?(e[a]=e[a]||{_errors:[]},e[a]._errors.push(t(s))):e[a]=e[a]||{_errors:[]},e=e[a],r++}}};return r(this),a}static assert(e){if(!(e instanceof y))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,s.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},a=[];for(let r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):a.push(e(r));return{formErrors:a,fieldErrors:t}}get formErrors(){return this.flatten()}}y.create=e=>new y(e);let g=(e,t)=>{let a;switch(e.code){case _.invalid_type:a=e.received===m.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case _.invalid_literal:a=`Invalid literal value, expected ${JSON.stringify(e.expected,s.jsonStringifyReplacer)}`;break;case _.unrecognized_keys:a=`Unrecognized key(s) in object: ${s.joinValues(e.keys,", ")}`;break;case _.invalid_union:a="Invalid input";break;case _.invalid_union_discriminator:a=`Invalid discriminator value. Expected ${s.joinValues(e.options)}`;break;case _.invalid_enum_value:a=`Invalid enum value. Expected ${s.joinValues(e.options)}, received '${e.received}'`;break;case _.invalid_arguments:a="Invalid function arguments";break;case _.invalid_return_type:a="Invalid function return type";break;case _.invalid_date:a="Invalid date";break;case _.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(a=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(a=`${a} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?a=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?a=`Invalid input: must end with "${e.validation.endsWith}"`:s.assertNever(e.validation):a="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case _.too_small:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case _.too_big:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case _.custom:a="Invalid input";break;case _.invalid_intersection_types:a="Intersection results could not be merged";break;case _.not_multiple_of:a=`Number must be a multiple of ${e.multipleOf}`;break;case _.not_finite:a="Number must be finite";break;default:a=t.defaultError,s.assertNever(e)}return{message:a}},v=e=>{let{data:t,path:a,errorMaps:r,issueData:s}=e,i=[...a,...s.path||[]],n={...s,path:i};if(void 0!==s.message)return{...s,path:i,message:s.message};let d="";for(let e of r.filter(e=>!!e).slice().reverse())d=e(n,{data:t,defaultError:d}).message;return{...s,path:i,message:d}};function T(e,t){let a=v({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,g,g==g?void 0:g].filter(e=>!!e)});e.common.issues.push(a)}class E{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let a=[];for(let r of t){if("aborted"===r.status)return k;"dirty"===r.status&&e.dirty(),a.push(r.value)}return{status:e.value,value:a}}static async mergeObjectAsync(e,t){let a=[];for(let e of t){let t=await e.key,r=await e.value;a.push({key:t,value:r})}return E.mergeObjectSync(e,a)}static mergeObjectSync(e,t){let a={};for(let r of t){let{key:t,value:s}=r;if("aborted"===t.status||"aborted"===s.status)return k;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==s.value||r.alwaysSet)&&(a[t.value]=s.value)}return{status:e.value,value:a}}}let k=Object.freeze({status:"aborted"}),x=e=>({status:"dirty",value:e}),b=e=>({status:"valid",value:e}),N=e=>"aborted"===e.status,w=e=>"dirty"===e.status,O=e=>"valid"===e.status,A=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(n||(n={}));class C{constructor(e,t,a,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=a,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let S=(e,t)=>{if(O(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new y(e.common.issues);return this._error=t,this._error}}};function R(e){if(!e)return{};let{errorMap:t,invalid_type_error:a,required_error:r,description:s}=e;if(t&&(a||r))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:s}:{errorMap:(t,s)=>{let{message:i}=e;return"invalid_enum_value"===t.code?{message:i??s.defaultError}:void 0===s.data?{message:i??r??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:i??a??s.defaultError}},description:s}}class L{get description(){return this._def.description}_getType(e){return f(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:f(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new E,ctx:{common:e.parent.common,data:e.data,parsedType:f(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(A(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let a=this.safeParse(e,t);if(a.success)return a.data;throw a.error}safeParse(e,t){let a={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:f(e)},r=this._parseSync({data:e,path:a.path,parent:a});return S(a,r)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:f(e)};if(!this["~standard"].async)try{let a=this._parseSync({data:e,path:[],parent:t});return O(a)?{value:a.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>O(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let a=await this.safeParseAsync(e,t);if(a.success)return a.data;throw a.error}async safeParseAsync(e,t){let a={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:f(e)},r=this._parse({data:e,path:a.path,parent:a});return S(a,await (A(r)?r:Promise.resolve(r)))}refine(e,t){let a=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{let s=e(t),i=()=>r.addIssue({code:_.custom,...a(t)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then(e=>!!e||(i(),!1)):!!s||(i(),!1)})}refinement(e,t){return this._refinement((a,r)=>!!e(a)||(r.addIssue("function"==typeof t?t(a,r):t),!1))}_refinement(e){return new eN({schema:this,typeName:d.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ew.create(this,this._def)}nullable(){return eO.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return eu.create(this)}promise(){return eb.create(this,this._def)}or(e){return el.create([this,e],this._def)}and(e){return ep.create(this,e,this._def)}transform(e){return new eN({...R(this._def),schema:this,typeName:d.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eA({...R(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:d.ZodDefault})}brand(){return new eR({typeName:d.ZodBranded,type:this,...R(this._def)})}catch(e){return new eC({...R(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:d.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eL.create(this,e)}readonly(){return eI.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let I=/^c[^\s-]{8,}$/i,Z=/^[0-9a-z]+$/,j=/^[0-9A-HJKMNP-TV-Z]{26}$/i,U=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,F=/^[a-z0-9_-]{21}$/i,P=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,M=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,D=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,$=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,X=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,z=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,K=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,V=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,B=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,q="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",W=RegExp(`^${q}$`);function Y(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let a=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${a}`}class H extends L{_parse(e){var t,a,i,n;let d;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==m.string){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.string,received:t.parsedType}),k}let u=new E;for(let o of this._def.checks)if("min"===o.kind)e.data.length<o.value&&(T(d=this._getOrReturnCtx(e,d),{code:_.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),u.dirty());else if("max"===o.kind)e.data.length>o.value&&(T(d=this._getOrReturnCtx(e,d),{code:_.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),u.dirty());else if("length"===o.kind){let t=e.data.length>o.value,a=e.data.length<o.value;(t||a)&&(d=this._getOrReturnCtx(e,d),t?T(d,{code:_.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}):a&&T(d,{code:_.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}),u.dirty())}else if("email"===o.kind)D.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"email",code:_.invalid_string,message:o.message}),u.dirty());else if("emoji"===o.kind)r||(r=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),r.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"emoji",code:_.invalid_string,message:o.message}),u.dirty());else if("uuid"===o.kind)U.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"uuid",code:_.invalid_string,message:o.message}),u.dirty());else if("nanoid"===o.kind)F.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"nanoid",code:_.invalid_string,message:o.message}),u.dirty());else if("cuid"===o.kind)I.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"cuid",code:_.invalid_string,message:o.message}),u.dirty());else if("cuid2"===o.kind)Z.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"cuid2",code:_.invalid_string,message:o.message}),u.dirty());else if("ulid"===o.kind)j.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"ulid",code:_.invalid_string,message:o.message}),u.dirty());else if("url"===o.kind)try{new URL(e.data)}catch{T(d=this._getOrReturnCtx(e,d),{validation:"url",code:_.invalid_string,message:o.message}),u.dirty()}else"regex"===o.kind?(o.regex.lastIndex=0,o.regex.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"regex",code:_.invalid_string,message:o.message}),u.dirty())):"trim"===o.kind?e.data=e.data.trim():"includes"===o.kind?e.data.includes(o.value,o.position)||(T(d=this._getOrReturnCtx(e,d),{code:_.invalid_string,validation:{includes:o.value,position:o.position},message:o.message}),u.dirty()):"toLowerCase"===o.kind?e.data=e.data.toLowerCase():"toUpperCase"===o.kind?e.data=e.data.toUpperCase():"startsWith"===o.kind?e.data.startsWith(o.value)||(T(d=this._getOrReturnCtx(e,d),{code:_.invalid_string,validation:{startsWith:o.value},message:o.message}),u.dirty()):"endsWith"===o.kind?e.data.endsWith(o.value)||(T(d=this._getOrReturnCtx(e,d),{code:_.invalid_string,validation:{endsWith:o.value},message:o.message}),u.dirty()):"datetime"===o.kind?(function(e){let t=`${q}T${Y(e)}`,a=[];return a.push(e.local?"Z?":"Z"),e.offset&&a.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${a.join("|")})`,RegExp(`^${t}$`)})(o).test(e.data)||(T(d=this._getOrReturnCtx(e,d),{code:_.invalid_string,validation:"datetime",message:o.message}),u.dirty()):"date"===o.kind?W.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{code:_.invalid_string,validation:"date",message:o.message}),u.dirty()):"time"===o.kind?RegExp(`^${Y(o)}$`).test(e.data)||(T(d=this._getOrReturnCtx(e,d),{code:_.invalid_string,validation:"time",message:o.message}),u.dirty()):"duration"===o.kind?M.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"duration",code:_.invalid_string,message:o.message}),u.dirty()):"ip"===o.kind?(t=e.data,!(("v4"===(a=o.version)||!a)&&$.test(t)||("v6"===a||!a)&&z.test(t))&&1&&(T(d=this._getOrReturnCtx(e,d),{validation:"ip",code:_.invalid_string,message:o.message}),u.dirty())):"jwt"===o.kind?!function(e,t){if(!P.test(e))return!1;try{let[a]=e.split("."),r=a.replace(/-/g,"+").replace(/_/g,"/").padEnd(a.length+(4-a.length%4)%4,"="),s=JSON.parse(atob(r));if("object"!=typeof s||null===s||"typ"in s&&s?.typ!=="JWT"||!s.alg||t&&s.alg!==t)return!1;return!0}catch{return!1}}(e.data,o.alg)&&(T(d=this._getOrReturnCtx(e,d),{validation:"jwt",code:_.invalid_string,message:o.message}),u.dirty()):"cidr"===o.kind?(i=e.data,!(("v4"===(n=o.version)||!n)&&X.test(i)||("v6"===n||!n)&&K.test(i))&&1&&(T(d=this._getOrReturnCtx(e,d),{validation:"cidr",code:_.invalid_string,message:o.message}),u.dirty())):"base64"===o.kind?V.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"base64",code:_.invalid_string,message:o.message}),u.dirty()):"base64url"===o.kind?B.test(e.data)||(T(d=this._getOrReturnCtx(e,d),{validation:"base64url",code:_.invalid_string,message:o.message}),u.dirty()):s.assertNever(o);return{status:u.value,value:e.data}}_regex(e,t,a){return this.refinement(t=>e.test(t),{validation:t,code:_.invalid_string,...n.errToObj(a)})}_addCheck(e){return new H({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...n.errToObj(e)})}url(e){return this._addCheck({kind:"url",...n.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...n.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...n.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...n.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...n.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...n.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...n.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...n.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...n.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...n.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...n.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...n.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...n.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...n.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...n.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...n.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...n.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...n.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...n.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...n.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...n.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...n.errToObj(t)})}nonempty(e){return this.min(1,n.errToObj(e))}trim(){return new H({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new H({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new H({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}H.create=e=>new H({checks:[],typeName:d.ZodString,coerce:e?.coerce??!1,...R(e)});class G extends L{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==m.number){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.number,received:t.parsedType}),k}let a=new E;for(let r of this._def.checks)"int"===r.kind?s.isInteger(e.data)||(T(t=this._getOrReturnCtx(e,t),{code:_.invalid_type,expected:"integer",received:"float",message:r.message}),a.dirty()):"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(T(t=this._getOrReturnCtx(e,t),{code:_.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),a.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(T(t=this._getOrReturnCtx(e,t),{code:_.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),a.dirty()):"multipleOf"===r.kind?0!==function(e,t){let a=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,s=a>r?a:r;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}(e.data,r.value)&&(T(t=this._getOrReturnCtx(e,t),{code:_.not_multiple_of,multipleOf:r.value,message:r.message}),a.dirty()):"finite"===r.kind?Number.isFinite(e.data)||(T(t=this._getOrReturnCtx(e,t),{code:_.not_finite,message:r.message}),a.dirty()):s.assertNever(r);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,a,r){return new G({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:n.toString(r)}]})}_addCheck(e){return new G({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:n.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:n.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:n.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:n.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&s.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let a of this._def.checks)if("finite"===a.kind||"int"===a.kind||"multipleOf"===a.kind)return!0;else"min"===a.kind?(null===t||a.value>t)&&(t=a.value):"max"===a.kind&&(null===e||a.value<e)&&(e=a.value);return Number.isFinite(t)&&Number.isFinite(e)}}G.create=e=>new G({checks:[],typeName:d.ZodNumber,coerce:e?.coerce||!1,...R(e)});class Q extends L{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==m.bigint)return this._getInvalidInput(e);let a=new E;for(let r of this._def.checks)"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(T(t=this._getOrReturnCtx(e,t),{code:_.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),a.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(T(t=this._getOrReturnCtx(e,t),{code:_.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),a.dirty()):"multipleOf"===r.kind?e.data%r.value!==BigInt(0)&&(T(t=this._getOrReturnCtx(e,t),{code:_.not_multiple_of,multipleOf:r.value,message:r.message}),a.dirty()):s.assertNever(r);return{status:a.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.bigint,received:t.parsedType}),k}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,a,r){return new Q({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:n.toString(r)}]})}_addCheck(e){return new Q({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}Q.create=e=>new Q({checks:[],typeName:d.ZodBigInt,coerce:e?.coerce??!1,...R(e)});class J extends L{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==m.boolean){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.boolean,received:t.parsedType}),k}return b(e.data)}}J.create=e=>new J({typeName:d.ZodBoolean,coerce:e?.coerce||!1,...R(e)});class ee extends L{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==m.date){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.date,received:t.parsedType}),k}if(Number.isNaN(e.data.getTime()))return T(this._getOrReturnCtx(e),{code:_.invalid_date}),k;let a=new E;for(let r of this._def.checks)"min"===r.kind?e.data.getTime()<r.value&&(T(t=this._getOrReturnCtx(e,t),{code:_.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),a.dirty()):"max"===r.kind?e.data.getTime()>r.value&&(T(t=this._getOrReturnCtx(e,t),{code:_.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),a.dirty()):s.assertNever(r);return{status:a.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ee({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:n.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:n.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ee.create=e=>new ee({checks:[],coerce:e?.coerce||!1,typeName:d.ZodDate,...R(e)});class et extends L{_parse(e){if(this._getType(e)!==m.symbol){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.symbol,received:t.parsedType}),k}return b(e.data)}}et.create=e=>new et({typeName:d.ZodSymbol,...R(e)});class ea extends L{_parse(e){if(this._getType(e)!==m.undefined){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.undefined,received:t.parsedType}),k}return b(e.data)}}ea.create=e=>new ea({typeName:d.ZodUndefined,...R(e)});class er extends L{_parse(e){if(this._getType(e)!==m.null){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.null,received:t.parsedType}),k}return b(e.data)}}er.create=e=>new er({typeName:d.ZodNull,...R(e)});class es extends L{constructor(){super(...arguments),this._any=!0}_parse(e){return b(e.data)}}es.create=e=>new es({typeName:d.ZodAny,...R(e)});class ei extends L{constructor(){super(...arguments),this._unknown=!0}_parse(e){return b(e.data)}}ei.create=e=>new ei({typeName:d.ZodUnknown,...R(e)});class en extends L{_parse(e){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.never,received:t.parsedType}),k}}en.create=e=>new en({typeName:d.ZodNever,...R(e)});class ed extends L{_parse(e){if(this._getType(e)!==m.undefined){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.void,received:t.parsedType}),k}return b(e.data)}}ed.create=e=>new ed({typeName:d.ZodVoid,...R(e)});class eu extends L{_parse(e){let{ctx:t,status:a}=this._processInputParams(e),r=this._def;if(t.parsedType!==m.array)return T(t,{code:_.invalid_type,expected:m.array,received:t.parsedType}),k;if(null!==r.exactLength){let e=t.data.length>r.exactLength.value,s=t.data.length<r.exactLength.value;(e||s)&&(T(t,{code:e?_.too_big:_.too_small,minimum:s?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),a.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(T(t,{code:_.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),a.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(T(t,{code:_.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),a.dirty()),t.common.async)return Promise.all([...t.data].map((e,a)=>r.type._parseAsync(new C(t,e,t.path,a)))).then(e=>E.mergeArray(a,e));let s=[...t.data].map((e,a)=>r.type._parseSync(new C(t,e,t.path,a)));return E.mergeArray(a,s)}get element(){return this._def.type}min(e,t){return new eu({...this._def,minLength:{value:e,message:n.toString(t)}})}max(e,t){return new eu({...this._def,maxLength:{value:e,message:n.toString(t)}})}length(e,t){return new eu({...this._def,exactLength:{value:e,message:n.toString(t)}})}nonempty(e){return this.min(1,e)}}eu.create=(e,t)=>new eu({type:e,minLength:null,maxLength:null,exactLength:null,typeName:d.ZodArray,...R(t)});class eo extends L{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=s.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==m.object){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.object,received:t.parsedType}),k}let{status:t,ctx:a}=this._processInputParams(e),{shape:r,keys:s}=this._getCached(),i=[];if(!(this._def.catchall instanceof en&&"strip"===this._def.unknownKeys))for(let e in a.data)s.includes(e)||i.push(e);let n=[];for(let e of s){let t=r[e],s=a.data[e];n.push({key:{status:"valid",value:e},value:t._parse(new C(a,s,a.path,e)),alwaysSet:e in a.data})}if(this._def.catchall instanceof en){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of i)n.push({key:{status:"valid",value:e},value:{status:"valid",value:a.data[e]}});else if("strict"===e)i.length>0&&(T(a,{code:_.unrecognized_keys,keys:i}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of i){let r=a.data[t];n.push({key:{status:"valid",value:t},value:e._parse(new C(a,r,a.path,t)),alwaysSet:t in a.data})}}return a.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of n){let a=await t.key,r=await t.value;e.push({key:a,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>E.mergeObjectSync(t,e)):E.mergeObjectSync(t,n)}get shape(){return this._def.shape()}strict(e){return n.errToObj,new eo({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,a)=>{let r=this._def.errorMap?.(t,a).message??a.defaultError;return"unrecognized_keys"===t.code?{message:n.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new eo({...this._def,unknownKeys:"strip"})}passthrough(){return new eo({...this._def,unknownKeys:"passthrough"})}extend(e){return new eo({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new eo({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:d.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new eo({...this._def,catchall:e})}pick(e){let t={};for(let a of s.objectKeys(e))e[a]&&this.shape[a]&&(t[a]=this.shape[a]);return new eo({...this._def,shape:()=>t})}omit(e){let t={};for(let a of s.objectKeys(this.shape))e[a]||(t[a]=this.shape[a]);return new eo({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof eo){let a={};for(let r in t.shape){let s=t.shape[r];a[r]=ew.create(e(s))}return new eo({...t._def,shape:()=>a})}if(t instanceof eu)return new eu({...t._def,type:e(t.element)});if(t instanceof ew)return ew.create(e(t.unwrap()));if(t instanceof eO)return eO.create(e(t.unwrap()));if(t instanceof em)return em.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let a of s.objectKeys(this.shape)){let r=this.shape[a];e&&!e[a]?t[a]=r:t[a]=r.optional()}return new eo({...this._def,shape:()=>t})}required(e){let t={};for(let a of s.objectKeys(this.shape))if(e&&!e[a])t[a]=this.shape[a];else{let e=this.shape[a];for(;e instanceof ew;)e=e._def.innerType;t[a]=e}return new eo({...this._def,shape:()=>t})}keyof(){return eE(s.objectKeys(this.shape))}}eo.create=(e,t)=>new eo({shape:()=>e,unknownKeys:"strip",catchall:en.create(),typeName:d.ZodObject,...R(t)}),eo.strictCreate=(e,t)=>new eo({shape:()=>e,unknownKeys:"strict",catchall:en.create(),typeName:d.ZodObject,...R(t)}),eo.lazycreate=(e,t)=>new eo({shape:e,unknownKeys:"strip",catchall:en.create(),typeName:d.ZodObject,...R(t)});class el extends L{_parse(e){let{ctx:t}=this._processInputParams(e),a=this._def.options;if(t.common.async)return Promise.all(a.map(async e=>{let a={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;let a=e.map(e=>new y(e.ctx.common.issues));return T(t,{code:_.invalid_union,unionErrors:a}),k});{let e,r=[];for(let s of a){let a={...t,common:{...t.common,issues:[]},parent:null},i=s._parseSync({data:t.data,path:t.path,parent:a});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:a}),a.common.issues.length&&r.push(a.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let s=r.map(e=>new y(e));return T(t,{code:_.invalid_union,unionErrors:s}),k}}get options(){return this._def.options}}el.create=(e,t)=>new el({options:e,typeName:d.ZodUnion,...R(t)});let ec=e=>{if(e instanceof ev)return ec(e.schema);if(e instanceof eN)return ec(e.innerType());if(e instanceof eT)return[e.value];if(e instanceof ek)return e.options;if(e instanceof ex)return s.objectValues(e.enum);else if(e instanceof eA)return ec(e._def.innerType);else if(e instanceof ea)return[void 0];else if(e instanceof er)return[null];else if(e instanceof ew)return[void 0,...ec(e.unwrap())];else if(e instanceof eO)return[null,...ec(e.unwrap())];else if(e instanceof eR)return ec(e.unwrap());else if(e instanceof eI)return ec(e.unwrap());else if(e instanceof eC)return ec(e._def.innerType);else return[]};class eh extends L{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==m.object)return T(t,{code:_.invalid_type,expected:m.object,received:t.parsedType}),k;let a=this.discriminator,r=t.data[a],s=this.optionsMap.get(r);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(T(t,{code:_.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[a]}),k)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,a){let r=new Map;for(let a of t){let t=ec(a.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let s of t){if(r.has(s))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);r.set(s,a)}}return new eh({typeName:d.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...R(a)})}}class ep extends L{_parse(e){let{status:t,ctx:a}=this._processInputParams(e),r=(e,r)=>{if(N(e)||N(r))return k;let i=function e(t,a){let r=f(t),i=f(a);if(t===a)return{valid:!0,data:t};if(r===m.object&&i===m.object){let r=s.objectKeys(a),i=s.objectKeys(t).filter(e=>-1!==r.indexOf(e)),n={...t,...a};for(let r of i){let s=e(t[r],a[r]);if(!s.valid)return{valid:!1};n[r]=s.data}return{valid:!0,data:n}}if(r===m.array&&i===m.array){if(t.length!==a.length)return{valid:!1};let r=[];for(let s=0;s<t.length;s++){let i=e(t[s],a[s]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}if(r===m.date&&i===m.date&&+t==+a)return{valid:!0,data:t};return{valid:!1}}(e.value,r.value);return i.valid?((w(e)||w(r))&&t.dirty(),{status:t.value,value:i.data}):(T(a,{code:_.invalid_intersection_types}),k)};return a.common.async?Promise.all([this._def.left._parseAsync({data:a.data,path:a.path,parent:a}),this._def.right._parseAsync({data:a.data,path:a.path,parent:a})]).then(([e,t])=>r(e,t)):r(this._def.left._parseSync({data:a.data,path:a.path,parent:a}),this._def.right._parseSync({data:a.data,path:a.path,parent:a}))}}ep.create=(e,t,a)=>new ep({left:e,right:t,typeName:d.ZodIntersection,...R(a)});class em extends L{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==m.array)return T(a,{code:_.invalid_type,expected:m.array,received:a.parsedType}),k;if(a.data.length<this._def.items.length)return T(a,{code:_.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),k;!this._def.rest&&a.data.length>this._def.items.length&&(T(a,{code:_.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let r=[...a.data].map((e,t)=>{let r=this._def.items[t]||this._def.rest;return r?r._parse(new C(a,e,a.path,t)):null}).filter(e=>!!e);return a.common.async?Promise.all(r).then(e=>E.mergeArray(t,e)):E.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new em({...this._def,rest:e})}}em.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new em({items:e,typeName:d.ZodTuple,rest:null,...R(t)})};class ef extends L{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==m.object)return T(a,{code:_.invalid_type,expected:m.object,received:a.parsedType}),k;let r=[],s=this._def.keyType,i=this._def.valueType;for(let e in a.data)r.push({key:s._parse(new C(a,e,a.path,e)),value:i._parse(new C(a,a.data[e],a.path,e)),alwaysSet:e in a.data});return a.common.async?E.mergeObjectAsync(t,r):E.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,a){return new ef(t instanceof L?{keyType:e,valueType:t,typeName:d.ZodRecord,...R(a)}:{keyType:H.create(),valueType:e,typeName:d.ZodRecord,...R(t)})}}class e_ extends L{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==m.map)return T(a,{code:_.invalid_type,expected:m.map,received:a.parsedType}),k;let r=this._def.keyType,s=this._def.valueType,i=[...a.data.entries()].map(([e,t],i)=>({key:r._parse(new C(a,e,a.path,[i,"key"])),value:s._parse(new C(a,t,a.path,[i,"value"]))}));if(a.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let a of i){let r=await a.key,s=await a.value;if("aborted"===r.status||"aborted"===s.status)return k;("dirty"===r.status||"dirty"===s.status)&&t.dirty(),e.set(r.value,s.value)}return{status:t.value,value:e}})}{let e=new Map;for(let a of i){let r=a.key,s=a.value;if("aborted"===r.status||"aborted"===s.status)return k;("dirty"===r.status||"dirty"===s.status)&&t.dirty(),e.set(r.value,s.value)}return{status:t.value,value:e}}}}e_.create=(e,t,a)=>new e_({valueType:t,keyType:e,typeName:d.ZodMap,...R(a)});class ey extends L{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==m.set)return T(a,{code:_.invalid_type,expected:m.set,received:a.parsedType}),k;let r=this._def;null!==r.minSize&&a.data.size<r.minSize.value&&(T(a,{code:_.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&a.data.size>r.maxSize.value&&(T(a,{code:_.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());let s=this._def.valueType;function i(e){let a=new Set;for(let r of e){if("aborted"===r.status)return k;"dirty"===r.status&&t.dirty(),a.add(r.value)}return{status:t.value,value:a}}let n=[...a.data.values()].map((e,t)=>s._parse(new C(a,e,a.path,t)));return a.common.async?Promise.all(n).then(e=>i(e)):i(n)}min(e,t){return new ey({...this._def,minSize:{value:e,message:n.toString(t)}})}max(e,t){return new ey({...this._def,maxSize:{value:e,message:n.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ey.create=(e,t)=>new ey({valueType:e,minSize:null,maxSize:null,typeName:d.ZodSet,...R(t)});class eg extends L{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==m.function)return T(t,{code:_.invalid_type,expected:m.function,received:t.parsedType}),k;function a(e,a){return v({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,g,g].filter(e=>!!e),issueData:{code:_.invalid_arguments,argumentsError:a}})}function r(e,a){return v({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,g,g].filter(e=>!!e),issueData:{code:_.invalid_return_type,returnTypeError:a}})}let s={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof eb){let e=this;return b(async function(...t){let n=new y([]),d=await e._def.args.parseAsync(t,s).catch(e=>{throw n.addIssue(a(t,e)),n}),u=await Reflect.apply(i,this,d);return await e._def.returns._def.type.parseAsync(u,s).catch(e=>{throw n.addIssue(r(u,e)),n})})}{let e=this;return b(function(...t){let n=e._def.args.safeParse(t,s);if(!n.success)throw new y([a(t,n.error)]);let d=Reflect.apply(i,this,n.data),u=e._def.returns.safeParse(d,s);if(!u.success)throw new y([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new eg({...this._def,args:em.create(e).rest(ei.create())})}returns(e){return new eg({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,a){return new eg({args:e||em.create([]).rest(ei.create()),returns:t||ei.create(),typeName:d.ZodFunction,...R(a)})}}class ev extends L{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ev.create=(e,t)=>new ev({getter:e,typeName:d.ZodLazy,...R(t)});class eT extends L{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return T(t,{received:t.data,code:_.invalid_literal,expected:this._def.value}),k}return{status:"valid",value:e.data}}get value(){return this._def.value}}function eE(e,t){return new ek({values:e,typeName:d.ZodEnum,...R(t)})}eT.create=(e,t)=>new eT({value:e,typeName:d.ZodLiteral,...R(t)});class ek extends L{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),a=this._def.values;return T(t,{expected:s.joinValues(a),received:t.parsedType,code:_.invalid_type}),k}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),a=this._def.values;return T(t,{received:t.data,code:_.invalid_enum_value,options:a}),k}return b(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ek.create(e,{...this._def,...t})}exclude(e,t=this._def){return ek.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}ek.create=eE;class ex extends L{_parse(e){let t=s.getValidEnumValues(this._def.values),a=this._getOrReturnCtx(e);if(a.parsedType!==m.string&&a.parsedType!==m.number){let e=s.objectValues(t);return T(a,{expected:s.joinValues(e),received:a.parsedType,code:_.invalid_type}),k}if(this._cache||(this._cache=new Set(s.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=s.objectValues(t);return T(a,{received:a.data,code:_.invalid_enum_value,options:e}),k}return b(e.data)}get enum(){return this._def.values}}ex.create=(e,t)=>new ex({values:e,typeName:d.ZodNativeEnum,...R(t)});class eb extends L{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==m.promise&&!1===t.common.async?(T(t,{code:_.invalid_type,expected:m.promise,received:t.parsedType}),k):b((t.parsedType===m.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eb.create=(e,t)=>new eb({type:e,typeName:d.ZodPromise,...R(t)});class eN extends L{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===d.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:a}=this._processInputParams(e),r=this._def.effect||null,i={addIssue:e=>{T(a,e),e.fatal?t.abort():t.dirty()},get path(){return a.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===r.type){let e=r.transform(a.data,i);if(a.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return k;let r=await this._def.schema._parseAsync({data:e,path:a.path,parent:a});return"aborted"===r.status?k:"dirty"===r.status||"dirty"===t.value?x(r.value):r});{if("aborted"===t.value)return k;let r=this._def.schema._parseSync({data:e,path:a.path,parent:a});return"aborted"===r.status?k:"dirty"===r.status||"dirty"===t.value?x(r.value):r}}if("refinement"===r.type){let e=e=>{let t=r.refinement(e,i);if(a.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(a=>"aborted"===a.status?k:("dirty"===a.status&&t.dirty(),e(a.value).then(()=>({status:t.value,value:a.value}))));{let r=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===r.status?k:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}}if("transform"===r.type)if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(e=>O(e)?Promise.resolve(r.transform(e.value,i)).then(e=>({status:t.value,value:e})):k);else{let e=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});if(!O(e))return k;let s=r.transform(e.value,i);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}s.assertNever(r)}}eN.create=(e,t,a)=>new eN({schema:e,typeName:d.ZodEffects,effect:t,...R(a)}),eN.createWithPreprocess=(e,t,a)=>new eN({schema:t,effect:{type:"preprocess",transform:e},typeName:d.ZodEffects,...R(a)});class ew extends L{_parse(e){return this._getType(e)===m.undefined?b(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ew.create=(e,t)=>new ew({innerType:e,typeName:d.ZodOptional,...R(t)});class eO extends L{_parse(e){return this._getType(e)===m.null?b(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eO.create=(e,t)=>new eO({innerType:e,typeName:d.ZodNullable,...R(t)});class eA extends L{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return t.parsedType===m.undefined&&(a=this._def.defaultValue()),this._def.innerType._parse({data:a,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eA.create=(e,t)=>new eA({innerType:e,typeName:d.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...R(t)});class eC extends L{_parse(e){let{ctx:t}=this._processInputParams(e),a={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:a.data,path:a.path,parent:{...a}});return A(r)?r.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new y(a.common.issues)},input:a.data})})):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new y(a.common.issues)},input:a.data})}}removeCatch(){return this._def.innerType}}eC.create=(e,t)=>new eC({innerType:e,typeName:d.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...R(t)});class eS extends L{_parse(e){if(this._getType(e)!==m.nan){let t=this._getOrReturnCtx(e);return T(t,{code:_.invalid_type,expected:m.nan,received:t.parsedType}),k}return{status:"valid",value:e.data}}}eS.create=e=>new eS({typeName:d.ZodNaN,...R(e)}),Symbol("zod_brand");class eR extends L{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return this._def.type._parse({data:a,path:t.path,parent:t})}unwrap(){return this._def.type}}class eL extends L{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?k:"dirty"===e.status?(t.dirty(),x(e.value)):this._def.out._parseAsync({data:e.value,path:a.path,parent:a})})();{let e=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?k:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:a.path,parent:a})}}static create(e,t){return new eL({in:e,out:t,typeName:d.ZodPipeline})}}class eI extends L{_parse(e){let t=this._def.innerType._parse(e),a=e=>(O(e)&&(e.value=Object.freeze(e.value)),e);return A(t)?t.then(e=>a(e)):a(t)}unwrap(){return this._def.innerType}}eI.create=(e,t)=>new eI({innerType:e,typeName:d.ZodReadonly,...R(t)}),eo.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(d||(d={}));let eZ=H.create;G.create,eS.create,Q.create,J.create,ee.create,et.create,ea.create,er.create,es.create,ei.create,en.create,ed.create,eu.create;let ej=eo.create;eo.strictCreate,el.create,eh.create,ep.create,em.create,ef.create,e_.create,ey.create,eg.create,ev.create,eT.create;let eU=ek.create;ex.create,eb.create,eN.create,ew.create,eO.create,eN.createWithPreprocess,eL.create;let eF=!1,eP=ej({name:eZ().min(1,"Nama lengkap tidak boleh kosong"),email:eZ().email("Alamat email tidak valid"),password:eZ().min(8,"Password harus minimal 8 karakter"),role:eU(["creator","promoter"]).optional().default("promoter")});async function eM(e){try{eF||(await (0,p.wU)(),eF=!0);let t=await e.json(),a=eP.safeParse(t);if(!a.success)return h.NextResponse.json({error:a.error.errors[0].message},{status:400});let{name:r,email:s,password:i,role:n}=a.data;if(await p.QQ.findByEmail(s))return h.NextResponse.json({error:"Email sudah terdaftar"},{status:409});let d=await (0,p.Er)(i),u=await p.QQ.create({email:s,name:r,password_hash:d,role:n,bio:void 0,is_active:!0});return h.NextResponse.json({success:!0,message:"Akun berhasil dibuat. Silakan login.",user:{id:u.id,email:u.email,name:u.name,role:u.role}})}catch(e){return console.error("Registration error:",e),h.NextResponse.json({error:e.message||"Terjadi kesalahan saat mendaftar"},{status:500})}}let eD=new o.AppRouteRouteModule({definition:{kind:l.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\monetizr\\apps\\auth\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"export",userland:u}),{workAsyncStorage:e$,workUnitAsyncStorage:eX,serverHooks:ez}=eD;function eK(){return(0,c.patchFetch)({workAsyncStorage:e$,workUnitAsyncStorage:eX})}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},7032:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[191,744,692],()=>a(3946));module.exports=r})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[413],{2935:(e,a,t)=>{"use strict";t.d(a,{$n:()=>c,Zp:()=>b,Wu:()=>y,BT:()=>h,wL:()=>v,aR:()=>N,ZB:()=>g,pd:()=>f,JU:()=>x});var r=t(4568),s=t(7620),l=t(9649),d=t(615),o=t(2987),n=t(607);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,n.QP)((0,o.$)(a))}let m=(0,d.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,a)=>{let{className:t,variant:s,size:d,asChild:o=!1,...n}=e,c=o?l.DX:"button";return(0,r.jsx)(c,{className:i(m({variant:s,size:d,className:t})),ref:a,...n})});c.displayName="Button";let f=s.forwardRef((e,a)=>{let{className:t,type:s,...l}=e;return(0,r.jsx)("input",{type:s,className:i("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...l})});f.displayName="Input";var u=t(4762);let p=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),x=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(u.b,{ref:a,className:i(p(),t),...s})});x.displayName=u.b.displayName;let b=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});b.displayName="Card";let N=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:i("flex flex-col space-y-1.5 p-6",t),...s})});N.displayName="CardHeader";let g=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("h3",{ref:a,className:i("text-2xl font-semibold leading-none tracking-tight",t),...s})});g.displayName="CardTitle";let h=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("p",{ref:a,className:i("text-sm text-muted-foreground",t),...s})});h.displayName="CardDescription";let y=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:i("p-6 pt-0",t),...s})});y.displayName="CardContent";let v=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:i("flex items-center p-6 pt-0",t),...s})});v.displayName="CardFooter";var w=t(7167),j=t(7911),k=t(4931),R=t(1261);w.bL,w.l9,w.YJ,w.ZL,w.Pb,w.z6,s.forwardRef((e,a)=>{let{className:t,inset:s,children:l,...d}=e;return(0,r.jsxs)(w.ZP,{ref:a,className:i("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",t),...d,children:[l,(0,r.jsx)(j.A,{className:"ml-auto h-4 w-4"})]})}).displayName=w.ZP.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(w.G5,{ref:a,className:i("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...s})}).displayName=w.G5.displayName,s.forwardRef((e,a)=>{let{className:t,sideOffset:s=4,...l}=e;return(0,r.jsx)(w.ZL,{children:(0,r.jsx)(w.UC,{ref:a,sideOffset:s,className:i("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})})}).displayName=w.UC.displayName,s.forwardRef((e,a)=>{let{className:t,inset:s,...l}=e;return(0,r.jsx)(w.q7,{ref:a,className:i("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",t),...l})}).displayName=w.q7.displayName,s.forwardRef((e,a)=>{let{className:t,children:s,checked:l,...d}=e;return(0,r.jsxs)(w.H_,{ref:a,className:i("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:l,...d,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(w.VF,{children:(0,r.jsx)(k.A,{className:"h-4 w-4"})})}),s]})}).displayName=w.H_.displayName,s.forwardRef((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsxs)(w.hN,{ref:a,className:i("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(w.VF,{children:(0,r.jsx)(R.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=w.hN.displayName,s.forwardRef((e,a)=>{let{className:t,inset:s,...l}=e;return(0,r.jsx)(w.JU,{ref:a,className:i("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",t),...l})}).displayName=w.JU.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(w.wv,{ref:a,className:i("-mx-1 my-1 h-px bg-muted",t),...s})}).displayName=w.wv.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:i("w-full caption-bottom text-sm",t),...s})})}).displayName="Table",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("thead",{ref:a,className:i("[&_tr]:border-b",t),...s})}).displayName="TableHeader",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tbody",{ref:a,className:i("[&_tr:last-child]:border-0",t),...s})}).displayName="TableBody",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tfoot",{ref:a,className:i("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...s})}).displayName="TableFooter",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tr",{ref:a,className:i("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...s})}).displayName="TableRow",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("th",{ref:a,className:i("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...s})}).displayName="TableHead",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("td",{ref:a,className:i("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...s})}).displayName="TableCell",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("caption",{ref:a,className:i("mt-4 text-sm text-muted-foreground",t),...s})}).displayName="TableCaption"},5753:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>c});var r=t(4568),s=t(7261),l=t.n(s),d=t(1938),o=t(60),n=t(5006),i=t(2935);let m=n.Ik({email:n.Yj().email({message:"Alamat email tidak valid."})});function c(){let{register:e,handleSubmit:a,formState:{errors:t,isSubmitting:s}}=(0,d.mN)({resolver:(0,o.u)(m)}),n=async e=>{try{console.log("Mengirim link reset untuk:",e.email),alert("Jika email Anda terdaftar, Anda akan menerima link reset.")}catch(e){console.error(e),alert("Terjadi kesalahan. Coba lagi nanti.")}};return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900 p-4",children:(0,r.jsx)(i.Zp,{className:"w-full max-w-md",children:(0,r.jsxs)("form",{onSubmit:a(n),children:[(0,r.jsxs)(i.aR,{className:"space-y-1 text-center",children:[(0,r.jsx)(i.ZB,{className:"text-2xl font-bold",children:"Lupa Password?"}),(0,r.jsx)(i.BT,{children:"Masukkan email Anda, kami akan mengirimkan instruksi untuk reset."})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.JU,{htmlFor:"email",children:"Email"}),(0,r.jsx)(i.pd,{id:"email",type:"email",placeholder:"<EMAIL>",...e("email")}),t.email&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:t.email.message})]}),(0,r.jsx)(i.$n,{type:"submit",className:"w-full",disabled:s,children:s?"Mengirim...":"Kirim Link Reset"})]}),(0,r.jsx)(i.wL,{className:"text-sm text-center",children:(0,r.jsx)(l(),{href:"/auth/login",className:"underline",children:"Kembali ke Login"})})]})})})}},7357:(e,a,t)=>{Promise.resolve().then(t.bind(t,5753))}},e=>{var a=a=>e(e.s=a);e.O(0,[0,587,315,358],()=>a(7357)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[381],{991:(e,a,t)=>{Promise.resolve().then(t.bind(t,2894))},2894:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>m});var r=t(4568),s=t(7620),n=t(1938),l=t(60),d=t(5006),i=t(3735);let o=d.Ik({title:d.Yj().min(1,"Judul kampanye tidak boleh kosong"),description:d.Yj().min(10,"Deskripsi harus minimal 10 karakter"),budget:d.ai().min(1e4,"Budget minimal Rp 10.000"),pricePerView:d.ai().min(100,"Harga per tampilan minimal Rp 100"),requirements:d.Yj().optional(),materialUrl:d.Yj().url("URL materi tidak valid").optional()});function c(e){let{onSuccess:a}=e,[t,d]=(0,s.useState)(!1),{register:c,handleSubmit:m,formState:{errors:u},reset:p}=(0,n.mN)({resolver:(0,l.u)(o)}),f=async e=>{d(!0);try{let t=await fetch("/api/campaigns",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.message||"Gagal membuat kampanye")}alert("Kampanye berhasil dibuat!"),p(),null==a||a()}catch(e){alert(e.message||"Terjadi kesalahan")}finally{d(!1)}};return(0,r.jsxs)(i.Zp,{className:"w-full max-w-2xl",children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"Buat Kampanye Baru"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("form",{onSubmit:m(f),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.JU,{htmlFor:"title",children:"Judul Kampanye"}),(0,r.jsx)(i.pd,{id:"title",...c("title"),placeholder:"Masukkan judul kampanye"}),u.title&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:u.title.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.JU,{htmlFor:"description",children:"Deskripsi"}),(0,r.jsx)("textarea",{id:"description",...c("description"),placeholder:"Jelaskan kampanye Anda",className:"w-full min-h-[100px] px-3 py-2 border border-input rounded-md"}),u.description&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:u.description.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.JU,{htmlFor:"budget",children:"Budget (Rp)"}),(0,r.jsx)(i.pd,{id:"budget",type:"number",...c("budget",{valueAsNumber:!0}),placeholder:"100000"}),u.budget&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:u.budget.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.JU,{htmlFor:"pricePerView",children:"Harga per Tampilan (Rp)"}),(0,r.jsx)(i.pd,{id:"pricePerView",type:"number",...c("pricePerView",{valueAsNumber:!0}),placeholder:"150"}),u.pricePerView&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:u.pricePerView.message})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.JU,{htmlFor:"requirements",children:"Persyaratan (Opsional)"}),(0,r.jsx)("textarea",{id:"requirements",...c("requirements"),placeholder:"Persyaratan khusus untuk promotor",className:"w-full min-h-[80px] px-3 py-2 border border-input rounded-md"}),u.requirements&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:u.requirements.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.JU,{htmlFor:"materialUrl",children:"URL Materi (Opsional)"}),(0,r.jsx)(i.pd,{id:"materialUrl",...c("materialUrl"),placeholder:"https://drive.google.com/..."}),u.materialUrl&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:u.materialUrl.message})]}),(0,r.jsx)(i.$n,{type:"submit",className:"w-full",disabled:t,children:t?"Membuat...":"Buat Kampanye"})]})})]})}function m(){let[e,a]=(0,s.useState)([]),[t,n]=(0,s.useState)(!1),[l,d]=(0,s.useState)(!0),o=async()=>{try{let e=await fetch("/api/campaigns");if(e.ok){let t=await e.json();a(t.campaigns)}}catch(e){console.error("Failed to fetch campaigns:",e)}finally{d(!1)}};(0,s.useEffect)(()=>{o()},[]);let m=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),u=e=>new Date(e).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"});return l?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Kampanye"})}),(0,r.jsx)("div",{className:"text-center py-8",children:"Loading..."})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Kampanye"}),(0,r.jsx)(i.$n,{onClick:()=>n(!t),children:t?"Tutup Form":"Buat Kampanye Baru"})]}),t&&(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(c,{onSuccess:()=>{n(!1),o()}})}),(0,r.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:0===e.length?(0,r.jsx)("div",{className:"col-span-full text-center py-8 text-muted-foreground",children:"Belum ada kampanye. Buat kampanye pertama Anda!"}):e.map(e=>(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsx)(i.ZB,{className:"text-lg",children:e.title}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("active"===e.status?"bg-green-100 text-green-800":"draft"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:"active"===e.status?"Aktif":"draft"===e.status?"Draft":e.status})]})}),(0,r.jsxs)(i.Wu,{className:"space-y-3",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Budget:"}),(0,r.jsx)("span",{className:"font-medium",children:m(e.budget)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Per Tampilan:"}),(0,r.jsx)("span",{className:"font-medium",children:m(e.pricePerView)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Dibuat:"}),(0,r.jsx)("span",{children:u(e.created_at)})]})]}),(0,r.jsx)("div",{className:"pt-2",children:(0,r.jsx)(i.$n,{variant:"outline",size:"sm",className:"w-full",children:"Lihat Detail"})})]})]},e.id))})]})}},2985:(e,a,t)=>{"use strict";t.d(a,{cn:()=>n});var r=t(2987),s=t(607);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,s.QP)((0,r.$)(a))}},3482:(e,a,t)=>{"use strict";t.r(a),t.d(a,{DropdownMenu:()=>c,DropdownMenuCheckboxItem:()=>N,DropdownMenuContent:()=>g,DropdownMenuGroup:()=>u,DropdownMenuItem:()=>y,DropdownMenuLabel:()=>w,DropdownMenuPortal:()=>p,DropdownMenuRadioGroup:()=>x,DropdownMenuRadioItem:()=>j,DropdownMenuSeparator:()=>v,DropdownMenuShortcut:()=>k,DropdownMenuSub:()=>f,DropdownMenuSubContent:()=>b,DropdownMenuSubTrigger:()=>h,DropdownMenuTrigger:()=>m});var r=t(4568),s=t(7620),n=t(7167),l=t(7911),d=t(4931),i=t(1261),o=t(2985);let c=n.bL,m=n.l9,u=n.YJ,p=n.ZL,f=n.Pb,x=n.z6,h=s.forwardRef((e,a)=>{let{className:t,inset:s,children:d,...i}=e;return(0,r.jsxs)(n.ZP,{ref:a,className:(0,o.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",t),...i,children:[d,(0,r.jsx)(l.A,{className:"ml-auto h-4 w-4"})]})});h.displayName=n.ZP.displayName;let b=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.G5,{ref:a,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...s})});b.displayName=n.G5.displayName;let g=s.forwardRef((e,a)=>{let{className:t,sideOffset:s=4,...l}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.UC,{ref:a,sideOffset:s,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})})});g.displayName=n.UC.displayName;let y=s.forwardRef((e,a)=>{let{className:t,inset:s,...l}=e;return(0,r.jsx)(n.q7,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",t),...l})});y.displayName=n.q7.displayName;let N=s.forwardRef((e,a)=>{let{className:t,children:s,checked:l,...i}=e;return(0,r.jsxs)(n.H_,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:l,...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),s]})});N.displayName=n.H_.displayName;let j=s.forwardRef((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsxs)(n.hN,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(i.A,{className:"h-2 w-2 fill-current"})})}),s]})});j.displayName=n.hN.displayName;let w=s.forwardRef((e,a)=>{let{className:t,inset:s,...l}=e;return(0,r.jsx)(n.JU,{ref:a,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",t),...l})});w.displayName=n.JU.displayName;let v=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.wv,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...s})});v.displayName=n.wv.displayName;let k=e=>{let{className:a,...t}=e;return(0,r.jsx)("span",{className:(0,o.cn)("ml-auto text-xs tracking-widest opacity-60",a),...t})};k.displayName="DropdownMenuShortcut"},3735:(e,a,t)=>{"use strict";t.d(a,{$n:()=>o,Zp:()=>u,Wu:()=>h,BT:()=>x,wL:()=>b,aR:()=>p,ZB:()=>f,rI:()=>g.DropdownMenu,SQ:()=>g.DropdownMenuContent,_2:()=>g.DropdownMenuItem,lp:()=>g.DropdownMenuLabel,mB:()=>g.DropdownMenuSeparator,ty:()=>g.DropdownMenuTrigger,pd:()=>c,JU:()=>m.Label});var r=t(4568),s=t(7620),n=t(9649),l=t(615),d=t(2985);let i=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,a)=>{let{className:t,variant:s,size:l,asChild:o=!1,...c}=e,m=o?n.DX:"button";return(0,r.jsx)(m,{className:(0,d.cn)(i({variant:s,size:l,className:t})),ref:a,...c})});o.displayName="Button";let c=s.forwardRef((e,a)=>{let{className:t,type:s,...n}=e;return(0,r.jsx)("input",{type:s,className:(0,d.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...n})});c.displayName="Input";var m=t(9237);let u=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});u.displayName="Card";let p=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",t),...s})});p.displayName="CardHeader";let f=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("h3",{ref:a,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});f.displayName="CardTitle";let x=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("p",{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",t),...s})});x.displayName="CardDescription";let h=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,d.cn)("p-6 pt-0",t),...s})});h.displayName="CardContent";let b=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,d.cn)("flex items-center p-6 pt-0",t),...s})});b.displayName="CardFooter";var g=t(3482);s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:(0,d.cn)("w-full caption-bottom text-sm",t),...s})})}).displayName="Table",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("thead",{ref:a,className:(0,d.cn)("[&_tr]:border-b",t),...s})}).displayName="TableHeader",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tbody",{ref:a,className:(0,d.cn)("[&_tr:last-child]:border-0",t),...s})}).displayName="TableBody",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tfoot",{ref:a,className:(0,d.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...s})}).displayName="TableFooter",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tr",{ref:a,className:(0,d.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...s})}).displayName="TableRow",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("th",{ref:a,className:(0,d.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...s})}).displayName="TableHead",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("td",{ref:a,className:(0,d.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...s})}).displayName="TableCell",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("caption",{ref:a,className:(0,d.cn)("mt-4 text-sm text-muted-foreground",t),...s})}).displayName="TableCaption"},9237:(e,a,t)=>{"use strict";t.r(a),t.d(a,{Label:()=>o});var r=t(4568),s=t(7620),n=t(4762),l=t(615),d=t(2985);let i=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.b,{ref:a,className:(0,d.cn)(i(),t),...s})});o.displayName=n.b.displayName}},e=>{var a=a=>e(e.s=a);e.O(0,[688,415,587,315,358],()=>a(991)),_N_E=e.O()}]);
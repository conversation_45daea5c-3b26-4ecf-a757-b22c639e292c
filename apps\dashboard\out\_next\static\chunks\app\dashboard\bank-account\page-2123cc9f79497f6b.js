(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[506],{1186:(e,a,t)=>{Promise.resolve().then(t.bind(t,6118))},2985:(e,a,t)=>{"use strict";t.d(a,{cn:()=>s});var r=t(2987),n=t(607);function s(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,n.QP)((0,r.$)(a))}},3482:(e,a,t)=>{"use strict";t.r(a),t.d(a,{DropdownMenu:()=>c,DropdownMenuCheckboxItem:()=>y,DropdownMenuContent:()=>h,DropdownMenuGroup:()=>u,DropdownMenuItem:()=>g,DropdownMenuLabel:()=>j,DropdownMenuPortal:()=>f,DropdownMenuRadioGroup:()=>x,DropdownMenuRadioItem:()=>w,DropdownMenuSeparator:()=>v,DropdownMenuShortcut:()=>k,DropdownMenuSub:()=>p,DropdownMenuSubContent:()=>N,DropdownMenuSubTrigger:()=>b,DropdownMenuTrigger:()=>m});var r=t(4568),n=t(7620),s=t(7167),o=t(7911),d=t(4931),l=t(1261),i=t(2985);let c=s.bL,m=s.l9,u=s.YJ,f=s.ZL,p=s.Pb,x=s.z6,b=n.forwardRef((e,a)=>{let{className:t,inset:n,children:d,...l}=e;return(0,r.jsxs)(s.ZP,{ref:a,className:(0,i.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",n&&"pl-8",t),...l,children:[d,(0,r.jsx)(o.A,{className:"ml-auto h-4 w-4"})]})});b.displayName=s.ZP.displayName;let N=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)(s.G5,{ref:a,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})});N.displayName=s.G5.displayName;let h=n.forwardRef((e,a)=>{let{className:t,sideOffset:n=4,...o}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{ref:a,sideOffset:n,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...o})})});h.displayName=s.UC.displayName;let g=n.forwardRef((e,a)=>{let{className:t,inset:n,...o}=e;return(0,r.jsx)(s.q7,{ref:a,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n&&"pl-8",t),...o})});g.displayName=s.q7.displayName;let y=n.forwardRef((e,a)=>{let{className:t,children:n,checked:o,...l}=e;return(0,r.jsxs)(s.H_,{ref:a,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:o,...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),n]})});y.displayName=s.H_.displayName;let w=n.forwardRef((e,a)=>{let{className:t,children:n,...o}=e;return(0,r.jsxs)(s.hN,{ref:a,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...o,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),n]})});w.displayName=s.hN.displayName;let j=n.forwardRef((e,a)=>{let{className:t,inset:n,...o}=e;return(0,r.jsx)(s.JU,{ref:a,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",n&&"pl-8",t),...o})});j.displayName=s.JU.displayName;let v=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)(s.wv,{ref:a,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",t),...n})});v.displayName=s.wv.displayName;let k=e=>{let{className:a,...t}=e;return(0,r.jsx)("span",{className:(0,i.cn)("ml-auto text-xs tracking-widest opacity-60",a),...t})};k.displayName="DropdownMenuShortcut"},3735:(e,a,t)=>{"use strict";t.d(a,{$n:()=>i,Zp:()=>u,Wu:()=>b,BT:()=>x,wL:()=>N,aR:()=>f,ZB:()=>p,rI:()=>h.DropdownMenu,SQ:()=>h.DropdownMenuContent,_2:()=>h.DropdownMenuItem,lp:()=>h.DropdownMenuLabel,mB:()=>h.DropdownMenuSeparator,ty:()=>h.DropdownMenuTrigger,pd:()=>c,JU:()=>m.Label});var r=t(4568),n=t(7620),s=t(9649),o=t(615),d=t(2985);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=n.forwardRef((e,a)=>{let{className:t,variant:n,size:o,asChild:i=!1,...c}=e,m=i?s.DX:"button";return(0,r.jsx)(m,{className:(0,d.cn)(l({variant:n,size:o,className:t})),ref:a,...c})});i.displayName="Button";let c=n.forwardRef((e,a)=>{let{className:t,type:n,...s}=e;return(0,r.jsx)("input",{type:n,className:(0,d.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...s})});c.displayName="Input";var m=t(9237);let u=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:a,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});u.displayName="Card";let f=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:a,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",t),...n})});f.displayName="CardHeader";let p=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("h3",{ref:a,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});p.displayName="CardTitle";let x=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("p",{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",t),...n})});x.displayName="CardDescription";let b=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:a,className:(0,d.cn)("p-6 pt-0",t),...n})});b.displayName="CardContent";let N=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:a,className:(0,d.cn)("flex items-center p-6 pt-0",t),...n})});N.displayName="CardFooter";var h=t(3482);n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:(0,d.cn)("w-full caption-bottom text-sm",t),...n})})}).displayName="Table",n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("thead",{ref:a,className:(0,d.cn)("[&_tr]:border-b",t),...n})}).displayName="TableHeader",n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("tbody",{ref:a,className:(0,d.cn)("[&_tr:last-child]:border-0",t),...n})}).displayName="TableBody",n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("tfoot",{ref:a,className:(0,d.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...n})}).displayName="TableFooter",n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("tr",{ref:a,className:(0,d.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...n})}).displayName="TableRow",n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("th",{ref:a,className:(0,d.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...n})}).displayName="TableHead",n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("td",{ref:a,className:(0,d.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...n})}).displayName="TableCell",n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("caption",{ref:a,className:(0,d.cn)("mt-4 text-sm text-muted-foreground",t),...n})}).displayName="TableCaption"},6118:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i});var r=t(4568),n=t(1938),s=t(60),o=t(5006),d=t(3735);let l=o.Ik({bankName:o.Yj().min(1,{message:"Nama bank tidak boleh kosong."}),accountHolderName:o.Yj().min(1,{message:"Nama pemilik rekening tidak boleh kosong."}),accountNumber:o.Yj().min(1,{message:"Nomor rekening tidak boleh kosong."})});function i(){let{register:e,handleSubmit:a,formState:{errors:t,isSubmitting:o}}=(0,n.mN)({resolver:(0,s.u)(l)}),i=async e=>{try{if(!(await fetch("/api/user/bank-account",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Gagal menyimpan rekening bank");alert("Rekening bank berhasil disimpan!")}catch(e){console.error(e),alert("Terjadi kesalahan saat menyimpan rekening bank")}};return(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)(d.Zp,{className:"w-full max-w-md",children:(0,r.jsxs)("form",{onSubmit:a(i),children:[(0,r.jsxs)(d.aR,{children:[(0,r.jsx)(d.ZB,{children:"Informasi Rekening Bank"}),(0,r.jsx)(d.BT,{children:"Masukkan detail rekening bank Anda untuk menerima pembayaran."})]}),(0,r.jsxs)(d.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.JU,{htmlFor:"bankName",children:"Nama Bank"}),(0,r.jsx)(d.pd,{id:"bankName",placeholder:"Contoh: Bank Central Asia",...e("bankName")}),t.bankName&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:t.bankName.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.JU,{htmlFor:"accountHolderName",children:"Nama Pemilik Rekening"}),(0,r.jsx)(d.pd,{id:"accountHolderName",placeholder:"Nama sesuai buku tabungan",...e("accountHolderName")}),t.accountHolderName&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:t.accountHolderName.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.JU,{htmlFor:"accountNumber",children:"Nomor Rekening"}),(0,r.jsx)(d.pd,{id:"accountNumber",placeholder:"xxxxxxxxxx",...e("accountNumber")}),t.accountNumber&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:t.accountNumber.message})]})]}),(0,r.jsx)(d.wL,{children:(0,r.jsx)(d.$n,{type:"submit",disabled:o,children:o?"Menyimpan...":"Simpan Rekening"})})]})})})}},9237:(e,a,t)=>{"use strict";t.r(a),t.d(a,{Label:()=>i});var r=t(4568),n=t(7620),s=t(4762),o=t(615),d=t(2985);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)(s.b,{ref:a,className:(0,d.cn)(l(),t),...n})});i.displayName=s.b.displayName}},e=>{var a=a=>e(e.s=a);e.O(0,[688,415,587,315,358],()=>a(1186)),_N_E=e.O()}]);